# 知乎Playwright适配器使用指南

## 🎯 概述

知乎Playwright适配器是TextUp项目的核心组件，使用Playwright Web自动化技术实现知乎平台的文章自动发布功能。由于知乎没有公开的发布API，本适配器通过模拟真实用户操作来实现自动化发布。

### 特性

- ✅ **Web自动化发布** - 基于Playwright的稳定自动化
- ✅ **反检测机制** - 内置多层反检测策略
- ✅ **会话管理** - 自动保存和恢复登录状态
- ✅ **人性化操作** - 模拟真实用户行为模式
- ✅ **错误恢复** - 智能错误处理和重试机制
- ✅ **格式转换** - 支持Markdown到HTML的自动转换
- ✅ **批量发布** - 支持多文章批量处理

## 📋 安装要求

### 系统要求

- **Python**: 3.8+
- **操作系统**: Windows 10+, macOS 10.15+, Linux
- **内存**: 建议4GB以上
- **磁盘空间**: 至少500MB（浏览器文件）

### 依赖安装

#### 1. 快速安装

```bash
# 进入项目目录
cd uploader/textup

# 运行安装脚本
python scripts/install_playwright.py

# 或者手动安装
pip install playwright beautifulsoup4 markdown2
playwright install chromium
```

#### 2. 详细安装步骤

```bash
# 1. 安装Python依赖
pip install playwright>=1.40.0
pip install beautifulsoup4>=4.12.0
pip install markdown2>=2.4.0

# 2. 安装浏览器（仅需要chromium）
playwright install chromium

# 3. 安装系统依赖（Linux系统）
playwright install-deps
```

#### 3. 验证安装

```bash
# 检查安装状态
python scripts/install_playwright.py --check-only

# 运行测试
python test_playwright.py
```

## ⚙️ 配置

### 1. 认证凭证配置

#### 方式一：环境变量（推荐）

```bash
# 设置环境变量
export ZHIHU_USERNAME="<EMAIL>"
export ZHIHU_PASSWORD="your_password"
export ZHIHU_HEADLESS="true"  # 无头模式
export ZHIHU_DEBUG="false"    # 调试模式
```

#### 方式二：配置文件

创建 `zhihu_credentials.json`:

```json
{
    "username": "<EMAIL>",
    "password": "your_password",
    "headless": true,
    "debug": false
}
```

#### 方式三：代码中配置

```python
credentials = {
    "username": "<EMAIL>",
    "password": "your_password",
    "headless": True,
    "debug": False
}
```

### 2. 适配器参数配置

```python
adapter_config = {
    "timeout": 60,           # 请求超时时间（秒）
    "max_retries": 2,        # 最大重试次数
    "retry_delay": 5.0,      # 重试延迟（秒）
    "rate_limit_calls": 10,  # 频率限制次数
    "rate_limit_period": 3600, # 频率限制周期（秒）
    "headless": True,        # 无头模式
    "debug": False           # 调试模式
}
```

## 🚀 使用方法

### 1. 基本用法

```python
import asyncio
from textup.adapters import ZhihuPlaywrightAdapter
from textup.models import TransformedContent, ContentFormat

async def publish_article():
    # 创建适配器
    async with ZhihuPlaywrightAdapter() as adapter:
        # 认证
        credentials = {
            "username": "<EMAIL>",
            "password": "your_password"
        }
        
        auth_result = await adapter.authenticate(credentials)
        if not auth_result.success:
            print(f"认证失败: {auth_result.error_message}")
            return
        
        # 准备内容
        content = TransformedContent(
            title="我的第一篇自动发布文章",
            content="# 标题\n\n这是文章内容...",
            content_format=ContentFormat.MARKDOWN,
            tags=["Python", "自动化", "知乎"]
        )
        
        # 发布文章
        result = await adapter.publish(content, {})
        
        if result.success:
            print(f"发布成功: {result.platform_url}")
        else:
            print(f"发布失败: {result.error_message}")

# 运行
asyncio.run(publish_article())
```

### 2. 使用工厂模式

```python
from textup.adapters import create_adapter

async def publish_with_factory():
    # 使用工厂创建适配器
    adapter = create_adapter(
        platform='zhihu',
        adapter_type='playwright',  # 指定使用Playwright
        headless=True,
        debug=False
    )
    
    # 其余代码同上...
```

### 3. 批量发布

```python
from pathlib import Path
import frontmatter

async def batch_publish():
    async with ZhihuPlaywrightAdapter() as adapter:
        # 认证
        await adapter.authenticate(credentials)
        
        # 批量处理Markdown文件
        articles_dir = Path("articles")
        for md_file in articles_dir.glob("*.md"):
            # 读取文件
            with open(md_file, 'r', encoding='utf-8') as f:
                post = frontmatter.load(f)
            
            # 创建内容对象
            content = TransformedContent(
                title=post.metadata.get('title', md_file.stem),
                content=post.content,
                content_format=ContentFormat.MARKDOWN,
                tags=post.metadata.get('tags', [])
            )
            
            # 发布
            result = await adapter.publish(content, {})
            print(f"{md_file.name}: {'✅' if result.success else '❌'}")
            
            # 发布间隔
            await asyncio.sleep(30)
```

## 🔐 认证流程

### 1. 自动认证流程

```python
async def authenticate_example():
    adapter = ZhihuPlaywrightAdapter()
    
    credentials = {
        "username": "<EMAIL>",  # 支持邮箱
        "password": "your_password"
    }
    
    # 执行认证
    auth_result = await adapter.authenticate(credentials)
    
    if auth_result.success:
        print("认证成功，会话已保存")
    else:
        print(f"认证失败: {auth_result.error_message}")
```

### 2. 处理验证码和安全检查

当遇到验证码或安全检查时，适配器会：

1. **自动暂停** - 等待手动处理
2. **控制台提示** - 显示需要处理的验证类型
3. **等待继续** - 处理完成后按回车继续

```python
# 示例：处理验证码
# 控制台会显示：
# "检测到验证码，需要手动处理"
# "请手动完成验证码验证，然后按回车继续..."
```

### 3. 会话管理

- **自动保存** - 登录成功后自动保存cookies
- **自动恢复** - 下次使用时自动加载会话
- **过期检测** - 自动检测会话有效期（7天）
- **安全存储** - 用户名哈希化存储

## 📝 内容发布

### 1. 支持的内容格式

#### Markdown格式（推荐）

```python
content = TransformedContent(
    title="技术分享：Python自动化",
    content="""
# Python自动化最佳实践

## 1. 工具选择

- **Playwright**: 现代化的Web自动化
- **Requests**: HTTP请求处理
- **BeautifulSoup**: HTML解析

## 2. 代码示例

```python
import asyncio
from playwright.async_api import async_playwright

async def main():
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        # 你的代码...
```

## 3. 最佳实践

> 始终遵循目标网站的robots.txt和使用条款
    """,
    content_format=ContentFormat.MARKDOWN,
    tags=["Python", "自动化", "Playwright"],
    metadata={
        "category": "技术分享",
        "is_private": False
    }
)
```

#### HTML格式

```python
content = TransformedContent(
    title="HTML格式文章",
    content="""
    <h1>HTML格式示例</h1>
    <p>这是一段普通文字。</p>
    <blockquote>这是引用内容</blockquote>
    <pre><code>print("Hello, World!")</code></pre>
    """,
    content_format=ContentFormat.HTML,
    tags=["HTML", "格式化"]
)
```

### 2. 内容验证

适配器会自动验证内容：

```python
# 验证规则
validation_rules = {
    "title": {
        "required": True,
        "max_length": 100,
        "min_length": 1
    },
    "content": {
        "required": True,
        "min_length": 20
    },
    "tags": {
        "max_count": 5
    }
}
```

### 3. 发布选项

```python
publish_options = {
    "is_private": False,      # 是否私密发布
    "allow_comments": True,   # 是否允许评论
    "schedule_time": None     # 定时发布（暂未实现）
}

result = await adapter.publish(content, publish_options)
```

## 🛡️ 反检测机制

### 1. 浏览器指纹伪装

- **User-Agent随机化** - 随机选择真实的浏览器标识
- **Viewport设置** - 模拟常见的屏幕分辨率
- **时区和语言** - 设置合理的地理位置信息
- **WebDriver属性隐藏** - 移除自动化检测标识

### 2. 人性化行为模拟

```python
class AntiDetectionSystem:
    async def simulate_human_behavior(self):
        # 随机鼠标移动
        await self._random_mouse_movement()
        
        # 随机滚动
        await self._random_scrolling()
        
        # 随机停留
        await self._random_pause()
```

### 3. 频率控制

- **请求间隔** - 自动控制操作频率
- **随机延迟** - 每个操作之间随机等待
- **会话保持** - 避免频繁登录
- **错误重试** - 智能重试机制

## 🔧 故障排查

### 1. 常见问题

#### 登录失败

```python
# 问题：用户名或密码错误
解决：检查credentials配置，确保用户名和密码正确

# 问题：需要验证码
解决：手动完成验证码验证，或设置debug=True观察页面

# 问题：账号被限制
解决：降低操作频率，检查账号状态
```

#### 发布失败

```python
# 问题：内容格式不符合要求
解决：检查标题长度、内容长度等格式要求

# 问题：网络连接问题
解决：检查网络连接，增加timeout时间

# 问题：页面元素定位失败
解决：检查知乎页面是否有更新，可能需要更新选择器
```

#### 浏览器问题

```python
# 问题：浏览器启动失败
解决：重新安装浏览器 playwright install chromium

# 问题：系统依赖缺失（Linux）
解决：安装系统依赖 playwright install-deps

# 问题：权限问题
解决：检查文件权限，或使用虚拟环境
```

### 2. 调试模式

```python
# 开启调试模式
adapter = ZhihuPlaywrightAdapter(
    headless=False,  # 显示浏览器
    debug=True       # 详细日志
)
```

### 3. 日志分析

```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 查看详细执行过程
```

## 📊 性能优化

### 1. 资源使用优化

```python
# 优化配置
optimized_config = {
    "headless": True,           # 无头模式节省资源
    "timeout": 30,              # 合理的超时时间
    "max_retries": 1,           # 减少重试次数
    "rate_limit_calls": 5,      # 严格的频率限制
}
```

### 2. 批量发布优化

```python
async def optimized_batch_publish():
    async with ZhihuPlaywrightAdapter() as adapter:
        await adapter.authenticate(credentials)
        
        for article in articles:
            # 发布
            result = await adapter.publish(article, {})
            
            # 适当的间隔时间
            if result.success:
                await asyncio.sleep(60)  # 成功后等待1分钟
            else:
                await asyncio.sleep(300) # 失败后等待5分钟
```

## ⚖️ 使用规范

### 1. 法律合规

- 遵守知乎平台使用条款
- 不发布违法违规内容
- 尊重知识产权
- 合理使用自动化工具

### 2. 技术规范

- 控制发布频率（建议每小时不超过5篇）
- 避免短时间内大量操作
- 定期检查账号状态
- 及时更新适配器版本

### 3. 内容质量

- 确保内容原创性和质量
- 合理使用标签
- 提供有价值的信息
- 遵循社区规范

## 🔗 API参考

### ZhihuPlaywrightAdapter

```python
class ZhihuPlaywrightAdapter(BaseAdapter):
    """知乎Playwright适配器"""
    
    def __init__(self, **kwargs):
        """
        初始化适配器
        
        Args:
            timeout (int): 请求超时时间
            max_retries (int): 最大重试次数
            retry_delay (float): 重试延迟
            rate_limit_calls (int): 频率限制次数
            rate_limit_period (int): 频率限制周期
            headless (bool): 是否无头模式
            debug (bool): 是否调试模式
        """
    
    async def authenticate(self, credentials: Dict[str, Any]) -> AuthResult:
        """
        平台认证
        
        Args:
            credentials: 认证凭证
                - username: 用户名（邮箱或手机号）
                - password: 密码
                - headless: 是否无头模式
                - debug: 是否调试模式
        
        Returns:
            AuthResult: 认证结果
        """
    
    async def publish(self, content: TransformedContent, options: Dict[str, Any]) -> PublishResult:
        """
        发布内容
        
        Args:
            content: 转换后的内容
            options: 发布选项
        
        Returns:
            PublishResult: 发布结果
        """
    
    async def validate_format(self, content: TransformedContent) -> ValidationResult:
        """
        验证内容格式
        
        Args:
            content: 待验证的内容
        
        Returns:
            ValidationResult: 验证结果
        """
    
    async def get_publish_status(self, platform_post_id: str) -> Dict[str, Any]:
        """
        获取发布状态
        
        Args:
            platform_post_id: 平台文章ID
        
        Returns:
            Dict: 状态信息
        """
```

### 工厂函数

```python
def create_adapter(platform: str, adapter_type: str = "auto", **kwargs) -> BaseAdapter:
    """
    创建适配器
    
    Args:
        platform: 平台名称 ('zhihu')
        adapter_type: 适配器类型 ('playwright', 'auto')
        **kwargs: 适配器参数
    
    Returns:
        BaseAdapter: 适配器实例
    """

def get_recommended_adapter_type(platform: str) -> str:
    """
    获取推荐的适配器类型
    
    Args:
        platform: 平台名称
    
    Returns:
        str: 推荐的适配器类型
    """
```

## 🎓 学习资源

- [Playwright官方文档](https://playwright.dev/python/)
- [知乎平台规则](https://www.zhihu.com/term)
- [TextUp项目文档](../README.md)
- [示例代码](../examples/zhihu_playwright_example.py)

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个适配器！

### 开发环境设置

```bash
# 克隆项目
git clone https://github.com/your-repo/textup.git
cd textup

# 安装开发依赖
pip install -e ".[dev]"

# 运行测试
pytest tests/adapters/test_zhihu_playwright.py -v
```

### 提交规范

- 遵循PEP 8代码规范
- 添加适当的测试用例
- 更新相关文档
- 提供详细的提交信息

---

**免责声明**: 本适配器仅供学习和研究使用，使用时请遵守知乎平台的使用条款和相关法律法规。开发者不承担因使用本工具而产生的任何法律责任。