#!/usr/bin/env python3
"""
知乎Playwright适配器使用示例

本示例展示如何使用ZhihuPlaywrightAdapter进行知乎文章的自动化发布。
包括认证、内容准备、发布操作的完整流程。

依赖安装：
pip install playwright beautifulsoup4 markdown2

浏览器安装：
playwright install chromium

注意事项：
1. 使用前请确保遵守知乎平台规则
2. 避免过于频繁的操作，以免触发反爬机制
3. 建议在测试环境中先进行调试
"""

import asyncio
import os
from pathlib import Path
from typing import Dict, Any

# TextUp相关导入
from textup.adapters import ZhihuPlaywrightAdapter, create_adapter
from textup.models import TransformedContent, ContentFormat, Platform


class ZhihuPublishExample:
    """知乎发布示例类"""

    def __init__(self):
        self.adapter = None
        self.credentials = self._load_credentials()

    def _load_credentials(self) -> Dict[str, Any]:
        """加载认证信息"""
        # 方式1: 从环境变量加载（推荐）
        username = os.getenv('ZHIHU_USERNAME')
        password = os.getenv('ZHIHU_PASSWORD')

        if username and password:
            return {
                'username': username,
                'password': password,
                'headless': os.getenv('ZHIHU_HEADLESS', 'true').lower() == 'true',
                'debug': os.getenv('ZHIHU_DEBUG', 'false').lower() == 'true'
            }

        # 方式2: 从配置文件加载
        config_file = Path('zhihu_credentials.json')
        if config_file.exists():
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)

        # 方式3: 手动输入
        print("请输入知乎登录信息：")
        return {
            'username': input("用户名(邮箱/手机号): "),
            'password': input("密码: "),
            'headless': input("无头模式? (y/N): ").lower().startswith('y'),
            'debug': input("调试模式? (y/N): ").lower().startswith('y')
        }

    async def initialize(self):
        """初始化适配器"""
        try:
            # 方式1: 使用工厂函数创建（推荐）
            self.adapter = create_adapter(
                platform='zhihu',
                adapter_type='playwright',  # 指定使用Playwright适配器
                **self.credentials
            )

            # 方式2: 直接创建适配器实例
            # self.adapter = ZhihuPlaywrightAdapter(**self.credentials)

            print("适配器初始化成功")
            return True

        except Exception as e:
            print(f"适配器初始化失败: {e}")
            return False

    async def authenticate(self) -> bool:
        """执行认证"""
        try:
            if not self.adapter:
                print("适配器未初始化")
                return False

            print("开始认证...")
            auth_result = await self.adapter.authenticate(self.credentials)

            if auth_result.success:
                print("认证成功！")
                return True
            else:
                print(f"认证失败: {auth_result.error_message}")
                return False

        except Exception as e:
            print(f"认证过程出错: {e}")
            return False

    def create_sample_content(self) -> TransformedContent:
        """创建示例内容"""
        content = """# Playwright自动化发布测试

## 简介

这是一篇通过Playwright自动化工具发布的测试文章。

## 技术栈

- **Python**: 编程语言
- **Playwright**: Web自动化框架
- **TextUp**: 多平台发布工具

## 功能特点

1. **自动化发布**: 支持批量发布到多个平台
2. **反检测机制**: 模拟人类操作行为
3. **会话管理**: 自动保存和恢复登录状态

## 代码示例

```python
# 使用TextUp发布文章
async with ZhihuPlaywrightAdapter() as adapter:
    await adapter.authenticate(credentials)
    result = await adapter.publish(content, options)
    print(f"发布结果: {result}")
```

## 注意事项

> 请合理使用自动化工具，遵守平台规则，避免过度频繁操作。

## 总结

通过Playwright技术，我们可以实现稳定可靠的知乎内容自动化发布。

---
*本文由TextUp自动发布工具生成*
"""

        return TransformedContent(
            title="Playwright自动化发布测试文章",
            content=content,
            html="",
            text="",
            images=[],
            links=[],
            content_format=ContentFormat.MARKDOWN,
            tags=["Python", "自动化", "Playwright", "知乎"],
            metrics=None,
            metadata={
                "author": "TextUp Bot",
                "category": "技术分享",
                "is_private": False  # 设置为True则私密发布
            }
        )

    async def publish_article(self, content: TransformedContent) -> bool:
        """发布文章"""
        try:
            print("开始发布文章...")

            # 验证内容格式
            validation_result = await self.adapter.validate_format(content)
            if not validation_result.is_valid:
                print("内容格式验证失败:")
                for error in validation_result.errors:
                    print(f"  - {error.field}: {error.message}")
                return False

            print("内容格式验证通过")

            # 执行发布
            publish_result = await self.adapter.publish(content, {})

            if publish_result.success:
                print("文章发布成功！")
                print(f"平台文章ID: {publish_result.platform_post_id}")
                print(f"文章链接: {publish_result.publish_url}")
                return True
            else:
                print(f"文章发布失败: {publish_result.error_message}")
                return False

        except Exception as e:
            print(f"发布过程出错: {e}")
            return False

    async def check_publish_status(self, post_id: str):
        """检查发布状态"""
        try:
            print(f"检查文章状态: {post_id}")
            status = await self.adapter.get_publish_status(post_id)
            print(f"文章状态: {status}")

        except Exception as e:
            print(f"状态检查失败: {e}")

    async def batch_publish_from_directory(self, directory_path: str):
        """批量发布目录中的文章"""
        try:
            directory = Path(directory_path)
            if not directory.exists():
                print(f"目录不存在: {directory_path}")
                return

            markdown_files = list(directory.glob("*.md"))
            if not markdown_files:
                print("目录中没有找到Markdown文件")
                return

            print(f"找到 {len(markdown_files)} 个文件待发布")

            for file_path in markdown_files:
                print(f"\n处理文件: {file_path.name}")

                try:
                    # 读取文件内容
                    with open(file_path, 'r', encoding='utf-8') as f:
                        file_content = f.read()

                    # 解析frontmatter
                    import frontmatter
                    post = frontmatter.loads(file_content)

                    # 创建内容对象
                    content = TransformedContent(
                        title=post.metadata.get('title', file_path.stem),
                        content=post.content,
                        html="",
                        text="",
                        images=[],
                        links=[],
                        content_format=ContentFormat.MARKDOWN,
                        tags=post.metadata.get('tags', []),
                        metrics=None,
                        metadata=post.metadata
                    )

                    # 发布文章
                    success = await self.publish_article(content)

                    if success:
                        print(f"✅ {file_path.name} 发布成功")
                        # 可以将已发布的文件移动到已完成目录
                        # published_dir = directory / 'published'
                        # published_dir.mkdir(exist_ok=True)
                        # file_path.rename(published_dir / file_path.name)
                    else:
                        print(f"❌ {file_path.name} 发布失败")

                    # 发布间隔，避免操作过于频繁
                    print("等待30秒后继续下一篇...")
                    await asyncio.sleep(30)

                except Exception as e:
                    print(f"❌ 处理文件 {file_path.name} 失败: {e}")
                    continue

            print("\n批量发布完成！")

        except Exception as e:
            print(f"批量发布失败: {e}")

    async def cleanup(self):
        """清理资源"""
        if self.adapter and hasattr(self.adapter, 'close'):
            await self.adapter.close()
            print("适配器已关闭")


async def main():
    """主函数 - 单篇文章发布示例"""
    example = ZhihuPublishExample()

    try:
        # 1. 初始化适配器
        if not await example.initialize():
            return

        # 2. 执行认证
        if not await example.authenticate():
            return

        # 3. 准备内容
        content = example.create_sample_content()
        print(f"准备发布文章: {content.title}")

        # 4. 发布文章
        success = await example.publish_article(content)

        if success:
            print("🎉 发布流程完成！")
        else:
            print("😞 发布失败")

    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        # 5. 清理资源
        await example.cleanup()


async def batch_publish_example():
    """批量发布示例"""
    example = ZhihuPublishExample()

    try:
        # 初始化和认证
        if not await example.initialize():
            return
        if not await example.authenticate():
            return

        # 批量发布
        articles_dir = input("请输入文章目录路径: ").strip()
        if articles_dir:
            await example.batch_publish_from_directory(articles_dir)

    except Exception as e:
        print(f"批量发布出错: {e}")
    finally:
        await example.cleanup()


def create_sample_markdown_file():
    """创建示例Markdown文件"""
    content = """---
title: "我的第一篇自动发布文章"
tags: ["Python", "自动化", "知乎"]
category: "技术分享"
is_private: false
---

# 我的第一篇自动发布文章

这是一篇通过TextUp工具自动发布的文章。

## 内容介绍

使用Playwright技术实现知乎文章的自动化发布。

## 技术特点

1. 支持Markdown格式
2. 自动处理图片上传
3. 智能反检测机制

## 结语

感谢使用TextUp工具！
"""

    with open('sample_article.md', 'w', encoding='utf-8') as f:
        f.write(content)

    print("示例文件 'sample_article.md' 已创建")


if __name__ == "__main__":
    print("知乎Playwright适配器使用示例")
    print("=" * 40)

    choice = input("""
请选择操作:
1. 发布单篇文章
2. 批量发布文章
3. 创建示例文件
请输入选择 (1-3): """).strip()

    if choice == '1':
        asyncio.run(main())
    elif choice == '2':
        asyncio.run(batch_publish_example())
    elif choice == '3':
        create_sample_markdown_file()
    else:
        print("无效选择")
