# 知乎Playwright适配器 - TextUp项目

## 📖 概述

知乎Playwright适配器是TextUp项目的核心组件之一，专门用于实现知乎平台的文章自动化发布功能。由于知乎官方不提供公开的发布API，本适配器采用基于Playwright的Web自动化技术来模拟真实用户操作。

### 🎯 核心特性

- ✅ **自动化发布** - 支持Markdown/HTML格式的文章自动发布
- ✅ **智能反检测** - 内置多层反检测机制，模拟真实用户行为
- ✅ **会话管理** - 自动保存和恢复登录状态，减少重复认证
- ✅ **批量操作** - 支持批量文章发布和内容管理
- ✅ **错误恢复** - 智能错误处理和自动重试机制
- ✅ **格式转换** - 自动处理Markdown到HTML的格式转换
- ✅ **标签管理** - 自动添加和管理文章标签
- ✅ **状态监控** - 实时监控发布状态和文章访问情况

### 🔧 技术架构

```
ZhihuPlaywrightAdapter
├── BrowserManager      # 浏览器管理和反检测
├── AuthManager        # 认证和会话管理
├── ContentPublisher   # 内容发布核心逻辑
├── AntiDetectionSystem # 反检测行为模拟
└── ErrorHandler       # 错误处理和恢复
```

## 📋 系统要求

### 基础要求
- **Python**: 3.8 或更高版本
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **内存**: 建议 4GB 以上
- **磁盘空间**: 至少 1GB（包含浏览器文件）

### 网络要求
- 稳定的互联网连接
- 能够访问知乎网站
- 建议使用代理以提高稳定性（可选）

## 🚀 快速开始

### 1. 安装依赖

#### 自动安装（推荐）
```bash
# 进入项目目录
cd uploader/textup

# 运行安装脚本
python scripts/install_playwright.py
```

#### 手动安装
```bash
# 安装Python依赖
pip install playwright>=1.40.0 beautifulsoup4>=4.12.0 markdown2>=2.4.0

# 安装浏览器
playwright install chromium

# Linux系统需要安装系统依赖
playwright install-deps  # 仅Linux需要
```

### 2. 验证安装
```bash
# 检查安装状态
python scripts/install_playwright.py --check-only

# 运行快速测试
python test_zhihu_playwright_quick.py
```

### 3. 配置认证信息

#### 方式一：环境变量（推荐）
```bash
export ZHIHU_USERNAME="<EMAIL>"
export ZHIHU_PASSWORD="your_password"
export ZHIHU_HEADLESS="true"
```

#### 方式二：配置文件
创建 `zhihu_credentials.json`:
```json
{
    "username": "<EMAIL>",
    "password": "your_password",
    "headless": true,
    "debug": false
}
```

### 4. 运行第一个示例
```bash
python docs/examples/zhihu_playwright_example.py
```

## 💻 使用方法

### 基本使用

```python
import asyncio
from textup.adapters import ZhihuPlaywrightAdapter
from textup.models import TransformedContent, ContentFormat

async def publish_article():
    # 创建适配器实例
    async with ZhihuPlaywrightAdapter() as adapter:
        # 配置认证信息
        credentials = {
            "username": "<EMAIL>",
            "password": "your_password",
            "headless": True  # 无头模式，不显示浏览器界面
        }
        
        # 执行认证
        auth_result = await adapter.authenticate(credentials)
        if not auth_result.success:
            print(f"认证失败: {auth_result.error_message}")
            return
        
        # 准备文章内容
        content = TransformedContent(
            title="我的第一篇自动发布文章",
            content="""
# 使用TextUp自动发布文章

## 简介
这是一篇通过TextUp工具自动发布的文章示例。

## 特性
- 支持Markdown格式
- 自动格式转换
- 智能反检测

## 总结
TextUp让内容发布变得更加简单高效！
            """,
            html="",
            text="",
            images=[],
            links=[],
            content_format=ContentFormat.MARKDOWN,
            tags=["TextUp", "自动化", "Python"],
            metrics=None
        )
        
        # 发布文章
        result = await adapter.publish(content, {})
        
        if result.success:
            print(f"✅ 发布成功!")
            print(f"📝 文章ID: {result.platform_post_id}")
            print(f"🔗 文章链接: {result.publish_url}")
        else:
            print(f"❌ 发布失败: {result.error_message}")

# 运行示例
asyncio.run(publish_article())
```

### 使用工厂模式

```python
from textup.adapters import create_adapter

# 使用工厂函数创建适配器
adapter = create_adapter(
    platform='zhihu',
    adapter_type='playwright',
    headless=True,
    debug=False
)
```

### 批量发布

```python
import asyncio
from pathlib import Path
import frontmatter

async def batch_publish():
    async with ZhihuPlaywrightAdapter() as adapter:
        # 认证
        await adapter.authenticate(credentials)
        
        # 批量处理文章
        articles_dir = Path("articles")
        
        for md_file in articles_dir.glob("*.md"):
            print(f"📄 处理文件: {md_file.name}")
            
            # 读取Markdown文件
            with open(md_file, 'r', encoding='utf-8') as f:
                post = frontmatter.load(f)
            
            # 创建内容对象
            content = TransformedContent(
                title=post.metadata.get('title', md_file.stem),
                content=post.content,
                html="",
                text="",
                images=[],
                links=[],
                content_format=ContentFormat.MARKDOWN,
                tags=post.metadata.get('tags', []),
                metrics=None,
                metadata=post.metadata
            )
            
            # 发布文章
            result = await adapter.publish(content, {})
            
            if result.success:
                print(f"✅ {md_file.name} 发布成功")
            else:
                print(f"❌ {md_file.name} 发布失败: {result.error_message}")
            
            # 发布间隔，避免频率过高
            print("⏳ 等待30秒后继续...")
            await asyncio.sleep(30)

asyncio.run(batch_publish())
```

### 创建示例文章

创建一个 `article.md` 文件：
```markdown
---
title: "Python自动化最佳实践"
tags: ["Python", "自动化", "最佳实践"]
category: "技术分享"
---

# Python自动化最佳实践

## 1. 工具选择

在Python自动化领域，选择合适的工具至关重要：

- **Playwright**: 现代化的Web自动化框架
- **Requests**: HTTP请求处理
- **BeautifulSoup**: HTML解析和处理

## 2. 代码示例

```python
import asyncio
from playwright.async_api import async_playwright

async def automate_task():
    async with async_playwright() as p:
        browser = await p.chromium.launch()
        page = await browser.new_page()
        # 你的自动化逻辑
        await browser.close()

asyncio.run(automate_task())
```

## 3. 最佳实践

> 记住：始终遵循目标网站的robots.txt和使用条款

1. **频率控制**: 避免过于频繁的请求
2. **错误处理**: 实现完善的异常处理
3. **用户体验**: 模拟真实用户行为

## 结语

Python自动化能够极大提升工作效率，但需要负责任地使用。
```

## ⚙️ 配置选项

### 适配器配置

```python
adapter_config = {
    # 基础设置
    "timeout": 60,           # 请求超时时间（秒）
    "max_retries": 2,        # 最大重试次数
    "retry_delay": 5.0,      # 重试延迟（秒）
    
    # 频率限制
    "rate_limit_calls": 10,  # 每小时最大请求数
    "rate_limit_period": 3600, # 频率限制周期（秒）
    
    # 浏览器设置
    "headless": True,        # 无头模式
    "debug": False,          # 调试模式
    "slow_mo": 0,           # 操作延迟（毫秒）
    
    # 安全设置
    "user_agent": None,      # 自定义User-Agent
    "proxy": None,           # 代理设置
}

adapter = ZhihuPlaywrightAdapter(**adapter_config)
```

### 发布选项

```python
publish_options = {
    "is_private": False,      # 是否私密发布
    "allow_comments": True,   # 是否允许评论
    "schedule_time": None,    # 定时发布（暂未实现）
    "auto_tags": True,        # 是否自动建议标签
}

result = await adapter.publish(content, publish_options)
```

## 🔧 故障排查

### 常见问题

#### 1. 登录失败
**问题**: 认证过程失败
```
❌ 认证失败: 登录失败，请检查用户名和密码
```

**解决方案**:
- 检查用户名和密码是否正确
- 确认账号未被限制或冻结
- 尝试手动登录知乎网站验证
- 检查网络连接是否正常

#### 2. 验证码问题
**问题**: 登录时遇到验证码
```
需要验证码，请手动处理
```

**解决方案**:
- 设置 `debug=True` 查看浏览器界面
- 手动完成验证码验证
- 降低操作频率，避免触发安全检查
- 考虑使用代理IP

#### 3. 发布失败
**问题**: 文章发布不成功
```
❌ 发布失败: 内容格式验证失败
```

**解决方案**:
```python
# 检查内容格式
validation = await adapter.validate_format(content)
if not validation.is_valid:
    for error in validation.errors:
        print(f"格式错误: {error.field} - {error.message}")
```

#### 4. 浏览器启动失败
**问题**: Playwright浏览器无法启动
```
Error: Browser binary not found
```

**解决方案**:
```bash
# 重新安装浏览器
playwright install chromium

# Linux系统安装依赖
playwright install-deps
```

### 调试模式

启用调试模式查看详细执行过程：

```python
adapter = ZhihuPlaywrightAdapter(
    headless=False,  # 显示浏览器界面
    debug=True,      # 启用详细日志
    slow_mo=100      # 减慢操作速度便于观察
)
```

### 日志配置

```python
import logging

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('zhihu_playwright.log'),
        logging.StreamHandler()
    ]
)
```

## 📊 性能优化

### 减少资源占用

```python
# 优化配置
optimized_adapter = ZhihuPlaywrightAdapter(
    headless=True,           # 无头模式节省资源
    timeout=30,              # 较短的超时时间
    max_retries=1,           # 减少重试次数
    rate_limit_calls=5,      # 严格的频率限制
)
```

### 批量发布优化

```python
async def optimized_batch():
    async with ZhihuPlaywrightAdapter() as adapter:
        await adapter.authenticate(credentials)
        
        for article in articles:
            result = await adapter.publish(article, {})
            
            # 动态调整间隔时间
            if result.success:
                await asyncio.sleep(60)   # 成功后等待1分钟
            else:
                await asyncio.sleep(300)  # 失败后等待5分钟
```

## ⚖️ 使用规范

### 法律合规

1. **遵守平台规则**: 严格遵守知乎平台的使用条款和社区规范
2. **内容原创性**: 确保发布的内容具有原创性，避免侵权
3. **合理使用**: 适度使用自动化工具，避免对平台造成负担
4. **数据保护**: 妥善保管账号信息，避免泄露

### 技术规范

1. **发布频率**: 
   - 建议每小时发布不超过5篇文章
   - 文章之间间隔至少10分钟
   - 避免在短时间内大量操作

2. **内容质量**:
   - 确保文章内容有价值且符合平台要求
   - 合理使用标签，不要添加无关标签
   - 文章长度适中，内容结构清晰

3. **账号安全**:
   - 定期更换密码
   - 启用两步验证（如果支持）
   - 监控账号异常活动

### 最佳实践

```python
# 推荐的安全配置
safe_config = {
    "timeout": 45,
    "max_retries": 1,
    "retry_delay": 10.0,
    "rate_limit_calls": 3,      # 非常保守的频率限制
    "rate_limit_period": 3600,
    "headless": True,
    "debug": False
}
```

## 🧪 测试

### 运行测试套件

```bash
# 快速功能测试
python test_zhihu_playwright_quick.py

# 完整单元测试
pytest tests/adapters/test_zhihu_playwright.py -v

# 集成测试（需要真实环境）
pytest tests/adapters/test_zhihu_playwright.py::TestZhihuPlaywrightAdapterIntegration -v
```

### 创建自定义测试

```python
import asyncio
from textup.adapters import ZhihuPlaywrightAdapter

async def custom_test():
    adapter = ZhihuPlaywrightAdapter()
    
    # 测试凭证验证
    creds = {"username": "<EMAIL>", "password": "test123"}
    result = adapter._validate_credentials(creds)
    print(f"凭证验证: {'✅' if result.is_valid else '❌'}")
    
    # 测试内容验证
    from textup.models import TransformedContent, ContentFormat
    content = TransformedContent(
        title="测试文章",
        content="测试内容，确保长度足够。",
        html="", text="", images=[], links=[],
        content_format=ContentFormat.MARKDOWN,
        tags=["测试"], metrics=None
    )
    
    result = adapter._validate_format_impl(content)
    print(f"内容验证: {'✅' if result.is_valid else '❌'}")

asyncio.run(custom_test())
```

## 📚 API文档

### ZhihuPlaywrightAdapter 类

#### 构造函数
```python
def __init__(self, **kwargs):
    """
    初始化知乎Playwright适配器
    
    参数:
        timeout (int): 请求超时时间，默认60秒
        max_retries (int): 最大重试次数，默认2次
        retry_delay (float): 重试延迟，默认5.0秒
        rate_limit_calls (int): 频率限制次数，默认10次/小时
        rate_limit_period (int): 频率限制周期，默认3600秒
        headless (bool): 是否无头模式，默认True
        debug (bool): 是否调试模式，默认False
    """
```

#### 主要方法

```python
async def authenticate(self, credentials: Dict[str, Any]) -> AuthResult:
    """
    执行平台认证
    
    参数:
        credentials: 认证凭证字典
            - username: 用户名（邮箱或手机号）
            - password: 密码
            - headless: 是否无头模式（可选）
            - debug: 是否调试模式（可选）
    
    返回:
        AuthResult: 认证结果对象
    """

async def publish(self, content: TransformedContent, options: Dict[str, Any]) -> PublishResult:
    """
    发布内容到知乎平台
    
    参数:
        content: 转换后的内容对象
        options: 发布选项字典
    
    返回:
        PublishResult: 发布结果对象
    """

async def validate_format(self, content: TransformedContent) -> ValidationResult:
    """
    验证内容格式是否符合平台要求
    
    参数:
        content: 待验证的内容对象
    
    返回:
        ValidationResult: 验证结果对象
    """

async def get_publish_status(self, platform_post_id: str) -> Dict[str, Any]:
    """
    获取文章发布状态
    
    参数:
        platform_post_id: 平台文章ID
    
    返回:
        Dict: 包含状态信息的字典
    """
```

### 工厂函数

```python
def create_adapter(platform: str, adapter_type: str = "auto", **kwargs) -> BaseAdapter:
    """
    创建适配器实例
    
    参数:
        platform: 平台名称（'zhihu'）
        adapter_type: 适配器类型（'playwright' 或 'auto'）
        **kwargs: 适配器初始化参数
    
    返回:
        BaseAdapter: 适配器实例
    """
```

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 开发环境设置

```bash
# 克隆项目
git clone <repository_url>
cd textup

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装开发依赖
pip install -e ".[dev]"
pip install playwright pytest pytest-asyncio black isort flake8

# 安装Playwright浏览器
playwright install chromium
```

### 提交规范

1. **代码风格**: 遵循 PEP 8 规范
2. **测试覆盖**: 为新功能添加测试用例
3. **文档更新**: 更新相关文档和注释
4. **提交信息**: 使用清晰的提交信息

```bash
# 代码格式化
black src/textup/adapters/zhihu_playwright.py
isort src/textup/adapters/zhihu_playwright.py

# 代码检查
flake8 src/textup/adapters/zhihu_playwright.py

# 运行测试
pytest tests/adapters/test_zhihu_playwright.py
```

### 问题反馈

- 🐛 **Bug报告**: 使用GitHub Issues报告问题
- 💡 **功能请求**: 提出新功能建议
- 📖 **文档改进**: 帮助改进文档质量
- 🔧 **代码贡献**: 提交Pull Request

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## ⚠️ 免责声明

本工具仅供学习和研究使用。使用者需要：

1. 遵守知乎平台的使用条款和相关法律法规
2. 对使用本工具的后果承担全部责任
3. 确保发布的内容合法合规
4. 尊重知识产权和他人权益

开发者不承担因使用本工具而产生的任何直接或间接损失。

## 🔗 相关链接

- [TextUp项目主页](README.md)
- [Playwright官方文档](https://playwright.dev/python/)
- [知乎官方网站](https://www.zhihu.com/)
- [Python异步编程指南](https://docs.python.org/3/library/asyncio.html)

---

**最后更新**: 2024年1月

如有问题或建议，请通过GitHub Issues联系我们。感谢使用TextUp知乎Playwright适配器！ 🚀