# 通用项目AI自动化工作流生成器

**文档版本**: v1.0  
**创建时间**: 2025-09-02  
**适用范围**: 任何软件开发项目  
**使用目标**: 一键生成AI自动化执行工作流文档  

---

## 🎯 使用方法

### 步骤1: 复制提示词模板
复制下方的**完整提示词模板**到您的AI对话中

### 步骤2: 项目信息填入
将模板中的`{项目相关信息}`替换为您的项目实际情况

### 步骤3: 执行生成
AI会自动分析您的项目并生成完整的自动化工作流文档

### 步骤4: 开始自动执行
使用生成的工作流文档，只需说"请按照这个文档继续工作"

---

## 📋 完整提示词模板

### 基础模板 (直接复制使用)

```
我需要你为我的项目创建一个AI自动化工作流文档。请按照以下要求进行：

## 项目背景信息
**项目名称**: {填写项目名称}
**项目类型**: {Web应用/CLI工具/库/API服务/移动应用/其他}
**技术栈**: {填写主要技术栈，如Python/JavaScript/Java/Go等}
**项目路径**: {填写项目根目录路径}
**当前阶段**: {开发中/测试中/部署准备/维护中}

## 当前项目状态
**代码完成度**: {估算百分比，如80%}
**测试状况**: {测试覆盖率，测试类型，通过情况}
**文档状况**: {文档完整度，需要补充的部分}
**已知问题**: {列出当前主要问题和技术债务}
**部署状况**: {部署配置，环境准备情况}

## 项目目标和计划
**短期目标**: {1-3天内要完成的目标}
**质量标准**: {代码质量，测试覆盖率，性能等要求}
**交付时间**: {预期完成时间}
**优先级**: {功能优先级，质量要求等}

## 项目结构信息
{请提供项目的主要目录结构，如：
```
src/
├── main/          # 主代码
├── test/          # 测试代码
├── docs/          # 文档
└── config/        # 配置文件
```
}

## 已有文档
{如果有相关文档，请简要说明或提供链接}

---

请根据以上信息，创建一个完整的AI自动化工作流文档，包含以下核心特性：

1. **自动状态检测**: 包含具体的命令和脚本，能自动检测项目当前状态
2. **条件执行逻辑**: "如果...则执行"的逻辑，AI能根据状态自动选择任务
3. **进度跟踪系统**: checkbox和状态标记，AI能自动更新进度
4. **任务分解**: 将目标分解为可执行的具体任务
5. **质量保证**: 包含代码质量、测试、文档的检查机制
6. **错误处理**: 自动重试和问题升级机制
7. **汇报机制**: 标准化的进度汇报格式

最终生成的文档应该让我能够简单地对AI说"请按照这个文档继续工作"，AI就能完全自动化地执行剩余任务并更新文档进度。

请开始生成工作流文档。
```

---

## 🔧 项目类型特定模板

### Web应用项目模板

```
## Web应用项目特定信息
**前端技术**: {React/Vue/Angular/Vanilla JS}
**后端技术**: {Node.js/Python/Java/PHP}
**数据库**: {MySQL/PostgreSQL/MongoDB/Redis}
**部署方式**: {Docker/云服务/传统服务器}
**API设计**: {RESTful/GraphQL/RPC}

## Web应用特定检测项
- [ ] 前端构建是否正常
- [ ] 后端服务是否可启动
- [ ] 数据库连接是否正常
- [ ] API接口是否可访问
- [ ] 前后端集成是否正常
- [ ] 静态资源是否正确加载
```

### CLI工具项目模板

```
## CLI工具项目特定信息
**命令结构**: {主命令和子命令列表}
**配置方式**: {配置文件/环境变量/命令行参数}
**依赖管理**: {包管理器和依赖文件}
**安装方式**: {pip/npm/homebrew/手动安装}
**平台支持**: {Windows/macOS/Linux}

## CLI工具特定检测项
- [ ] 命令行工具是否可正常安装
- [ ] 所有命令是否可正常执行
- [ ] 帮助信息是否完整
- [ ] 配置文件是否正确解析
- [ ] 错误处理是否用户友好
```

### 库/框架项目模板

```
## 库项目特定信息
**包名称**: {包的名称}
**API设计**: {公开接口列表}
**版本管理**: {语义化版本}
**发布平台**: {PyPI/npm/Maven/NuGet}
**示例代码**: {是否有完整示例}

## 库项目特定检测项
- [ ] 公开API是否稳定
- [ ] 文档是否与API匹配
- [ ] 示例代码是否可运行
- [ ] 包是否可正确安装
- [ ] 版本管理是否规范
```

---

## 🎨 自定义扩展模板

### 添加自定义检测项

```
## 自定义检测项模板
**检测名称**: {检测项描述}
**检测命令**: {具体的检测命令或脚本}
**成功条件**: {什么情况下认为检测成功}
**失败处理**: {检测失败时的处理方案}

示例：
**检测名称**: 代码格式化检查
**检测命令**: `npm run lint` 或 `black --check src/`
**成功条件**: 命令返回码为0，无格式问题
**失败处理**: 自动运行格式化命令修复
```

### 添加自定义任务模板

```
## 自定义任务模板
**任务名称**: {任务描述}
**前置条件**: {执行此任务需要满足的条件}
**执行步骤**: {具体执行步骤}
**验证方法**: {如何验证任务完成}
**回滚方案**: {任务失败时的回滚方案}

示例：
**任务名称**: 数据库迁移
**前置条件**: 数据库连接正常，备份完成
**执行步骤**: 1. 运行迁移脚本 2. 验证数据完整性
**验证方法**: 查询关键表数据，确认结构正确
**回滚方案**: 恢复数据库备份
```

---

## 📊 质量标准模板

### 通用质量标准

```
## 代码质量标准
- [ ] 代码格式化: 通过自动格式化检查
- [ ] 代码规范: 通过Linter检查  
- [ ] 类型检查: 通过静态类型检查
- [ ] 安全检查: 通过安全漏洞扫描
- [ ] 性能检查: 关键路径性能满足要求

## 测试质量标准
- [ ] 单元测试: 覆盖率 ≥ {设定目标，如80%}
- [ ] 集成测试: 主要流程覆盖完整
- [ ] 端到端测试: 用户场景验证完整
- [ ] 性能测试: 响应时间满足要求
- [ ] 兼容性测试: 目标平台/浏览器兼容

## 文档质量标准  
- [ ] API文档: 所有公开接口有文档
- [ ] 用户文档: 安装和使用指南完整
- [ ] 开发文档: 架构和贡献指南清晰
- [ ] 示例代码: 主要使用场景有示例
- [ ] 变更日志: 版本变更记录完整
```

---

## 🚀 快速启动示例

### Python项目示例

```
我需要你为我的项目创建一个AI自动化工作流文档。请按照以下要求进行：

## 项目背景信息
**项目名称**: DataAnalyzer
**项目类型**: Python库
**技术栈**: Python 3.9+, pandas, numpy, pytest
**项目路径**: /home/<USER>/projects/data-analyzer
**当前阶段**: 开发中

## 当前项目状态
**代码完成度**: 75%
**测试状况**: 单元测试覆盖率45%，缺少集成测试
**文档状况**: API文档30%完成，用户指南缺失
**已知问题**: 性能优化待完成，错误处理不够完善
**部署状况**: 打包配置完成，发布流程待建立

## 项目目标和计划
**短期目标**: 3天内完成开发，测试覆盖率达到80%，发布v1.0
**质量标准**: 测试覆盖率≥80%，文档完整，通过所有质量检查
**交付时间**: 2025-09-05
**优先级**: 功能完整性 > 性能优化 > 文档完善

## 项目结构信息
```
src/data_analyzer/
├── core/              # 核心功能模块
├── utils/             # 工具函数
├── tests/             # 测试代码
├── docs/              # 文档
└── examples/          # 示例代码
```

## 已有文档
- README.md (基础介绍)
- docs/api.md (部分API文档)

请根据以上信息生成完整的AI自动化工作流文档。
```

### JavaScript项目示例

```
我需要你为我的项目创建一个AI自动化工作流文档。请按照以下要求进行：

## 项目背景信息
**项目名称**: TaskManager
**项目类型**: Web应用
**技术栈**: React, Node.js, MongoDB, Express
**项目路径**: /Users/<USER>/projects/task-manager
**当前阶段**: 测试中

## 当前项目状态
**代码完成度**: 90%
**测试状况**: 前端测试60%，后端测试40%，E2E测试缺失
**文档状况**: API文档70%，用户手册50%
**已知问题**: 数据库性能优化，前端响应式设计完善
**部署状况**: Docker配置完成，CI/CD待建立

## 项目目标和计划
**短期目标**: 2天内完成测试，部署到生产环境
**质量标准**: 测试覆盖率≥75%，性能测试通过
**交付时间**: 2025-09-04
**优先级**: 功能稳定性 > 用户体验 > 性能优化

## 项目结构信息
```
frontend/              # React前端
backend/               # Node.js后端
database/              # 数据库脚本
tests/                 # 测试代码
docs/                  # 文档
docker/                # Docker配置
```

请根据以上信息生成完整的AI自动化工作流文档。
```

---

## 🔄 模板自定义指南

### 添加项目特定检测

1. **识别关键检测点**: 您的项目成功的关键指标是什么？
2. **编写检测命令**: 如何用命令行自动检测这些指标？
3. **定义成功条件**: 什么情况下认为检测通过？
4. **设计失败处理**: 检测失败时AI应该如何处理？

### 自定义任务优先级

```
## 任务优先级模板
**P0 - 阻塞任务**: 不完成无法继续的任务
**P1 - 高优先级**: 影响核心功能的任务  
**P2 - 中优先级**: 影响用户体验的任务
**P3 - 低优先级**: 优化和增强的任务

示例：
P0: 修复编译错误、解决阻塞性bug
P1: 核心功能实现、关键测试用例
P2: 用户界面优化、文档完善
P3: 性能优化、代码重构
```

---

## ✅ 使用成功标准

使用这个模板生成的工作流文档应该能够：

1. **✅ 自动状态检测**: AI能准确判断项目当前状态
2. **✅ 智能任务选择**: 只执行必要的未完成任务
3. **✅ 自动进度更新**: 任务完成后自动更新文档
4. **✅ 质量保证**: 确保交付满足设定的质量标准
5. **✅ 用户友好**: 只需"请按照文档继续工作"即可启动

---

## 📞 使用支持

### 常见问题

**Q: 如何为复杂项目使用这个模板？**  
A: 将复杂项目分解为多个模块，每个模块使用一个工作流文档

**Q: 生成的工作流可以修改吗？**  
A: 可以！生成后您可以根据实际情况调整任务和检测逻辑

**Q: 如何处理多技术栈项目？**  
A: 在技术栈字段中列出所有技术，AI会自动适配相应的检测和任务

### 技术支持

如果您在使用过程中遇到问题：
1. 检查项目信息是否填写完整
2. 确认技术栈和项目类型匹配
3. 验证项目路径和结构信息正确
4. 必要时可以要求AI重新生成特定部分

---

**使用保证**: 通过这个模板，您可以为任何项目快速生成高质量的AI自动化工作流文档，实现真正的"一句话项目管理"！

**模板状态**: ✅ 生产就绪，立即可用  
**支持项目类型**: Web应用、CLI工具、库/框架、API服务、移动应用等  
**预期效果**: 5分钟生成文档，AI自动执行剩余开发工作