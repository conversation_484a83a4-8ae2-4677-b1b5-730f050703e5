# TextUp 开发测试指南 🛠️

> **专为开发阶段设计** - 使用 `uv run python` 方式直接运行源码，方便代码修改和调试

## 📋 目录

- [开发测试目标](#开发测试目标)
- [环境准备](#环境准备)
- [源码运行方式](#源码运行方式)
- [日志配置和监控](#日志配置和监控)
- [创建测试内容](#创建测试内容)
- [功能测试流程](#功能测试流程)
- [调试和故障排除](#调试和故障排除)
- [自动化测试](#自动化测试)
- [开发最佳实践](#开发最佳实践)
- [相关资源](#相关资源)

---

## 🎯 开发测试目标

本指南将帮助开发者：
- ✅ 快速搭建开发环境
- ✅ 使用源码直接运行程序，无需安装
- ✅ 通过日志实时查看程序运行状态
- ✅ 完整测试所有平台功能
- ✅ 快速调试和修改代码

**适用场景**: 代码开发、功能调试、快速测试

---

## � 环境准备

### 前置要求

- Python 3.9 或更高版本
- uv 包管理器
- Git（用于克隆项目）

### 1. 项目设置

```bash
# 进入项目根目录
cd textup  # 替换为你的项目路径

# 确认项目结构
ls -la src/textup/
# 应该看到 __init__.py, cli/, services/ 等目录
```

### 2. 创建开发环境

```bash
# 创建虚拟环境
uv venv --python 3.9

# 激活虚拟环境
source .venv/bin/activate  # Linux/macOS
# 或 .venv\Scripts\activate  # Windows

# 安装依赖（开发模式）
uv pip install -e .

# 验证安装
python --version
which python
```

### 3. 开发配置

```bash
# 创建开发专用目录
mkdir -p dev-config dev-logs dev-data test-content

# 设置环境变量（可选）
export TEXTUP_CONFIG_DIR="./dev-config"
export TEXTUP_LOG_LEVEL="DEBUG"

# 验证环境
python -c "import textup; print('✅ TextUp 模块导入成功')"
```

## 🚀 源码运行方式

### 基础运行方法

推荐使用 `uv run python -m textup.cli.main` 方式运行：

```bash
# 查看帮助
uv run python -m textup.cli.main --help

# 查看版本
uv run python -m textup.cli.main --version

# 启用调试模式
uv run python -m textup.cli.main --debug <command>
```

### 常用开发命令

```bash
# 配置管理
uv run python -m textup.cli.main config --list
uv run python -m textup.cli.main config --set key=value
uv run python -m textup.cli.main config --interactive

# 认证管理
uv run python -m textup.cli.main auth --status
uv run python -m textup.cli.main auth --interactive --platform weibo

# 内容发布测试
uv run python -m textup.cli.main publish --file article.md --platform weibo --dry-run
uv run python -m textup.cli.main publish --interactive
```

## 📝 日志配置和监控

### 启用调试日志

```bash
# 设置日志级别
export TEXTUP_LOG_LEVEL="DEBUG"

# 或在运行时启用调试
uv run python -m textup.cli.main --debug <command>
```

### 实时监控日志

```bash
# 查看主日志文件
tail -f dev-logs/textup-dev.log

# 查看错误日志
tail -f dev-logs/textup-errors.log

# 带颜色的日志监控
tail -f dev-logs/textup-dev.log | grep --color=always -E "(ERROR|WARNING|INFO|DEBUG)"
```

### 日志记录示例

```bash
# 记录配置测试日志
uv run python -m textup.cli.main --debug config --list 2>&1 | tee dev-logs/config-test.log

# 记录发布测试日志
uv run python -m textup.cli.main --debug publish --file test.md --platform weibo --dry-run 2>&1 | tee dev-logs/publish-test.log
```

## 🧪 创建测试内容

### 创建测试目录

```bash
# 创建测试内容目录
mkdir -p test-content test-results
```

### 基础测试文章

创建一个简单的测试文章：

```bash
cat > test-content/basic-test.md << 'EOF'
# TextUp 基础功能测试

## 测试目的
验证 TextUp 的基本发布功能是否正常工作。

## 测试内容

### 文本格式测试
- **粗体文本**
- *斜体文本*
- `行内代码`

### 列表测试
1. 第一项
2. 第二项
3. 第三项

### 代码块测试
```python
def hello_textup():
    print("Hello from TextUp!")
    return True
```

### 链接测试
[TextUp GitHub](https://github.com/textup-team/textup)

## 标签
#TextUp #测试 #开发
EOF
```

### 平台特定测试文章

创建针对不同平台的测试文章：

```bash
# 微博测试文章（短内容）
cat > test-content/weibo-test.md << 'EOF'
# 微博发布测试 🚀

TextUp 微博发布功能测试中...

✅ 支持 Markdown 转换
✅ 支持 Emoji 表情
✅ 支持话题标签

#微博测试 #TextUp #自动化发布
EOF

# 知乎测试文章（长内容）
cat > test-content/zhihu-test.md << 'EOF'
# 知乎专栏：TextUp 多平台发布工具解析

## 引言
在自媒体时代，内容创作者面临着如何高效地将优质内容分发到多个平台的挑战。TextUp 应运而生，为这个问题提供了完美的解决方案。

## 核心功能特性

### 1. 多平台支持
- 知乎：专栏文章发布
- 微博：长微博和普通微博
- 今日头条：文章和微头条

### 2. 智能格式转换
不同平台对内容格式的要求各不相同，TextUp 提供智能转换功能。

### 3. 认证安全管理
采用 OAuth 2.0 标准，确保账号安全。

## 结论
TextUp 不仅是一个发布工具，更是内容创作者的效率加速器。

#知乎 #TextUp #多平台发布 #内容创作工具
EOF
```

## 🔧 功能测试流程

### 配置功能测试

```bash
# 测试配置管理
uv run python -m textup.cli.main --debug config --list
uv run python -m textup.cli.main --debug config --set app.test_mode=true
uv run python -m textup.cli.main --debug config --get app.test_mode
```

### 平台功能测试

#### 微博平台测试

```bash
# 配置微博平台
uv run python -m textup.cli.main --debug config --set platforms.weibo.client_id="test_weibo_key"
uv run python -m textup.cli.main --debug config --set platforms.weibo.client_secret="test_weibo_secret"

# 测试认证状态
uv run python -m textup.cli.main --debug auth --status --platform weibo

# 测试内容验证和预览
uv run python -m textup.cli.main --debug validate --file test-content/weibo-test.md --platform weibo
uv run python -m textup.cli.main --debug preview --file test-content/weibo-test.md --platform weibo

# 测试模拟发布
uv run python -m textup.cli.main --debug publish --file test-content/weibo-test.md --platform weibo --dry-run
```

#### 知乎平台测试

```bash
# 配置知乎平台
uv run python -m textup.cli.main --debug config --set platforms.zhihu.client_id="test_zhihu_id"
uv run python -m textup.cli.main --debug config --set platforms.zhihu.client_secret="test_zhihu_secret"

# 测试知乎功能
uv run python -m textup.cli.main --debug auth --status --platform zhihu
uv run python -m textup.cli.main --debug validate --file test-content/zhihu-test.md --platform zhihu
uv run python -m textup.cli.main --debug publish --file test-content/zhihu-test.md --platform zhihu --dry-run
```

### 多平台批量测试

```bash
# 多平台配置检查
uv run python -m textup.cli.main --debug config --get platforms
uv run python -m textup.cli.main --debug auth --status

# 单文章多平台发布测试
uv run python -m textup.cli.main --debug publish --file test-content/basic-test.md --platforms weibo,zhihu --dry-run

# 批量文件发布测试
uv run python -m textup.cli.main --debug publish --directory test-content --platforms weibo,zhihu --dry-run
```

## �️ 调试和故障排除

### 常见问题排查

```bash
# 检查环境状态
python --version
which python
python -c "import textup; print('✅ TextUp 模块导入成功')"

# 检查配置
uv run python -m textup.cli.main config --list

# 检查日志
tail -20 dev-logs/textup-dev.log
```

### 调试技巧

```bash
# 启用详细调试
uv run python -m textup.cli.main --debug <command>

# 记录调试日志
uv run python -m textup.cli.main --debug <command> 2>&1 | tee debug.log

# 检查特定模块
python -c "from textup.cli import main; print('CLI 模块正常')"
python -c "from textup.services import config_manager; print('配置模块正常')"
```

## 🚀 自动化测试

### 快速测试脚本

创建一个快速测试脚本来验证基本功能：

```bash
#!/bin/bash
# quick-test.sh

echo "🚀 TextUp 快速测试"

# 1. 语法检查
echo "1. 检查语法..."
python -m py_compile src/textup/cli/main.py
if [ $? -ne 0 ]; then
    echo "❌ 语法错误"
    exit 1
fi

# 2. 导入测试
echo "2. 检查导入..."
python -c "import sys; sys.path.append('src'); import textup.cli.main" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 导入失败"
    exit 1
fi

# 3. 基础功能测试
echo "3. 基础功能测试..."
uv run python -m textup.cli.main --version > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ 基础功能异常"
    exit 1
fi

echo "✅ 所有测试通过！"
```

### 完整测试套件

```bash
#!/bin/bash
# run-dev-tests.sh

echo "🧪 TextUp 开发测试套件"

# 基础功能测试
echo "测试模块导入..."
python -c "import sys; sys.path.append('src'); import textup" || exit 1

echo "测试版本检查..."
uv run python -m textup.cli.main --version || exit 1

echo "测试配置功能..."
uv run python -m textup.cli.main config --list || exit 1

echo "测试认证状态..."
uv run python -m textup.cli.main auth --status || exit 1

# 如果测试文件存在，测试发布功能
if [ -f "test-content/basic-test.md" ]; then
    echo "测试内容验证..."
    uv run python -m textup.cli.main validate --file test-content/basic-test.md --platform weibo || exit 1

    echo "测试模拟发布..."
    uv run python -m textup.cli.main publish --file test-content/basic-test.md --platform weibo --dry-run || exit 1
fi

echo "🎉 所有测试通过！"
```

## 📝 开发最佳实践

### 日常开发流程

1. **环境准备**
   ```bash
   source .venv/bin/activate
   export TEXTUP_LOG_LEVEL="DEBUG"
   ```

2. **代码修改后**
   ```bash
   # 快速验证
   ./quick-test.sh

   # 功能测试
   uv run python -m textup.cli.main --debug <command>
   ```

3. **查看日志**
   ```bash
   tail -f dev-logs/textup-dev.log
   ```

### 调试技巧

- 使用 `--debug` 参数获取详细日志
- 使用 `--dry-run` 进行安全测试
- 定期检查日志文件排查问题
- 使用模块化测试验证特定功能

## 🔗 相关资源

### 用户文档
- **[📘 快速上手指南](../usage/quick-start-guide.md)** - 10分钟快速入门
- **[🧪 本地测试指南](../usage/local-testing-guide.md)** - 完整功能测试
- **[❓ 常见问题解答](../usage/faq.md)** - 快速解决问题
- **[🛠️ 故障排除指南](../usage/troubleshooting-guide.md)** - 系统问题诊断

### 技术文档
- **[🔧 API 参考文档](../api/)** - 开发者接口文档
- **[🏗️ 项目架构](../project-structure.md)** - 项目结构说明
- **[📋 产品规范](../product/)** - 完整产品文档

---

**开发测试环境已就绪！开始你的 TextUp 开发之旅吧！** 🚀

---

**最后更新**：2025-09-04
**文档版本**：v2.0
**维护团队**：TextUp 开发团队
