# TextUp 开发测试指南 🛠️

> **专为开发阶段设计** - 使用 `uv run python` 方式直接运行源码，方便代码修改和调试

---

## 🎯 开发测试目标

本指南将帮助开发者：
- ✅ 快速搭建开发环境
- ✅ 使用源码直接运行程序，无需安装
- ✅ 通过日志实时查看程序运行状态
- ✅ 完整测试所有平台功能
- ✅ 快速调试和修改代码

**适用场景**: 代码开发、功能调试、快速测试

---

## 📋 第一部分：开发环境准备

### 1.1 目录结构确认
```bash
# 进入项目根目录
cd /Volumes/mini_matrix/github/uploader/textup

# 确认项目结构
tree -L 3 .
# 或者
find . -name "*.py" -type f | head -10
```

### 1.2 创建开发环境
```bash
# 创建虚拟环境
uv venv --python 3.9

# 激活虚拟环境
source .venv/bin/activate

# 安装依赖（开发模式）
uv pip install -e .

# 验证Python路径
which python
python --version
```

### 1.3 设置开发配置
```bash
# 创建开发专用配置目录
mkdir -p dev-config
mkdir -p dev-logs
mkdir -p dev-data

# 设置环境变量（可选）
export TEXTUP_CONFIG_DIR="./dev-config"
export TEXTUP_LOG_LEVEL="DEBUG"
```

---

## 🚀 第二部分：使用源码直接运行

### 2.1 基础运行方式

**方法1: 使用模块方式运行（推荐）**
```bash
# 查看帮助
uv run python -m textup.cli.main --help

# 查看版本
uv run python -m textup.cli.main --version

# 启用调试模式
uv run python -m textup.cli.main --debug --help
```

**方法2: 直接运行主文件**
```bash
# 进入源码目录
cd src

# 直接运行CLI主文件
uv run python -m textup.cli.main --help
```

**方法3: 使用完整路径**
```bash
# 从项目根目录运行
uv run python src/textup/cli/main.py --help
```

### 2.2 常用开发命令模板
```bash
# 配置管理
uv run python -m textup.cli.main config --list
uv run python -m textup.cli.main config --set key=value
uv run python -m textup.cli.main config --interactive

# 认证管理
uv run python -m textup.cli.main auth --status
uv run python -m textup.cli.main auth --interactive --platform weibo

# 内容发布
uv run python -m textup.cli.main publish --file article.md --platform weibo --dry-run
uv run python -m textup.cli.main publish --interactive
```

---

## 📝 第三部分：日志配置和监控

### 3.1 配置详细日志
```bash
# 创建日志配置文件
cat > dev-config/logging.yaml << 'EOF'
version: 1
disable_existing_loggers: false

formatters:
  detailed:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
  simple:
    format: '%(levelname)s - %(message)s'

handlers:
  console:
    class: logging.StreamHandler
    level: DEBUG
    formatter: simple
    stream: ext://sys.stdout
  
  file:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: detailed
    filename: dev-logs/textup-dev.log
    maxBytes: 10485760  # 10MB
    backupCount: 5

  error_file:
    class: logging.FileHandler
    level: ERROR
    formatter: detailed
    filename: dev-logs/textup-errors.log

loggers:
  textup:
    level: DEBUG
    handlers: [console, file, error_file]
    propagate: false

root:
  level: INFO
  handlers: [console, file]
EOF
```

### 3.2 实时监控日志
```bash
# 启动日志监控（新终端窗口）
tail -f dev-logs/textup-dev.log

# 或者使用多彩日志查看
tail -f dev-logs/textup-dev.log | grep --color=always -E "(ERROR|WARNING|INFO|DEBUG)"

# 只查看错误日志
tail -f dev-logs/textup-errors.log
```

### 3.3 带日志的运行示例
```bash
# 运行时启用详细日志
uv run python -m textup.cli.main --debug config --list 2>&1 | tee dev-logs/config-test.log

# 发布测试时记录完整日志
uv run python -m textup.cli.main --debug publish --file test.md --platform weibo --dry-run 2>&1 | tee dev-logs/publish-test.log
```

---

## 🧪 第四部分：创建测试内容

### 4.1 创建测试文件目录
```bash
# 创建测试内容目录
mkdir -p test-content
mkdir -p test-results
```

### 4.2 创建基础测试文章
```bash
cat > test-content/basic-test.md << 'EOF'
# TextUp 基础功能测试

## 测试目的
验证 TextUp 的基本发布功能是否正常工作。

## 测试内容

### 文本格式测试
- **粗体文本**
- *斜体文本*
- `行内代码`

### 列表测试
1. 第一项
2. 第二项
3. 第三项

### 代码块测试
```python
def hello_textup():
    print("Hello from TextUp!")
    return True
```

### 链接测试
[TextUp GitHub](https://github.com/textup-team/textup)

## 标签
#TextUp #测试 #开发
EOF
```

### 4.3 创建平台特定测试文章
```bash
# 微博测试文章（短内容）
cat > test-content/weibo-test.md << 'EOF'
# 微博发布测试 🚀

TextUp 微博发布功能测试中...

✅ 支持 Markdown 转换
✅ 支持 Emoji 表情
✅ 支持话题标签

测试时间：$(date)

#微博测试 #TextUp #自动化发布
EOF

# 知乎测试文章（长内容）
cat > test-content/zhihu-test.md << 'EOF'
# 知乎专栏：TextUp 多平台发布工具深度解析

## 引言

在自媒体时代，内容创作者面临着一个共同的挑战：如何高效地将优质内容分发到多个平台？TextUp 应运而生，为这个问题提供了完美的解决方案。

## 核心功能特性

### 1. 多平台支持
TextUp 目前支持以下主流平台：
- 知乎：专栏文章发布
- 微博：长微博和普通微博
- 今日头条：文章和微头条
- 小红书：笔记发布（开发中）

### 2. 智能格式转换
不同平台对内容格式的要求各不相同：

```python
# TextUp 的格式转换引擎
class PlatformAdapter:
    def convert_markdown(self, content: str) -> str:
        # 智能转换逻辑
        return self.adapt_content(content)
```

### 3. 认证安全管理
采用 OAuth 2.0 标准，确保账号安全：
- 加密存储访问令牌
- 自动刷新过期令牌
- 支持多账号管理

## 技术架构

TextUp 采用现代 Python 技术栈：

| 组件 | 技术选择 | 说明 |
|------|----------|------|
| CLI 框架 | Typer | 现代 Python CLI 库 |
| HTTP 客户端 | httpx | 异步 HTTP 请求 |
| 数据验证 | Pydantic | 类型安全的数据模型 |
| 配置管理 | YAML | 人类友好的配置格式 |

## 使用场景分析

### 个人博主
- 维护多个平台账号
- 希望同步发布内容
- 追求发布效率

### 企业营销
- 品牌内容统一管理
- 多渠道内容分发
- 发布流程标准化

### 技术团队
- 技术文档同步发布
- 开源项目宣传
- 社区内容建设

## 最佳实践建议

1. **内容策略**：根据平台特性调整内容风格
2. **发布时机**：利用平台用户活跃时间
3. **标签优化**：合理使用平台标签系统
4. **数据分析**：跟踪发布效果，优化策略

## 结论

TextUp 不仅是一个发布工具，更是内容创作者的效率加速器。通过自动化处理重复性工作，让创作者能够专注于内容质量的提升。

未来，我们还将支持更多平台，提供更智能的内容优化建议，敬请期待！

---

**关键词**：多平台发布、内容创作、自动化工具、Python、开源项目

#知乎 #TextUp #多平台发布 #内容创作工具 #Python开源
EOF

# 今日头条测试文章
cat > test-content/toutiao-test.md << 'EOF'
# 今日头条：自媒体人必备的多平台发布神器

## 痛点分析

作为自媒体从业者，你是否也遇到过这些问题：

- 📝 同一篇文章要在多个平台手动发布，费时费力
- 🔄 不同平台格式要求不同，需要反复调整
- 🔐 多个平台账号管理混乱，安全隐患大
- 📊 无法统一跟踪各平台发布效果

## 解决方案：TextUp

TextUp 是一款专为内容创作者设计的多平台发布工具，一键解决上述所有痛点！

### 核心优势

**🚀 一键多发**
- 支持微博、知乎、今日头条等主流平台
- 批量发布，节省 80% 时间
- 定时发布，最佳时机触达用户

**🎯 智能适配**
- 自动适配各平台格式要求
- Markdown 原生支持
- 图片、链接智能处理

**🔒 安全可靠**
- OAuth 2.0 安全认证
- 加密存储用户凭证
- 完善的错误处理机制

**📈 数据追踪**
- 发布状态实时监控
- 历史记录完整保存
- 失败自动重试

### 使用场景

#### 场景一：日常内容发布
小王是一位科技博主，每天需要在 5 个平台发布文章。使用 TextUp 后：
- 发布时间：从 30 分钟缩短到 5 分钟
- 格式错误：从经常出现到零错误
- 管理效率：提升 6 倍

#### 场景二：爆款内容推广
小李写了一篇爆款文章，需要快速在所有平台推广：
- 传统方式：需要 1-2 小时逐个平台发布
- TextUp 方式：2 分钟完成所有平台发布
- 效果：抓住最佳传播时机，阅读量提升 200%

### 技术特色

TextUp 采用现代化技术架构：

```python
# 异步发布引擎示例
async def publish_to_platforms(content, platforms):
    tasks = [
        adapter.publish(content) 
        for adapter in platform_adapters
    ]
    results = await asyncio.gather(*tasks)
    return results
```

**技术亮点**：
- Python 3.9+ 现代语法
- 异步并发处理
- 类型安全设计
- 完善的测试覆盖

### 立即开始

1. **安装配置**（5分钟）
   ```bash
   pip install textup
   textup init
   ```

2. **平台认证**（10分钟）
   ```bash
   textup auth login weibo
   textup auth login zhihu
   ```

3. **发布内容**（1分钟）
   ```bash
   textup publish article.md --platforms all
   ```

### 用户评价

> "TextUp 真的解放了我的双手，现在发布文章就像发朋友圈一样简单！" —— 科技博主小张

> "团队使用 TextUp 后，内容运营效率提升了 5 倍，强烈推荐！" —— 某创业公司 CMO

## 总结

在内容为王的时代，效率工具的价值不言而喻。TextUp 不仅是一个发布工具，更是内容创作者的贴心助手。

**立即体验 TextUp，让内容创作更高效！**

---

**本文标签**：#自媒体工具 #多平台发布 #效率提升 #TextUp #内容创作

**相关阅读**：
- 《自媒体运营完全指南》
- 《Python 自动化工具开发实战》
- 《内容营销最佳实践》
EOF
```

---

## 🔧 第五部分：完整功能测试流程

### 5.1 配置功能测试
```bash
echo "=== 配置功能测试开始 ===" | tee -a dev-logs/test-session.log

# 测试配置初始化
echo "1. 测试配置初始化..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug config --interactive << 'EOF' 2>&1 | tee -a dev-logs/test-session.log
TextUp开发测试
INFO
3
EOF

# 测试配置读取
echo "2. 测试配置读取..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug config --list 2>&1 | tee -a dev-logs/test-session.log

# 测试单项配置
echo "3. 测试单项配置..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug config --set app.test_mode=true 2>&1 | tee -a dev-logs/test-session.log

# 验证配置
echo "4. 验证配置..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug config --get app.test_mode 2>&1 | tee -a dev-logs/test-session.log

echo "=== 配置功能测试完成 ===" | tee -a dev-logs/test-session.log
```

### 5.2 微博平台功能测试
```bash
echo "=== 微博平台测试开始 ===" | tee -a dev-logs/test-session.log

# 1. 配置微博平台
echo "1. 配置微博凭证..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug config --set platforms.weibo.client_id="test_weibo_key" 2>&1 | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug config --set platforms.weibo.client_secret="test_weibo_secret" 2>&1 | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug config --set platforms.weibo.redirect_uri="http://localhost:8080/auth/weibo/callback" 2>&1 | tee -a dev-logs/test-session.log

# 2. 验证微博配置
echo "2. 验证微博配置..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug config --get platforms.weibo 2>&1 | tee -a dev-logs/test-session.log

# 3. 测试微博认证状态
echo "3. 检查微博认证状态..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug auth --status --platform weibo 2>&1 | tee -a dev-logs/test-session.log

# 4. 测试内容验证
echo "4. 测试微博内容验证..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug validate --file test-content/weibo-test.md --platform weibo 2>&1 | tee -a dev-logs/test-session.log

# 5. 测试内容预览
echo "5. 测试微博内容预览..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug preview --file test-content/weibo-test.md --platform weibo 2>&1 | tee -a dev-logs/test-session.log

# 6. 测试模拟发布
echo "6. 测试微博模拟发布..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug publish --file test-content/weibo-test.md --platform weibo --dry-run 2>&1 | tee -a dev-logs/test-session.log

echo "=== 微博平台测试完成 ===" | tee -a dev-logs/test-session.log
```

### 5.3 知乎平台功能测试
```bash
echo "=== 知乎平台测试开始 ===" | tee -a dev-logs/test-session.log

# 1. 配置知乎平台
echo "1. 配置知乎凭证..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug config --set platforms.zhihu.client_id="test_zhihu_id" 2>&1 | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug config --set platforms.zhihu.client_secret="test_zhihu_secret" 2>&1 | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug config --set platforms.zhihu.redirect_uri="http://localhost:8080/auth/zhihu/callback" 2>&1 | tee -a dev-logs/test-session.log

# 2. 验证知乎配置
echo "2. 验证知乎配置..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug config --get platforms.zhihu 2>&1 | tee -a dev-logs/test-session.log

# 3. 测试知乎认证状态
echo "3. 检查知乎认证状态..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug auth --status --platform zhihu 2>&1 | tee -a dev-logs/test-session.log

# 4. 测试长文章内容验证
echo "4. 测试知乎长文章验证..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug validate --file test-content/zhihu-test.md --platform zhihu 2>&1 | tee -a dev-logs/test-session.log

# 5. 测试内容预览
echo "5. 测试知乎内容预览..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug preview --file test-content/zhihu-test.md --platform zhihu 2>&1 | tee -a dev-logs/test-session.log

# 6. 测试模拟发布
echo "6. 测试知乎模拟发布..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug publish --file test-content/zhihu-test.md --platform zhihu --dry-run 2>&1 | tee -a dev-logs/test-session.log

echo "=== 知乎平台测试完成 ===" | tee -a dev-logs/test-session.log
```

### 5.4 今日头条平台功能测试
```bash
echo "=== 今日头条平台测试开始 ===" | tee -a dev-logs/test-session.log

# 1. 配置今日头条平台
echo "1. 配置今日头条凭证..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug config --set platforms.toutiao.app_id="test_toutiao_id" 2>&1 | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug config --set platforms.toutiao.secret="test_toutiao_secret" 2>&1 | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug config --set platforms.toutiao.redirect_uri="http://localhost:8080/auth/toutiao/callback" 2>&1 | tee -a dev-logs/test-session.log

# 2. 验证今日头条配置
echo "2. 验证今日头条配置..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug config --get platforms.toutiao 2>&1 | tee -a dev-logs/test-session.log

# 3. 测试今日头条认证状态
echo "3. 检查今日头条认证状态..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug auth --status --platform toutiao 2>&1 | tee -a dev-logs/test-session.log

# 4. 测试内容验证
echo "4. 测试今日头条内容验证..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug validate --file test-content/toutiao-test.md --platform toutiao 2>&1 | tee -a dev-logs/test-session.log

# 5. 测试内容预览
echo "5. 测试今日头条内容预览..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug preview --file test-content/toutiao-test.md --platform toutiao 2>&1 | tee -a dev-logs/test-session.log

# 6. 测试模拟发布
echo "6. 测试今日头条模拟发布..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug publish --file test-content/toutiao-test.md --platform toutiao --dry-run 2>&1 | tee -a dev-logs/test-session.log

echo "=== 今日头条平台测试完成 ===" | tee -a dev-logs/test-session.log
```

### 5.5 多平台批量测试
```bash
echo "=== 多平台批量测试开始 ===" | tee -a dev-logs/test-session.log

# 1. 多平台配置检查
echo "1. 多平台配置检查..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug config --get platforms 2>&1 | tee -a dev-logs/test-session.log

# 2. 多平台认证状态
echo "2. 多平台认证状态..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug auth --status 2>&1 | tee -a dev-logs/test-session.log

# 3. 单文章多平台发布测试
echo "3. 单文章多平台发布测试..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug publish --file test-content/basic-test.md --platforms weibo,zhihu,toutiao --dry-run 2>&1 | tee -a dev-logs/test-session.log

# 4. 批量文件发布测试
echo "4. 批量文件发布测试..." | tee -a dev-logs/test-session.log
uv run python -m textup.cli.main --debug publish --directory test-content --platforms weibo,zhihu --dry-run 2>&1 | tee -a dev-logs/test-session.log

echo "=== 多平台批量测试完成 ===" | tee -a dev-logs/test-session.log
```

---

## 📊 第六部分：交互式测试

### 6.1 交互式配置测试
```bash
# 创建交互式测试脚本
cat > test-interactive-config.sh << 'EOF'
#!/bin/bash
echo "=== 交互式配置测试 ==="

# 模拟用户输入进行交互式配置
echo "TextUp Development Test
DEBUG
5
y" | uv run python -m textup.cli.main --debug config --interactive 2>&1 | tee dev-logs/interactive-config.log

echo "配置完成，查看结果："
uv run python -m textup.cli.main config --list
EOF

chmod +x test-interactive-config.sh
```

### 6.2 交互式发布测试
```bash
# 创建交互式发布测试脚本
cat > test-interactive-publish.sh << 'EOF'
#!/bin/bash
echo "=== 交互式发布测试 ==="

# 模拟用户输入进行交互式发布
echo "test-content/basic-test.md
weibo
y" | uv run python -m textup.cli.main --debug publish --interactive --dry-run 2>&1 | tee dev-logs/interactive-publish.log

echo "发布测试完成，查看日志："
tail -20 dev-logs/interactive-publish.log
EOF

chmod +x test-interactive-publish.sh
```

---

## 🛠️ 第七部分：代码修改测试流程

### 7.1 热重载开发模式
```bash
# 安装开发工具（可选）
uv pip install watchdog

# 创建文件变化监控脚本
cat > dev-watch.py << 'EOF'
import time
import subprocess
import sys
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class CodeChangeHandler(FileSystemEventHandler):
    def __init__(self):
        self.last_run = 0
    
    def on_modified(self, event):
        if event.is_directory:
            return
        
        # 只监控Python文件
        if not event.src_path.endswith('.py'):
            return
            
        current_time = time.time()
        # 防止重复执行
        if current_time - self.last_run < 2:
            return
            
        print(f"\n📝 文件变化: {event.src_path}")
        print("🔄 重新运行测试...")
        
        # 运行快速测试
        try:
            result = subprocess.run([
                'uv', 'run', 'python', '-m', 'textup.cli.main', '--version'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 基础功能正常")
            else:
                print("❌ 发现错误:")
                print(result.stderr)
        except Exception as e:
            print(f"❌ 执行失败: {e}")
        
        self.last_run = current_time
        print("=" * 50)

if __name__ == "__main__":
    event_handler = CodeChangeHandler()
    observer = Observer()
    observer.schedule(event_handler, "./src", recursive=True)
    observer.start()
    
    print("🔍 开始监控代码变化...")
    print("按 Ctrl+C 退出监控")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        observer.stop()
        print("\n👋 监控已停止")
    
    observer.join()
EOF
```

### 7.2 快速测试循环
```bash
# 创建快速测试脚本
cat > quick-test.sh << 'EOF'
#!/bin/bash

echo "🚀 快速测试循环开始..."

# 1. 语法检查
echo "1. 检查语法错误..."
python -m py_compile src/textup/cli/main.py
if [ $? -ne 0 ]; then
    echo "❌ 语法错误，请修复后重试"
    exit 1
fi
echo "✅ 语法检查通过"

# 2. 导入测试
echo "2. 检查导入..."
python -c "import sys; sys.path.append('src'); import textup.cli.main" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 导入失败"
    exit 1
fi
echo "✅ 导入测试通过"

# 3. 基础功能测试
echo "3. 基础功能测试..."
uv run python -m textup.cli.main --version > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ 基础功能异常"
    exit 1
fi
echo "✅ 基础功能正常"

# 4. 配置测试
echo "4. 配置功能测试..."
uv run python -m textup.cli.main config --list > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 配置功能正常"
else
    echo "⚠️  配置功能可能有问题，但不影响开发"
fi

echo "🎉 快速测试完成！"
EOF

chmod +x quick-test.sh

# 使用方法
./quick-test.sh
```

### 7.3 调试断点设置
```bash
# 创建调试版本的运行脚本
cat > debug-run.py << 'EOF'
#!/usr/bin/env python3
"""
开发调试运行脚本
支持断点调试和详细日志输出
"""

import sys
import os
import pdb
from pathlib import Path

# 添加源码路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# 设置调试环境变量
os.environ["TEXTUP_DEBUG"] = "1"
os.environ["TEXTUP_LOG_LEVEL"] = "DEBUG"

def debug_main():
    """调试模式主函数"""
    try:
        # 导入CLI模块
        from textup.cli import main
        
        # 设置断点（可选）
        # pdb.set_trace()
        
        # 运行主程序
        main.main()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已正确安装依赖")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 程序已中断")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 运行时错误: {e}")
        # 自动进入调试模式
        pdb.post_mortem()
        sys.exit(1)

if __name__ == "__main__":
    debug_main()
EOF

chmod +x debug-run.py
```

---

## 🐛 第八部分：错误处理和调试

### 8.1 常见错误排查
```bash
# 创建错误排查脚本
cat > troubleshoot.sh << 'EOF'
#!/bin/bash

echo "🔍 TextUp 错误排查工具"
echo "======================="

# 1. 环境检查
echo "1. 环境检查..."
echo "Python版本: $(python --version)"
echo "UV版本: $(uv --version 2>/dev/null || echo '未安装')"
echo "虚拟环境: $VIRTUAL_ENV"

# 2. 依赖检查
echo -e "\n2. 依赖检查..."
python -c "
import sys
required_packages = ['typer', 'rich', 'pydantic', 'httpx', 'pyyaml']
for pkg in required_packages:
    try:
        __import__(pkg)
        print(f'✅ {pkg}')
    except ImportError:
        print(f'❌ {pkg} - 未安装')
"

# 3. 文件结构检查
echo -e "\n3. 文件结构检查..."
critical_files=(
    "src/textup/__init__.py"
    "src/textup/cli/main.py"
    "src/textup/services/config_manager.py"
    "pyproject.toml"
)

for file in "${critical_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file - 缺失"
    fi
done

# 4. 权限检查
echo -e "\n4. 权限检查..."
if [ -w "." ]; then
    echo "✅ 当前目录可写"
else
    echo "❌ 当前目录不可写"
fi

# 5. 配置目录检查
echo -e "\n5. 配置目录检查..."
config_dirs=("$HOME/.textup" "./dev-config" "./dev-logs")
for dir in "${config_dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo "✅ $dir"
    else
        echo "⚠️  $dir - 不存在（将自动创建）"
        mkdir -p "$dir" 2>/dev/null
    fi
done

echo -e "\n6. 快速功能测试..."
if uv run python -c "import sys; sys.path.append('src'); import textup" 2>/dev/null; then
    echo "✅ TextUp 模块导入成功"
else
    echo "❌ TextUp 模块导入失败"
    echo "建议执行: uv pip install -e ."
fi

echo -e "\n🔧 排查完成！"
EOF

chmod +x troubleshoot.sh
```

### 8.2 日志分析工具
```bash
# 创建日志分析脚本
cat > analyze-logs.sh << 'EOF'
#!/bin/bash

LOG_FILE="dev-logs/textup-dev.log"
ERROR_FILE="dev-logs/textup-errors.log"

echo "📊 TextUp 日志分析报告"
echo "====================="

if [ ! -f "$LOG_FILE" ]; then
    echo "❌ 日志文件不存在: $LOG_FILE"
    echo "请先运行一些命令生成日志"
    exit 1
fi

# 1. 日志统计
echo "1. 日志统计："
echo "   总行数: $(wc -l < "$LOG_FILE")"
echo "   错误数: $(grep -c ERROR "$LOG_FILE" 2>/dev/null || echo 0)"
echo "   警告数: $(grep -c WARNING "$LOG_FILE" 2>/dev/null || echo 0)"
echo "   信息数: $(grep -c INFO "$LOG_FILE" 2>/dev/null || echo 0)"
echo "   调试数: $(grep -c DEBUG "$LOG_FILE" 2>/dev/null || echo 0)"

# 2. 最近错误
echo -e "\n2. 最近错误："
if [ -f "$ERROR_FILE" ]; then
    tail -10 "$ERROR_FILE" | head -5
else
    echo "   无错误日志文件"
fi

# 3. 性能分析
echo -e "\n3. 性能分析："
echo "   平均响应时间: $(grep -o 'took [0-9.]*ms' "$LOG_FILE" | awk '{sum+=$2; count++} END {print (count>0 ? sum/count : 0) "ms"}' 2>/dev/null || echo '无数据')"

# 4. 功能使用统计
echo -e "\n4. 功能使用统计："
echo "   配置操作: $(grep -c 'config' "$LOG_FILE" 2>/dev/null || echo 0)"
echo "   认证操作: $(grep -c 'auth' "$LOG_FILE" 2>/dev/null || echo 0)"
echo "   发布操作: $(grep -c 'publish' "$LOG_FILE" 2>/dev/null || echo 0)"

# 5. 最新活动
echo -e "\n5. 最新活动（最后10行）："
tail -10 "$LOG_FILE"

echo -e "\n📋 分析完成！"
EOF

chmod +x analyze-logs.sh
```

---

## 🧪 第九部分：自动化测试集成

### 9.1 创建完整测试套件
```bash
# 创建自动化测试运行脚本
cat > run-dev-tests.sh << 'EOF'
#!/bin/bash

echo "🧪 TextUp 开发测试套件"
echo "======================"

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试结果统计
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo -e "\n${YELLOW}🔸 运行测试: $test_name${NC}"
    ((TESTS_TOTAL++))
    
    if eval "$test_command" > /tmp/test_output 2>&1; then
        echo -e "${GREEN}✅ $test_name - 通过${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}❌ $test_name - 失败${NC}"
        echo "错误输出:"
        cat /tmp/test_output | head -5
        ((TESTS_FAILED++))
    fi
}

# 开始测试
echo "开始运行测试套件..."

# 1. 基础功能测试
run_test "模块导入" "python -c 'import sys; sys.path.append(\"src\"); import textup'"
run_test "版本检查" "uv run python -m textup.cli.main --version"
run_test "帮助信息" "uv run python -m textup.cli.main --help"

# 2. 配置功能测试
run_test "配置列表" "uv run python -m textup.cli.main config --list"
run_test "配置设置" "uv run python -m textup.cli.main config --set test.key=value"
run_test "配置获取" "uv run python -m textup.cli.main config --get test.key"

# 3. 认证功能测试
run_test "认证状态" "uv run python -m textup.cli.main auth --status"

# 4. 内容验证测试（如果测试文件存在）
if [ -f "test-content/basic-test.md" ]; then
    run_test "内容验证-微博" "uv run python -m textup.cli.main validate --file test-content/basic-test.md --platform weibo"
    run_test "内容预览-微博" "uv run python -m textup.cli.main preview --file test-content/basic-test.md --platform weibo"
    run_test "模拟发布-微博" "uv run python -m textup.cli.main publish --file test-content/basic-test.md --platform weibo --dry-run"
fi

# 5. 错误处理测试
run_test "无效文件处理" "! uv run python -m textup.cli.main publish --file nonexistent.md --platform weibo --dry-run"
run_test "无效平台处理" "! uv run python -m textup.cli.main publish --file test-content/basic-test.md --platform invalid --dry-run"

# 6. Pytest 测试（如果可用）
if command -v pytest &> /dev/null; then
    run_test "单元测试" "uv run pytest tests/ -v --tb=short -q"
fi

# 生成测试报告
echo -e "\n📊 测试结果统计"
echo "=================="
echo -e "总测试数: $TESTS_TOTAL"
echo -e "${GREEN}通过: $TESTS_PASSED${NC}"
echo -e "${RED}失败: $TESTS_FAILED${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 所有测试通过！${NC}"
    exit 0
else
    echo -e "\n${RED}⚠️  有 $TESTS_FAILED 个测试失败${NC}"
    exit 1
fi
EOF

chmod +x run-dev-tests.sh
```

### 9.2 性能基准测试
```bash
# 创建性能测试脚本
cat > benchmark-test.sh << 'EOF'
#!/bin/bash

echo "⚡ TextUp 性能基准测试"
echo "===================="

# 创建测试文件
mkdir -p benchmark-content
for i in {1..10}; do
    echo "# 测试文章 $i

这是第 $i 篇测试文章，用于性能测试。

## 内容

Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.

#测试 #性能 #TextUp" > "benchmark-content/test-$i.md"
done

# 性能测试
echo "1. 单文件处理速度测试..."
time uv run python -m textup.cli.main publish --file benchmark-content/test-1.md --platform weibo --dry-run

echo -e "\n2. 批量处理速度测试..."
time uv run python -m textup.cli.main publish --directory benchmark-content --platform weibo --dry-run

echo -e "\n3. 多平台处理速度测试..."
time uv run python -m textup.cli.main publish --file benchmark-content/test-1.md --platforms weibo,zhihu,toutiao --dry-run

# 清理
rm -rf benchmark-content

echo -e "\n🏁 性能测试完成！"
EOF

chmod +x benchmark-test.sh
```

---

## 📝 第十部分：开发最佳实践

### 10.1 代码修改工作流程
```bash
# 创建开发工作流脚本
cat > dev-workflow.md << 'EOF'
# TextUp 开发工作流程

## 1. 准备阶段
```bash
# 激活开发环境
source .venv/bin/activate

# 确认环境状态
./troubleshoot.sh

# 启动日志监控（新终端）
tail -f dev-logs/textup-dev.log
```

## 2. 开发阶段
```bash
# 修改代码前先备份配置
cp dev-config/config.yaml dev-config/config.backup.yaml

# 快速测试（每次修改后）
./quick-test.sh

# 功能测试（重要修改后）
./run-dev-tests.sh
```

## 3. 测试阶段
```bash
# 单功能测试
uv run python -m textup.cli.main --debug [command] [options]

# 完整功能测试
bash test-scripts/test-[platform]-functionality.sh

# 性能测试
./benchmark-test.sh
```

## 4. 调试阶段
```bash
# 详细日志模式
uv run python -m textup.cli.main --debug [command]

# 交互式调试
python debug-run.py [args]

# 日志分析
./analyze-logs.sh
```
EOF
```

### 10.2 常用开发命令速查表
```bash
cat > dev-commands.md << 'EOF'
# TextUp 开发命令速查表

## 基础运行
```bash
# 查看版本
uv run python -m textup.cli.main --version

# 查看帮助
uv run python -m textup.cli.main --help

# 调试模式
uv run python -m textup.cli.main --debug [command]
```

## 配置管理
```bash
# 列出所有配置
uv run python -m textup.cli.main config --list

# 设置配置项
uv run python -m textup.cli.main config --set key=value

# 获取配置项
uv run python -m textup.cli.main config --get key

# 交互式配置
uv run python -m textup.cli.main config --interactive
```

## 平台认证
```bash
# 查看认证状态
uv run python -m textup.cli.main auth --status

# 平台认证
uv run python -m textup.cli.main auth --interactive --platform [platform]

# 撤销认证
uv run python -m textup.cli.main auth --revoke --platform [platform]
```

## 内容发布
```bash
# 验证内容
uv run python -m textup.cli.main validate --file [file] --platform [platform]

# 预览内容
uv run python -m textup.cli.main preview --file [file] --platform [platform]

# 模拟发布
uv run python -m textup.cli.main publish --file [file] --platform [platform] --dry-run

# 实际发布
uv run python -m textup.cli.main publish --file [file] --platform [platform]

# 批量发布
uv run python -m textup.cli.main publish --directory [dir] --platforms [platforms]

# 交互式发布
uv run python -m textup.cli.main publish --interactive
```

## 开发工具
```bash
# 快速测试
./quick-test.sh

# 完整测试
./run-dev-tests.sh

# 性能测试
./benchmark-test.sh

# 错误排查
./troubleshoot.sh

# 日志分析
./analyze-logs.sh
```
EOF
```

---

## 🎯 第十一部分：测试检查清单

### 11.1 创建测试检查清单
```bash
cat > testing-checklist.md << 'EOF'
# TextUp 开发测试检查清单

## 环境准备 ✅
- [ ] Python 3.9+ 已安装
- [ ] uv 包管理器已安装
- [ ] 虚拟环境已创建并激活
- [ ] 项目依赖已安装
- [ ] 源码可以正常导入

## 基础功能测试 ✅
- [ ] `--version` 显示版本信息
- [ ] `--help` 显示帮助信息
- [ ] `--debug` 模式正常工作
- [ ] 配置目录自动创建
- [ ] 日志文件正常生成

## 配置管理测试 ✅
- [ ] `config --list` 显示配置
- [ ] `config --set` 设置配置
- [ ] `config --get` 获取配置
- [ ] `config --interactive` 交互式配置
- [ ] 配置持久化保存

## 认证管理测试 ✅
- [ ] `auth --status` 显示状态
- [ ] 微博平台配置正常
- [ ] 知乎平台配置正常
- [ ] 今日头条平台配置正常
- [ ] 认证错误处理正常

## 内容处理测试 ✅
- [ ] Markdown 文件解析正常
- [ ] `validate` 内容验证功能
- [ ] `preview` 内容预览功能
- [ ] 格式转换功能正常
- [ ] 文件路径处理正常

## 发布功能测试 ✅
- [ ] `--dry-run` 模拟发布
- [ ] 单平台发布测试
- [ ] 多平台发布测试
- [ ] 批量发布测试
- [ ] 交互式发布测试

## 错误处理测试 ✅
- [ ] 无效文件路径处理
- [ ] 无效平台名称处理
- [ ] 网络错误处理
- [ ] 权限错误处理
- [ ] 配置错误处理

## 性能测试 ✅
- [ ] 单文件处理速度
- [ ] 批量文件处理速度
- [ ] 多平台并发处理
- [ ] 内存使用合理
- [ ] CPU 使用合理

## 日志系统测试 ✅
- [ ] DEBUG 级别日志
- [ ] INFO 级别日志
- [ ] WARNING 级别日志
- [ ] ERROR 级别日志
- [ ] 日志格式正确

## 代码质量检查 ✅
- [ ] 语法错误检查
- [ ] 导入错误检查
- [ ] 类型提示检查
- [ ] 代码风格检查
- [ ] 测试覆盖率检查
EOF
```

---

## 📋 第十二部分：总结和下一步

### 12.1 开发测试总结
本开发测试指南提供了：

1. **快速开发环境**: 使用 `uv run python` 直接运行源码
2. **实时日志监控**: 通过详细日志观察程序运行状态
3. **完整功能测试**: 覆盖所有核心功能的测试用例
4. **调试工具集**: 错误排查、性能分析、日志分析工具
5. **自动化测试**: 可重复执行的测试脚本
6. **开发最佳实践**: 标准化的开发工作流程

### 12.2 推荐的开发流程

1. **初始设置**（一次性）:
   ```bash
   cd /Volumes/mini_matrix/github/uploader/textup
   source .venv/bin/activate
   ./troubleshoot.sh
   ```

2. **日常开发循环**:
   ```bash
   # 修改代码
   vim src/textup/[module].py
   
   # 快速验证
   ./quick-test.sh
   
   # 功能测试
   uv run python -m textup.cli.main --debug [test-command]
   
   # 查看日志
   tail dev-logs/textup-dev.log
   ```

3. **重要修改后**:
   ```bash
   ./run-dev-tests.sh
   ./benchmark-test.sh
   ```

### 12.3 下一步建议

1. **获取真实API凭证**: 替换测试凭证为真实平台API凭证
2. **进行真实发布测试**: 在测试账号上进行实际发布测试
3. **优化性能**: 根据性能测试结果优化代码
4. **扩展测试覆盖**: 添加更多边界条件和错误场景测试
5. **集成CI/CD**: 将测试脚本集成到持续集成流程

### 12.4 技术支持

- **问题排查**: 运行 `./troubleshoot.sh`
- **日志分析**: 运行 `./analyze-logs.sh`
- **功能测试**: 运行 `./run-dev-tests.sh`
- **性能评估**: 运行 `./benchmark-test.sh`

---

**🎉 开发测试环境已就绪！开始你的 TextUp 开发之旅吧！** 🚀

---

## 📱 第十三部分：一键启动使用指南

### 13.1 快速启动方式

我们为你准备了便捷的一键启动脚本，让开发测试更加简单！

#### 方式一：图形化主控台（推荐）
```bash
# 启动完整的开发测试主控台
./start-dev-test.sh

# 或者直接启动主控台
./dev-scripts/dev-test-master.sh
```

#### 方式二：直接运行测试
```bash
# 快速测试（30秒）
./start-dev-test.sh --quick

# 完整测试（5-10分钟）
./start-dev-test.sh --full

# 故障排查
./dev-scripts/troubleshoot.sh

# 日志分析
./dev-scripts/analyze-logs.sh
```

#### 方式三：传统命令行方式
```bash
# 使用 uv run python 直接运行
uv run python -m textup.cli.main --version
uv run python -m textup.cli.main --debug config --list
uv run python -m textup.cli.main publish --file test.md --platform weibo --dry-run
```

### 13.2 主控台功能介绍

启动主控台后，你可以使用以下功能：

#### 🎯 主功能菜单
- **快速测试** - 30秒内完成基础环境和语法检查
- **完整功能测试** - 5-10分钟完整验证所有平台功能
- **故障排查** - 深度环境诊断和问题修复
- **日志分析** - 分析运行日志和性能数据

#### 🛠️ 开发工具菜单
- **创建测试内容** - 自动生成各平台测试文章
- **单平台测试** - 测试特定平台功能
- **性能基准测试** - 批量处理性能评估
- **代码质量检查** - 语法、格式、类型检查

#### ⚙️ 管理功能
- **环境配置管理** - 一键初始化开发环境
- **系统状态查看** - 详细的系统和环境信息
- **清理和维护** - 清理日志和临时文件

### 13.3 首次使用流程

如果是第一次使用，建议按以下步骤操作：

```bash
# 1. 启动快速启动脚本
./start-dev-test.sh

# 2. 选择 "6. 环境设置" 初始化环境

# 3. 激活虚拟环境（如果提示）
source .venv/bin/activate

# 4. 选择 "1. 完整控制台" 进入主控台

# 5. 运行 "1. 快速测试" 验证环境

# 6. 运行 "2. 完整功能测试" 验证所有功能
```

### 13.4 日常开发工作流

**推荐的日常开发流程**：

```bash
# 启动开发环境
./start-dev-test.sh --console

# 1. 修改代码
vim src/textup/某个文件.py

# 2. 快速验证
选择菜单 "1. 快速测试"

# 3. 查看日志
选择菜单 "4. 日志分析"

# 4. 重复上述流程
```

**快速命令行流程**：

```bash
# 修改代码后快速测试
./dev-scripts/quick-test.sh

# 查看运行日志
tail -f dev-logs/textup-dev.log

# 特定功能测试
uv run python -m textup.cli.main --debug publish --file test-content/basic-test.md --platform weibo --dry-run
```

### 13.5 常用测试命令速查

#### 基础功能测试
```bash
# 版本检查
uv run python -m textup.cli.main --version

# 配置管理
uv run python -m textup.cli.main config --list
uv run python -m textup.cli.main config --set test.key=value

# 认证状态
uv run python -m textup.cli.main auth --status
```

#### 平台测试命令
```bash
# 微博测试
uv run python -m textup.cli.main --debug publish --file test-content/weibo-test.md --platform weibo --dry-run

# 知乎测试  
uv run python -m textup.cli.main --debug publish --file test-content/zhihu-test.md --platform zhihu --dry-run

# 今日头条测试
uv run python -m textup.cli.main --debug publish --file test-content/toutiao-test.md --platform toutiao --dry-run

# 多平台测试
uv run python -m textup.cli.main --debug publish --file test-content/basic-test.md --platforms weibo,zhihu,toutiao --dry-run
```

#### 批量测试命令
```bash
# 批量文件测试
uv run python -m textup.cli.main --debug publish --directory test-content --platform weibo --dry-run

# 通配符测试
uv run python -m textup.cli.main --debug publish --pattern "test-content/*.md" --platforms weibo,zhihu --dry-run
```

### 13.6 日志和调试

#### 实时监控日志
```bash
# 主日志
tail -f dev-logs/textup-dev.log

# 错误日志
tail -f dev-logs/textup-errors.log

# 带颜色的日志监控
tail -f dev-logs/textup-dev.log | grep --color=always -E "(ERROR|WARNING|INFO|DEBUG)"
```

#### 调试技巧
```bash
# 启用详细调试
uv run python -m textup.cli.main --debug [任何命令]

# 将输出同时保存到文件
uv run python -m textup.cli.main --debug config --list 2>&1 | tee debug-output.log

# 超时控制（防止命令卡死）
timeout 30s uv run python -m textup.cli.main publish --file test.md --platform weibo --dry-run
```

### 13.7 故障排查指南

#### 常见问题快速解决

**问题1：命令找不到**
```bash
# 解决方案
source .venv/bin/activate  # 激活虚拟环境
uv pip install -e .        # 重新安装项目
```

**问题2：模块导入失败**
```bash
# 检查和修复
./dev-scripts/troubleshoot.sh  # 运行故障排查
./dev-scripts/quick-test.sh    # 验证修复结果
```

**问题3：测试超时或失败**
```bash
# 分析和解决
./dev-scripts/analyze-logs.sh  # 分析日志
./dev-scripts/troubleshoot.sh  # 深度诊断
```

### 13.8 脚本文件说明

项目中包含以下关键脚本：

| 脚本文件 | 功能说明 | 预计用时 |
|---------|---------|----------|
| `start-dev-test.sh` | 主启动脚本，提供图形化菜单 | - |
| `dev-scripts/dev-test-master.sh` | 完整开发测试主控台 | - |
| `dev-scripts/quick-test.sh` | 快速基础测试 | 30秒 |
| `dev-scripts/test-all-platforms.sh` | 完整功能测试 | 5-10分钟 |
| `dev-scripts/troubleshoot.sh` | 故障排查和诊断 | 1-2分钟 |
| `dev-scripts/analyze-logs.sh` | 日志分析工具 | 1分钟 |

### 13.9 最佳实践建议

1. **环境管理**
   - 始终在虚拟环境中开发
   - 定期运行 `./dev-scripts/troubleshoot.sh` 检查环境健康
   - 使用 `./start-dev-test.sh --init` 重置环境

2. **测试流程**
   - 每次代码修改后运行快速测试
   - 重要功能修改后运行完整测试
   - 定期查看日志分析报告

3. **问题解决**
   - 遇到问题先运行故障排查脚本
   - 查看详细日志文件定位问题
   - 使用 `--debug` 参数获取更多信息

4. **性能优化**
   - 定期运行性能基准测试
   - 监控日志中的响应时间
   - 优化频繁出现警告的代码

### 13.10 技术支持

如果遇到问题，可以按以下顺序寻求帮助：

1. **自助排查**
   ```bash
   ./dev-scripts/troubleshoot.sh      # 自动诊断
   ./dev-scripts/analyze-logs.sh      # 日志分析
   ./start-dev-test.sh                # 查看帮助菜单
   ```

2. **查看文档**
   - 本文档：完整的开发测试指南
   - `docs/quick-start-guide.md`：快速上手指南
   - `docs/local-testing-guide.md`：本地测试指南

3. **提交问题**
   - GitHub Issues：报告bug和功能请求
   - 提供详细的错误日志和环境信息

---

## 🎊 总结

通过本指南，你现在拥有了：

✅ **完整的开发测试环境** - 一键启动，无需复杂配置  
✅ **多种测试方式** - 从快速验证到完整测试  
✅ **强大的调试工具** - 日志分析、故障排查、性能监控  
✅ **标准化工作流程** - 高效的开发和测试流程  
✅ **详细的文档指南** - 涵盖所有使用场景  

现在你可以：
- 🚀 快速开始：`./start-dev-test.sh`
- 🧪 完整测试：选择菜单中的完整功能测试
- 🔍 问题排查：使用内置的故障排查工具
- 📊 性能分析：通过日志分析了解系统状况

**祝您开发愉快！TextUp 期待您的贡献！** 🎉🚀
