# TextUp AI自动化工作流文档

**项目名称**: TextUp - 多平台文本内容发布工具  
**文档版本**: v1.0 (自动生成)  
**创建时间**: 2025-09-02  
**执行模式**: AI完全自动化  
**项目路径**: /Volumes/mini_matrix/github/uploader/textup  

---

## 🤖 AI执行入口

### 使用方式
```
用户: "请按照这个文档继续工作"
AI: 1. 自动检测TextUp项目当前状态
    2. 执行未完成的任务
    3. 更新文档进度
    4. 汇报执行结果
```

### 执行原则
- ✅ **状态驱动**: 基于实际检测状态决定执行内容
- ✅ **幂等执行**: 重复执行不会产生副作用
- ✅ **自动跳过**: 已完成任务自动跳过
- ✅ **进度更新**: 完成任务后自动更新checkbox

---

## 📊 自动化状态检测系统

### 🔍 环境状态检测
```bash
# 工作目录确认
cd /Volumes/mini_matrix/github/uploader/textup && pwd

# 环境检测
echo "=== TextUp 环境检测 ==="
python --version | grep "3\.[9-1][0-9]" && echo "✅ Python版本正确" || echo "❌ Python版本不符合要求"
uv --version >/dev/null 2>&1 && echo "✅ uv可用" || echo "❌ uv不可用"
uv run textup --help >/dev/null 2>&1 && echo "✅ TextUp CLI可启动" || echo "❌ CLI启动失败"
```

### 🔍 基础功能检测
```bash
# 基础测试状态
BASIC_TESTS=$(uv run pytest tests/test_working_features.py -q 2>/dev/null | grep -c "26 passed")
if [ "$BASIC_TESTS" = "1" ]; then
    echo "✅ BASIC_TESTS: COMPLETED"
else
    echo "❌ BASIC_TESTS: INCOMPLETE"
fi

# 导入错误检测
python -c "
try:
    from textup.utils.exceptions import PublishError
    from textup.cli.main import parse_config_value  
    from textup.services.error_handler import RetryPolicy
    print('✅ IMPORTS: COMPLETED')
except ImportError as e:
    print(f'❌ IMPORTS: INCOMPLETE - {e}')
" 2>/dev/null

# 语法错误检测
python -m py_compile tests/test_config_manager_service.py 2>/dev/null && echo "✅ SYNTAX: COMPLETED" || echo "❌ SYNTAX: INCOMPLETE"
```

### 🔍 测试覆盖率检测
```bash
# 当前覆盖率检测
COVERAGE=$(uv run pytest --cov=src/textup --cov-report=term-missing 2>/dev/null | grep "TOTAL" | awk '{print $4}' | sed 's/%//' || echo "0")
echo "当前测试覆盖率: ${COVERAGE}%"

# 阶段判断
if [ "$COVERAGE" -ge "80" ]; then
    echo "✅ PHASE_COMPLETE: 项目就绪"
elif [ "$COVERAGE" -ge "60" ]; then
    echo "🔄 PHASE3_READY: 质量保障阶段"
elif [ "$COVERAGE" -ge "45" ]; then
    echo "🔄 PHASE2_READY: 集成测试阶段"  
else
    echo "🔄 PHASE1_READY: 基础修复阶段"
fi
```

### 🔍 代码质量检测
```bash
# 代码质量状态
echo "=== 代码质量检测 ==="
uv run black src/ tests/ --check >/dev/null 2>&1 && echo "✅ BLACK_FORMAT: OK" || echo "❌ BLACK_FORMAT: FAIL"
uv run isort src/ tests/ --check-only >/dev/null 2>&1 && echo "✅ IMPORT_SORT: OK" || echo "❌ IMPORT_SORT: FAIL"  
uv run flake8 src/ tests/ >/dev/null 2>&1 && echo "✅ FLAKE8_LINT: OK" || echo "❌ FLAKE8_LINT: FAIL"
uv run mypy src/textup >/dev/null 2>&1 && echo "✅ TYPE_CHECK: OK" || echo "❌ TYPE_CHECK: FAIL"
```

---

## 🔄 自动化执行工作流

### Phase 1: 基础修复阶段

#### ✅ Task 1.1: 导入错误修复
**状态**: [ ] 待执行 / [x] 已完成  
**检测命令**: `python -c "from textup.utils.exceptions import PublishError; from textup.cli.main import parse_config_value; from textup.services.error_handler import RetryPolicy"`  
**执行条件**: 如果导入检测失败则执行  
**执行内容**:
```python
# 1. 修复 PublishError - 在 src/textup/utils/exceptions.py 添加
class PublishError(TextUpError):
    """发布相关错误"""
    def __init__(self, message: str, platform: Optional[str] = None, details: Optional[Dict] = None):
        super().__init__(message)
        self.platform = platform
        self.details = details or {}

# 2. 修复 parse_config_value - 在 src/textup/cli/main.py 添加
import yaml
from typing import Any

def parse_config_value(value: str) -> Any:
    """解析配置值，支持YAML格式"""
    try:
        return yaml.safe_load(value)
    except yaml.YAMLError:
        return value

# 3. 修复 RetryPolicy - 在 src/textup/services/error_handler.py 添加  
from dataclasses import dataclass

@dataclass
class RetryPolicy:
    max_attempts: int = 3
    base_delay: float = 1.0
    exponential_backoff: bool = True
    max_delay: float = 60.0
```
**验证命令**: `python -c "from textup.utils.exceptions import PublishError; from textup.cli.main import parse_config_value; from textup.services.error_handler import RetryPolicy; print('✅ 所有导入修复成功')"`  
**更新状态**: 成功后更新为 [x] 已完成

#### ✅ Task 1.2: 语法错误修复
**状态**: [ ] 待执行 / [x] 已完成  
**检测命令**: `python -m py_compile tests/test_config_manager_service.py`  
**执行条件**: 如果编译失败则执行  
**执行内容**: 
```bash
# 1. 检查具体语法错误
python -m py_compile tests/test_config_manager_service.py

# 2. 定位第799行错误（通常是字符串未闭合、括号不匹配等）
# 3. 修复语法错误（具体错误具体分析）
```
**验证命令**: `python -m py_compile tests/test_config_manager_service.py && echo "✅ 语法错误修复成功"`  
**更新状态**: 成功后更新为 [x] 已完成

#### ✅ Task 1.3: CLI基础功能验证
**状态**: [ ] 待执行 / [x] 已完成  
**检测命令**: `uv run textup config --help && uv run textup publish --help && uv run textup auth --help`  
**执行条件**: 如果任何命令失败则执行  
**执行内容**:
```bash
# 验证所有CLI子命令
echo "=== CLI功能验证 ==="
uv run textup --help
uv run textup config --help
uv run textup publish --help  
uv run textup auth --help
uv run textup status --help

# 基础配置测试
mkdir -p /tmp/textup_test && cd /tmp/textup_test
uv run textup config init
uv run textup config show
cd - && rm -rf /tmp/textup_test
```
**验证命令**: `uv run textup --help && uv run textup config --help && echo "✅ CLI功能验证通过"`  
**更新状态**: 成功后更新为 [x] 已完成

#### ✅ Task 1.4: 测试覆盖率提升至45%
**状态**: [ ] 待执行 / [x] 已完成  
**检测命令**: `COVERAGE=$(uv run pytest --cov=src/textup --cov-report=term-missing 2>/dev/null | grep "TOTAL" | awk '{print $4}' | sed 's/%//'); [ "$COVERAGE" -ge "45" ]`  
**执行条件**: 如果覆盖率 < 45% 则执行  
**执行内容**:
```python
# 1. CLI测试增强 - 在 tests/test_cli_comprehensive.py 添加
import pytest
from typer.testing import CliRunner
from textup.cli.main import app

runner = CliRunner()

def test_cli_help_command():
    """测试主帮助命令"""
    result = runner.invoke(app, ["--help"])
    assert result.exit_code == 0
    assert "textup" in result.output.lower()

def test_config_commands():
    """测试配置相关命令"""
    result = runner.invoke(app, ["config", "--help"])
    assert result.exit_code == 0
    
def test_publish_commands():
    """测试发布相关命令"""
    result = runner.invoke(app, ["publish", "--help"])
    assert result.exit_code == 0

# 2. 服务层测试增强 - 在 tests/test_services_comprehensive.py 添加
class TestConfigManager:
    def test_config_loading(self):
        """测试配置加载"""
        from textup.services.config_manager import ConfigManager
        config = ConfigManager()
        assert config is not None
        
    def test_environment_variables(self):
        """测试环境变量处理"""
        import os
        os.environ['TEXTUP_TEST'] = 'test_value'
        # 测试环境变量读取逻辑

class TestContentManager:
    def test_markdown_parsing(self):
        """测试Markdown解析"""
        from textup.services.content_manager import ContentManager
        manager = ContentManager()
        # 测试内容解析逻辑
```
**验证命令**: `uv run pytest --cov=src/textup --cov-report=term-missing | grep "TOTAL.*4[5-9]%\|TOTAL.*[5-9][0-9]%"`  
**更新状态**: 达到45%后更新为 [x] 已完成

### Phase 2: 集成测试阶段

#### ✅ Task 2.1: 创建集成测试框架
**状态**: [ ] 待执行 / [x] 已完成  
**检测命令**: `ls tests/test_integration_workflows.py >/dev/null 2>&1`  
**执行条件**: 如果集成测试文件不存在则执行  
**执行内容**:
```python
# 创建 tests/test_integration_workflows.py
import pytest
import asyncio
from textup.models import Content, ContentFormat
from textup.services.config_manager import ConfigManager
from textup.services.content_manager import ContentManager
from textup.services.publish_engine import PublishEngine

class TestEndToEndWorkflows:
    async def test_complete_publish_workflow(self):
        """测试完整发布工作流"""
        # 1. 创建测试内容
        content = Content(
            title="集成测试文章",
            content="# 测试\n\n这是集成测试内容。",
            content_format=ContentFormat.MARKDOWN
        )
        
        # 2. 配置管理测试
        config_manager = ConfigManager()
        
        # 3. 内容处理测试
        content_manager = ContentManager()
        processed_content = await content_manager.process_content(content)
        
        # 4. 发布引擎测试（Mock模式）
        publish_engine = PublishEngine()
        # Mock测试逻辑
        
        assert processed_content is not None
        
    async def test_error_recovery_workflow(self):
        """测试错误恢复工作流"""
        # 错误场景测试
        pass
        
    async def test_configuration_persistence(self):
        """测试配置持久化"""
        # 配置保存和加载测试
        pass
```
**验证命令**: `uv run pytest tests/test_integration_workflows.py -v`  
**更新状态**: 成功后更新为 [x] 已完成

#### ✅ Task 2.2: Mock测试框架建立
**状态**: [ ] 待执行 / [x] 已完成  
**检测命令**: `grep -q "@pytest.fixture" tests/conftest.py 2>/dev/null`  
**执行条件**: 如果Mock框架未建立则执行  
**执行内容**:
```python
# 完善 tests/conftest.py
import pytest
from unittest.mock import Mock, AsyncMock, patch
from textup.models import Content, ContentFormat

@pytest.fixture
def sample_content():
    """示例内容数据"""
    return Content(
        title="测试文章",
        content="# 测试标题\n\n测试内容正文",
        content_format=ContentFormat.MARKDOWN
    )

@pytest.fixture
def mock_zhihu_api():
    """Mock知乎API"""
    with patch('httpx.AsyncClient') as mock_client:
        mock_client.return_value.post.return_value.json.return_value = {
            'access_token': 'mock_token',
            'expires_in': 3600
        }
        yield mock_client

@pytest.fixture
def mock_weibo_api():
    """Mock微博API"""
    with patch('httpx.AsyncClient') as mock_client:
        mock_client.return_value.post.return_value.json.return_value = {
            'access_token': 'mock_weibo_token'
        }
        yield mock_client
```
**验证命令**: `grep -q "mock_zhihu_api\|mock_weibo_api" tests/conftest.py && echo "✅ Mock框架建立成功"`  
**更新状态**: 成功后更新为 [x] 已完成

#### ✅ Task 2.3: 测试覆盖率提升至60%
**状态**: [ ] 待执行 / [x] 已完成  
**检测命令**: `COVERAGE=$(uv run pytest --cov=src/textup --cov-report=term-missing 2>/dev/null | grep "TOTAL" | awk '{print $4}' | sed 's/%//'); [ "$COVERAGE" -ge "60" ]`  
**执行条件**: 如果覆盖率 < 60% 则执行  
**执行内容**: 补充适配器测试、异常处理测试等  
**验证命令**: `uv run pytest --cov=src/textup --cov-report=term-missing | grep "TOTAL.*6[0-9]%\|TOTAL.*[7-9][0-9]%"`  
**更新状态**: 达到60%后更新为 [x] 已完成

### Phase 3: 质量保障阶段

#### ✅ Task 3.1: 测试覆盖率冲刺至80%
**状态**: [ ] 待执行 / [x] 已完成  
**检测命令**: `COVERAGE=$(uv run pytest --cov=src/textup --cov-report=term-missing 2>/dev/null | grep "TOTAL" | awk '{print $4}' | sed 's/%//'); [ "$COVERAGE" -ge "80" ]`  
**执行条件**: 如果覆盖率 < 80% 则执行  
**执行内容**:
```bash
# 1. 生成详细覆盖率报告
uv run pytest --cov=src/textup --cov-report=html

# 2. 分析缺失覆盖的代码
# 重点补充: CLI层、服务层、适配器层的缺失测试

# 3. 边界条件和异常处理测试
# 4. 异步操作测试
```
**验证命令**: `uv run pytest --cov=src/textup --cov-report=term-missing | grep "TOTAL.*8[0-9]%\|TOTAL.*9[0-9]%"`  
**更新状态**: 达到80%后更新为 [x] 已完成

#### ✅ Task 3.2: 代码质量检查
**状态**: [ ] 待执行 / [x] 已完成  
**检测命令**: `uv run black src/ tests/ --check && uv run isort src/ tests/ --check-only && uv run flake8 src/ tests/ && uv run mypy src/textup`  
**执行条件**: 如果任何检查失败则执行  
**执行内容**:
```bash
# 1. 自动修复格式问题
uv run black src/ tests/
uv run isort src/ tests/

# 2. 修复代码风格问题
uv run flake8 src/ tests/ --show-source

# 3. 修复类型检查问题
uv run mypy src/textup --show-error-codes
```
**验证命令**: `uv run black src/ tests/ --check && uv run flake8 src/ tests/ && echo "✅ 代码质量检查通过"`  
**更新状态**: 全部通过后更新为 [x] 已完成

#### ✅ Task 3.3: 最终交付验证
**状态**: [ ] 待执行 / [x] 已完成  
**检测命令**: `uv run pytest tests/ -v && uv build && pip install dist/*.whl --dry-run`  
**执行条件**: 前置任务完成后执行  
**执行内容**:
```bash
# 1. 完整回归测试
uv run pytest tests/ -v --tb=short

# 2. 打包验证
uv build
ls -la dist/

# 3. 安装测试
pip install dist/*.whl --force-reinstall
textup --help
textup --version
pip uninstall textup -y

# 4. 文档更新
# 确保README.md、用户指南等文档是最新的
```
**验证命令**: `uv run pytest tests/ -q && uv build && echo "✅ 项目交付就绪"`  
**更新状态**: 验收通过后更新为 [x] 已完成

---

## 📊 进度跟踪系统

### Phase 1 进度 (基础修复)
- [ ] Task 1.1: 导入错误修复 ⏱️ 预计30分钟
- [ ] Task 1.2: 语法错误修复 ⏱️ 预计20分钟  
- [ ] Task 1.3: CLI功能验证 ⏱️ 预计30分钟
- [ ] Task 1.4: 测试覆盖率45% ⏱️ 预计90分钟
- [ ] Phase 1 完成验证

### Phase 2 进度 (集成测试)  
- [ ] Task 2.1: 集成测试框架 ⏱️ 预计60分钟
- [ ] Task 2.2: Mock测试框架 ⏱️ 预计45分钟
- [ ] Task 2.3: 测试覆盖率60% ⏱️ 预计120分钟  
- [ ] Phase 2 完成验证

### Phase 3 进度 (质量保障)
- [ ] Task 3.1: 测试覆盖率80% ⏱️ 预计90分钟
- [ ] Task 3.2: 代码质量检查 ⏱️ 预计30分钟
- [ ] Task 3.3: 最终交付验证 ⏱️ 预计45分钟
- [ ] 项目完成确认

---

## 🔄 AI自动文档更新机制

### 状态更新规则
```
# AI完成任务后自动更新格式:
更新前: - [ ] Task 1.1: 导入错误修复
更新后: - [x] Task 1.1: 导入错误修复 ✅ 完成: 2025-09-03 10:30

# 同时更新执行摘要
```

### 进度汇报格式
```markdown
## 📊 执行结果汇报 [2025-09-XX HH:MM]

### ✅ 已完成任务
- [x] 导入错误修复 ✅ 10:30 (用时30分钟)
- [x] 语法错误修复 ✅ 11:00 (用时20分钟)

### 🔄 正在执行  
- Task 1.3: CLI功能验证 (预计11:30完成)

### 📊 关键指标
- 测试覆盖率: 38% → 45% (目标: 45%)
- 测试通过: 82/90 (91.1%)
- 代码质量: ✅ 格式检查通过

### 🎯 下一步计划
- 继续CLI功能验证，预计30分钟完成
- 然后开始测试覆盖率提升工作

### ❓ 需要确认
- 无需用户确认，继续自动执行
```

---

## 🚨 自动化错误处理

### 错误检测与重试
```bash
# AI执行失败时的自动处理逻辑
if [ $? -ne 0 ]; then
    echo "❌ 任务执行失败: $CURRENT_TASK"
    echo "错误信息: $ERROR_OUTPUT"
    
    if [ $RETRY_COUNT -lt 3 ]; then
        echo "🔄 自动重试 ($((RETRY_COUNT+1))/3)"
        RETRY_COUNT=$((RETRY_COUNT+1))
        # 重新执行任务
    else
        echo "🚨 任务多次失败，需要用户介入"
        echo "建议检查: 依赖安装、权限设置、网络连接"
        # 升级给用户处理
    fi
fi
```

### 升级条件
- 同一任务连续失败3次
- 测试覆盖率连续2小时无提升
- 出现未知的系统级错误
- 需要外部资源或用户权限

---

## ⚡ 快速命令参考

### 一键状态检测
```bash
cd /Volumes/mini_matrix/github/uploader/textup && \
echo "=== TextUp 状态检测 ===" && \
python --version && uv --version && \
uv run textup --help >/dev/null 2>&1 && echo "✅ 环境正常" && \
COVERAGE=$(uv run pytest --cov=src/textup --cov-report=term-missing 2>/dev/null | grep "TOTAL" | awk '{print $4}' | sed 's/%//') && \
echo "当前覆盖率: ${COVERAGE}%" && \
python -c "from textup.utils.exceptions import PublishError; from textup.cli.main import parse_config_value; from textup.services.error_handler import RetryPolicy" 2>/dev/null && echo "✅ 导入正常" || echo "❌ 存在导入错误"
```

### 一键测试执行
```bash
uv run pytest tests/test_working_features.py -q && echo "✅ 基础测试通过" || echo "❌ 基础测试失败"
```

### 一键质量检查
```bash
uv run black src/ tests/ --check && uv run flake8 src/ tests/ && echo "✅ 代码质量检查通过" || echo "❌ 需要修复质量问题"
```

---

## 🎯 最终交付标准

### 必须达成的标准
- [x] 测试覆盖率 ≥ 80%
- [x] 所有导入错误修复完成
- [x] CLI命令完全可用且验证通过
- [x] 所有代码质量检查通过 (Black, Flake8, mypy)
- [x] 项目可正常打包和安装
- [x] 集成测试覆盖主要工作流程

### 可选增强标准
- [ ] 性能基准测试通过
- [ ] 用户文档完整详细
- [ ] 示例代码丰富实用
- [ ] 错误处理用户友好

---

## 📞 使用说明

### 🚀 启动自动执行
对AI说: **"请按照这个文档继续工作"**

### 🔍 检查进度
AI会每30分钟自动汇报进度，包括:
- 已完成的任务
- 正在执行的任务  
- 关键指标变化
- 遇到的问题
- 下一步计划

### ⚠️ 问题处理
AI会自动处理常见问题，遇到无法解决的问题时会:
1. 详细描述问题现象
2. 提供可能的解决方案
3. 请求用户确认或提供帮助

---

**文档状态**: ✅ 自动化执行就绪  
**预期完成**: 3天内完成所有任务  
**最终目标**: 测试覆盖率80%，项目生产就绪  
**AI承诺**: 完全自动化执行，用户只需要说"请按照这个文档继续工作"