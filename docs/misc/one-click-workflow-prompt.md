# 一键生成AI自动化工作流提示词

**使用方法**: 复制下方提示词，填入您的项目信息，发送给AI即可生成自动化工作流文档

---

## 🚀 一键提示词模板 (直接复制使用)

```
我需要你为我的项目创建一个AI自动化工作流文档，让我能够对AI说"请按照这个文档继续工作"，AI就能自动执行剩余任务并更新进度。

## 📋 项目信息
**项目名称**: {填写项目名称}
**技术栈**: {如：Python/JavaScript/Java/Go/React/Vue等}
**项目路径**: {填写项目根目录路径}
**当前阶段**: {开发中/测试中/部署准备中}

## 📊 当前状态
**代码完成度**: {如：80%}
**主要问题**: {列出当前需要解决的主要问题}
**目标和计划**: {1-3天内的目标}
**质量要求**: {测试覆盖率、代码质量等要求}

## 🏗️ 项目结构
{粘贴或描述主要目录结构，如：
src/
├── main/
├── test/  
└── docs/
}

请创建包含以下特性的AI自动化工作流文档：
1. 自动状态检测命令
2. 条件执行逻辑（if-then）
3. 任务进度跟踪（checkbox）
4. 自动文档更新
5. 错误处理和重试
6. 标准汇报格式

要求AI能够完全自动化执行，用户只需说"请按照文档继续工作"即可。
```

---

## 📝 快速填写示例

### Python项目示例
```
**项目名称**: MyPythonApp
**技术栈**: Python 3.9+, FastAPI, PostgreSQL, pytest
**项目路径**: /home/<USER>/my-python-app
**当前阶段**: 开发中
**代码完成度**: 75%
**主要问题**: 测试覆盖率只有40%，缺少API文档，部分功能未实现
**目标和计划**: 3天内完成开发，测试覆盖率达到80%，生成完整文档
**质量要求**: 测试覆盖率≥80%，通过所有质量检查，API文档完整
```

### JavaScript项目示例
```
**项目名称**: TaskManagerWeb
**技术栈**: React, Node.js, Express, MongoDB
**项目路径**: /Users/<USER>/task-manager
**当前阶段**: 测试中
**代码完成度**: 85%
**主要问题**: 前端测试不足，后端API需要优化，缺少E2E测试
**目标和计划**: 2天内完成所有测试，部署到生产环境
**质量要求**: 前后端测试覆盖率≥70%，性能测试通过
```

---

## ⚡ 超级简化版 (30秒填写)

```
创建AI自动化工作流文档：
项目：{项目名称}，{技术栈}
状态：代码{完成度}%，主要问题是{核心问题}
目标：{天数}天内{要完成的目标}
要求：测试覆盖率≥{百分比}%，{其他质量要求}

请生成让AI能"请按照文档继续工作"的自动化工作流文档。
```

**示例**:
```
创建AI自动化工作流文档：
项目：博客系统，Python + Django + PostgreSQL
状态：代码80%，主要问题是测试不足和文档缺失
目标：3天内完成开发，发布v1.0
要求：测试覆盖率≥75%，API文档完整

请生成让AI能"请按照文档继续工作"的自动化工作流文档。
```

---

## 🎯 使用步骤

1. **复制模板** - 选择上方任一模板复制
2. **填写信息** - 将{}中的内容替换为您的项目信息  
3. **发送给AI** - 将完整提示词发送给AI
4. **获得文档** - AI生成完整的自动化工作流文档
5. **开始自动执行** - 对AI说"请按照这个文档继续工作"

---

## ✅ 生成文档的特性

通过这个提示词生成的工作流文档将包含：

- ✅ **自动状态检测**: AI能自动判断项目当前状态
- ✅ **智能任务执行**: 只执行未完成的必要任务  
- ✅ **进度自动更新**: 任务完成后自动更新checkbox
- ✅ **质量保证机制**: 代码质量、测试、文档检查
- ✅ **错误自动处理**: 重试机制和问题升级
- ✅ **标准化汇报**: 清晰的执行结果和下一步计划

---

**使用承诺**: 5分钟填写信息，获得完整AI自动化工作流，实现真正的"一句话项目管理"！