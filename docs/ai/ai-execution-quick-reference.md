# AI执行任务快速参考清单

**执行期间**: 2025-09-03 至 2025-09-05  
**当前状态**: 代码90%完成，测试覆盖率33% → 目标80%  
**核心任务**: 修复错误 → 提升测试 → 完善质量  

---

## 🚨 第1天 (2025-09-03) - 紧急修复日

### ⚡ 上午任务 (09:00-12:00)

#### Step 1: 环境验证 (09:00-09:30)
```bash
cd uploader/textup
uv --version && python --version
uv run textup --help
uv run pytest tests/test_working_features.py -v  # 必须26个全过
uv run pytest tests/ --tb=short > error_baseline.log 2>&1
```

#### Step 2: 修复导入错误 (09:30-11:30)
**修复 PublishError**:
```python
# 在 src/textup/utils/exceptions.py 添加
class PublishError(TextUpError):
    """发布相关错误"""
    pass
```

**修复 parse_config_value**:
```python
# 在 src/textup/cli/main.py 添加
def parse_config_value(value: str) -> Any:
    """解析配置值"""
    try:
        return yaml.safe_load(value)
    except yaml.YAMLError:
        return value
```

**修复 RetryPolicy**:
```python
# 在 src/textup/services/error_handler.py 添加
class RetryPolicy:
    def __init__(self, max_attempts: int = 3, base_delay: float = 1.0):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
```

**修复语法错误**:
```bash
python -m py_compile tests/test_config_manager_service.py  # 修复第799行
```

#### Step 3: 验证修复 (11:30-12:00)
```bash
python -c "from textup.utils.exceptions import PublishError; from textup.cli.main import parse_config_value; from textup.services.error_handler import RetryPolicy; print('✅ 所有导入成功')"
uv run pytest tests/ --tb=short -v | head -50
```

### ⚡ 下午任务 (13:00-18:00)

#### Step 4: CLI测试增强 (13:00-14:00)
```python
# 在 tests/test_cli_comprehensive.py 添加
def test_cli_help_commands():
    result = runner.invoke(app, ["--help"])
    assert result.exit_code == 0

def test_config_init_command():
    result = runner.invoke(app, ["config", "init"])
    # 验证配置创建
```

#### Step 5: 服务层测试 (14:00-16:00)
```python
# 在 tests/test_services_comprehensive.py 添加
class TestConfigManager:
    def test_yaml_config_loading(self):
        # 配置加载测试
    def test_environment_override(self):
        # 环境变量测试
    def test_config_validation(self):
        # 配置验证测试

class TestContentManager:
    def test_markdown_parsing(self):
        # Markdown解析测试
    def test_metadata_extraction(self):
        # 元数据提取测试
```

#### Step 6: 适配器Mock测试 (16:00-17:00)
```python
# 在 tests/test_adapters_comprehensive.py 添加
class TestZhihuAdapter:
    @pytest.fixture
    def mock_zhihu_api(self):
        with patch('httpx.AsyncClient') as mock:
            yield mock
    
    async def test_oauth_authentication(self, mock_zhihu_api):
        # OAuth测试
    async def test_content_publish(self, mock_zhihu_api):
        # 发布测试
```

#### Step 7: 第1天验证 (17:00-18:00)
```bash
uv run pytest tests/ -v --tb=short
uv run pytest --cov=src/textup --cov-report=term-missing | grep "TOTAL.*%"
# 目标: 覆盖率 ≥ 45%, 大部分测试通过
```

**第1天成功标准**:
- [ ] 所有导入错误修复 ✓
- [ ] CLI基本功能可用 ✓
- [ ] 测试覆盖率 ≥ 45%
- [ ] 至少70%测试通过

---

## 🔧 第2天 (2025-09-04) - 集成测试日

### ⚡ 上午任务 (09:00-12:00)

#### Step 1: 创建集成测试 (09:00-10:30)
```python
# 创建 tests/test_integration_workflows.py
class TestEndToEndWorkflows:
    async def test_complete_publish_workflow(self):
        # 1. 创建内容
        content = Content(title="测试", content="# 测试\n内容")
        # 2. 配置管理
        config = ConfigManager()
        # 3. 内容处理
        content_manager = ContentManager()
        # 4. 发布引擎
        publish_engine = PublishEngine()
        # 5. 验证流程
        
    async def test_error_recovery_workflow(self):
        # 错误恢复测试
        
    async def test_multi_platform_workflow(self):
        # 多平台测试
```

#### Step 2: Mock框架完善 (10:30-12:00)
```python
# 在 tests/conftest.py 添加
@pytest.fixture
def mock_zhihu_api():
    with patch('httpx.AsyncClient') as mock:
        mock.return_value.post.return_value.json.return_value = {
            'access_token': 'test_token'
        }
        yield mock

@pytest.fixture
def sample_content():
    return Content(title="测试", content="# 测试内容")
```

### ⚡ 下午任务 (13:00-18:00)

#### Step 3: 用户体验优化 (13:00-15:00)
```python
# 在 src/textup/cli/main.py 优化
def config_init_interactive():
    console = Console()
    console.print("[bold blue]TextUp 配置向导[/bold blue]")
    # 使用rich.prompt进行交互

def handle_command_error(error: Exception):
    console = Console()
    if isinstance(error, ConfigError):
        console.print(f"[red]配置错误:[/red] {error.message}")
        console.print(f"[yellow]建议:[/yellow] 运行 'textup config init'")
```

#### Step 4: 异常路径测试 (15:00-17:00)
```python
class TestExceptionScenarios:
    def test_network_timeout_handling(self):
        # 网络超时测试
    def test_authentication_failure(self):
        # 认证失败测试
    def test_content_validation_errors(self):
        # 内容验证测试
```

#### Step 5: 第2天验证 (17:00-18:00)
```bash
uv run pytest tests/ -v --maxfail=10
uv run pytest --cov=src/textup --cov-report=term-missing | grep "TOTAL"
# 目标: 覆盖率 ≥ 60%
```

**第2天成功标准**:
- [ ] 测试覆盖率 ≥ 60%
- [ ] 集成测试覆盖主要流程
- [ ] CLI用户体验改善
- [ ] Mock测试框架建立

---

## 🎯 第3天 (2025-09-05) - 质量保障日

### ⚡ 上午任务 (09:00-12:00)

#### Step 1: 覆盖率冲刺 (09:00-10:00)
```bash
uv run pytest --cov=src/textup --cov-report=html
open htmlcov/index.html  # 分析缺失覆盖
# 重点补充: CLI层 30%→60%, 服务层 45%→75%, 适配器层 35%→65%
```

#### Step 2: 代码质量检查 (10:00-11:00)
```bash
uv run black src/ tests/ --check --diff
uv run black src/ tests/  # 自动修复
uv run isort src/ tests/ --check-only --diff
uv run isort src/ tests/  # 自动修复
uv run flake8 src/ tests/
uv run mypy src/textup
```

#### Step 3: 文档完善 (11:00-12:00)
```bash
# 创建/更新文档
# - README.md
# - docs/user-guide.md
# - docs/configuration.md
# - docs/troubleshooting.md
```

### ⚡ 下午任务 (13:00-18:00)

#### Step 4: 完整回归测试 (13:00-14:30)
```bash
uv run pytest tests/ -v --tb=short --maxfail=5
# 预期: 所有测试通过
uv run pytest --cov=src/textup --cov-report=term-missing --cov-report=html
# 预期: 覆盖率 ≥ 80%
```

#### Step 5: 生产环境测试 (14:30-15:30)
```bash
cd /tmp && mkdir textup_test && cd textup_test
pip install /path/to/textup/dist/*.whl
textup --help
textup config init
pip uninstall textup -y
```

#### Step 6: 发布准备 (15:30-17:00)
```bash
# 版本确认
grep -r "version" pyproject.toml src/textup/__init__.py
# 打包验证
uv build
ls -la dist/
# 依赖验证
uv export --format requirements.txt > requirements.txt
```

#### Step 7: 最终验证 (17:00-18:00)
```bash
# 质量检查清单
uv run pytest --cov=src/textup --cov-report=term-missing | grep "TOTAL.*8[0-9]%\|TOTAL.*9[0-9]%"
uv run black src/ tests/ --check
uv run flake8 src/ tests/
uv run mypy src/textup
# 功能验证
uv run textup --help
uv run textup config init
uv run textup config show
```

**第3天成功标准**:
- [ ] 测试覆盖率 ≥ 80%
- [ ] 所有代码质量检查通过
- [ ] 文档完整且最新
- [ ] 发布包正常工作

---

## 📊 每日检查点

### 🔍 每日18:00汇报格式
```
📊 进展汇报 [日期]
完成任务: [具体完成的任务]
测试覆盖率: [当前%] (目标: [目标%])
测试通过率: [通过数]/[总数] ([百分比]%)
遇到问题: [问题描述]
解决方案: [解决措施]
明日重点: [明日计划]
需要确认: [用户确认事项]
```

### 🚨 问题升级触发条件
- 任务卡住超过30分钟
- 覆盖率进展低于预期10%以上
- 出现无法修复的阻塞错误
- 计划需要重大调整

### ✅ 最终交付清单
- [ ] 测试覆盖率 ≥ 80%
- [ ] 所有导入错误修复
- [ ] CLI命令完全可用
- [ ] 代码质量检查通过
- [ ] 文档体系完整
- [ ] 可正常打包安装

---

## 🛠️ 常用命令速查

### 环境检查
```bash
cd uploader/textup
uv run textup --help
uv run pytest tests/test_working_features.py -v
```

### 测试运行
```bash
# 完整测试
uv run pytest tests/ -v --tb=short

# 覆盖率检查
uv run pytest --cov=src/textup --cov-report=term-missing

# HTML报告
uv run pytest --cov=src/textup --cov-report=html
```

### 代码质量
```bash
uv run black src/ tests/ --check
uv run isort src/ tests/ --check-only
uv run flake8 src/ tests/
uv run mypy src/textup
```

### 打包发布
```bash
uv build
pip install dist/*.whl
textup --help
```

---

**执行原则**: 严格按时间表 → 质量优先 → 及时汇报 → 问题升级
**成功标志**: 80%覆盖率 + 全功能可用 + 质量通过 + 文档完整