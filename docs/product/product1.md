# TextUp - 多平台文本内容发布工具

## 项目概述

### 1.1 项目简介
TextUp 是一个企业级的多平台文本内容自动化发布工具，旨在帮助内容创作者、营销团队和自媒体从业者实现文本内容在多个主流媒体平台的一键批量发布。

#### 🔍 **技术背景与设计理念**

**为什么需要多平台发布工具？**
- **平台碎片化现状**: 当今内容生态中，用户分散在知乎、微博、小红书、今日头条等不同平台，每个平台都有独特的用户群体和内容偏好
- **手动发布的痛点**: 传统的逐平台手动发布方式存在效率低下、易出错、时间成本高等问题
- **内容一致性挑战**: 手动操作容易导致不同平台间内容差异、发布时间不同步等问题

**TextUp 的技术解决方案**:
1. **统一内容管理**: 采用源-目标转换架构，单一内容源自动适配多平台格式
2. **异步并发发布**: 利用任务队列和并发控制，提高发布效率同时避免平台限制
3. **智能调度系统**: 基于平台特性和用户行为数据，优化发布时机
4. **容错与重试机制**: 处理网络异常、平台限制等各种异常情况

### 1.2 核心价值

#### 🚀 **效率提升**: 从手动逐平台发布提升到一键批量发布

**技术原理**:
- **并发执行模型**: 采用Worker Pool模式，不同平台的发布任务并行执行，理论上可将发布时间缩短至原来的 1/N（N为平台数量）
- **任务队列系统**: 使用Redis/Bull Queue实现任务的异步处理和状态管理，避免阻塞等待
- **批量操作优化**: 通过批量加载、批量验证等技术减少系统开销

**效率提升量化分析**:
```
传统手动方式: 4个平台 × 5分钟/平台 = 20分钟
TextUp自动化: 2分钟(准备) + 3分钟(并发发布) = 5分钟
效率提升: 300% (从20分钟缩短到5分钟)
```

#### 🔄 **内容一致性**: 确保同一内容在不同平台的统一性

**技术原理**:
- **单一数据源原则** (Single Source of Truth): 所有平台的内容都来源于同一个源文件，从源头保证一致性
- **版本控制系统**: 对每次内容变更进行版本管理，支持回滚和差异对比
- **原子性发布**: 采用事务模式，所有平台发布成功才标记为完成，部分失败时自动回滚

**一致性保障机制**:
```python
# 内容哈希验证
content_verification = {
    "content_hash": "sha256:abc123...",  # 内容指纹，确保数据完整性
    "platforms": {
        "zhihu": {"status": "published", "hash": "sha256:abc123..."},
        "weibo": {"status": "published", "hash": "sha256:abc123..."}
    }
}
```

#### ⏰ **时间管理**: 支持定时发布和智能调度

**技本原理**:
- **Cron-like 调度引擎**: 基于时间表达式，支持复杂的定时规则
- **延迟执行队列**: 使用Redis Sorted Set实现高精度的延迟任务调度
- **时区感知**: 支持全球时区，自动处理夏令时转换

**智能调度算法**:
```python
# 基于用户活跃度的最优发布时间计算
def calculate_optimal_time(platform, content_type, audience):
    user_activity = get_platform_activity_data(platform)
    engagement_scores = analyze_historical_performance(content_type)
    
    # 综合考虑用户活跃度、内容类型、竞争强度
    optimal_slots = user_activity * engagement_scores * competition_factor
    return max(optimal_slots, key=lambda x: x.score)
```

#### 🔄 **格式适配**: 自动适配不同平台的内容格式要求

**技术原理**:
- **策略模式设计**: 每个平台都有独立的格式处理策略，新平台易于扩展
- **管道化处理**: 内容经过多道处理流水线，每个阶段负责不同的转换任务
- **格式验证器**: 每个平台都有严格的格式校验，提前发现兼容性问题

**格式转换流水线**:
```
Markdown 源文件
    ↓
[解析器] → AST(抽象语法树)
    ↓
[平台适配器] → 特定平台格式
    ↓
[格式验证器] → 验证通过
    ↓
[发布器] → 发布到目标平台
```

### 1.3 技术栈选择

#### 🐍 **核心技术栈: Python 3.9+**

**为什么选择Python？**
- **生态丰富**: Python拥有强大的第三方库生态，特别适合快速开发和自动化任务
- **异步支持**: asyncio和aiohttp提供了优秀的异步编程支持，适合I/O密集型的多平台发布任务
- **Web自动化**: Playwright Python版本功能完整，适合小红书等需要Web自动化的平台
- **数据处理**: pandas、pydantic等库为内容处理和数据验证提供强力支持
- **部署简单**: 相比Node.js，Python应用部署更直接，无需编译步骤

**技术栈详细组成**:

| 领域 | 技术选择 | 版本要求 | 选择理由 |
|------|----------|----------|----------|
| **核心语言** | Python | 3.9+ | 现代语法支持，异步编程成熟 |
| **包管理** | Poetry | latest | 依赖管理更清晰，虚拟环境隔离 |
| **Web框架** | FastAPI | 0.104+ | 高性能，自动API文档生成 |
| **异步HTTP** | aiohttp | 3.9+ | 异步请求，适合多平台并发 |
| **数据验证** | Pydantic | 2.5+ | 类型安全，自动验证 |
| **CLI框架** | Click/Typer | latest | 用户友好的命令行界面 |
| **Web自动化** | Playwright | 1.40+ | 跨浏览器支持，反爬能力强 |
| **数据库** | SQLite/PostgreSQL | - | 轻量级本地存储+生产级数据库 |
| **缓存** | Redis | 7.0+ | 高性能缓存和任务队列 |
| **测试框架** | pytest | 7.4+ | 功能强大，插件丰富 |
| **代码质量** | Black + Flake8 + isort | latest | 代码格式化和静态检查 |
| **加密** | cryptography | 41.0+ | 凭证安全存储 |
| **日志** | structlog | latest | 结构化日志记录 |

**核心依赖包**:
```python
# 核心功能
click>=8.1.7              # CLI框架
pydantic>=2.5.2           # 数据验证
aiohttp>=0.25.2           # 异步HTTP客户端
playwright>=1.40.0        # Web自动化

# 内容处理
markdown>=3.5.1           # Markdown解析
python-frontmatter>=1.0.0 # 元数据提取
pyyaml>=6.0.1             # YAML配置

# 安全和加密
cryptography>=41.0.8      # 凭证加密

# 工具库
rich>=13.7.0              # 美化输出
typer>=0.9.0              # 类型友好的CLI
schedule>=1.2.1           # 任务调度
```

### 1.4 目标用户
- 自媒体内容创作者
- 企业品牌营销团队
- 社交媒体运营人员
- 内容分发机构

## 系统架构

### 2.1 整体架构

#### 🏠 **分层架构设计理念**

**为什么采用分层架构？**
- **关注点分离**: 每一层只关注自己的职责，降低系统复杂度
- **可维护性**: 修改某一层不会影响其他层，提高系统稳定性
- **可扩展性**: 新增功能或平台时只需修改对应层级

```
┌───────────────────────────────────────────────────────────────┐
│                    TextUp 系统架构                           │
├───────────────────────────────────────────────────────────────┤
│  Web UI / CLI / API Gateway                                 │
├───────────────────────────────────────────────────────────────┤
│  Core Service Layer                                         │
│  ┌─────────────┐┌─────────────┐┌─────────────┐┌─────────────┐  │
│  │ Content     ││ Platform    ││ Schedule    ││ Config      │  │
│  │ Manager     ││ Adapter     ││ Engine      ││ Manager     │  │
│  └─────────────┘└─────────────┘└─────────────┘└─────────────┘  │
├───────────────────────────────────────────────────────────────┤
│  Platform Integration Layer                                 │
│  ┌─────────┐┌─────────┐┌─────────┐┌─────────┐┌─────────────┐    │
│  │ 知乎    ││ 微博    ││ 小红书  ││ 头条    ││ 自定义平台  │    │
│  │ Plugin  ││ Plugin  ││ Plugin  ││ Plugin  ││ Plugin      │    │
│  └─────────┘└─────────┘└─────────┘└─────────┘└─────────────┘    │
├───────────────────────────────────────────────────────────────┤
│  Data Layer                                                 │
│  ┌─────────────┐┌─────────────┐┌─────────────┐┌─────────────┐  │
│  │ Content DB  ││ Config DB   ││ Log DB      ││ Cache Layer │  │
│  └─────────────┘└─────────────┘└─────────────┘└─────────────┘  │
└───────────────────────────────────────────────────────────────┘
```

#### 🔄 **数据流转原理**

**请求处理流程**:
```
请求 → API Gateway → 服务层 → 集成层 → 数据层 → 响应
```

**关键设计原则**:
- **单一职责**: 每个模块只负责一个功能领域
- **依赖倒置**: 高层依赖抽象接口，不依赖具体实现
- **开放封闭**: 对扩展开放，对修改封闭
```
┌─────────────────────────────────────────────────────────────┐
│                    TextUp 系统架构                           │
├─────────────────────────────────────────────────────────────┤
│  Web UI / CLI / API Gateway                                 │
├─────────────────────────────────────────────────────────────┤
│  Core Service Layer                                         │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ Content     │ Platform    │ Schedule    │ Config      │  │
│  │ Manager     │ Adapter     │ Engine      │ Manager     │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  Platform Integration Layer                                 │
│  ┌─────────┬─────────┬─────────┬─────────┬─────────────┐    │
│  │ 知乎    │ 微博    │ 小红书  │ 头条    │ 自定义平台  │    │
│  │ Plugin  │ Plugin  │ Plugin  │ Plugin  │ Plugin      │    │
│  └─────────┴─────────┴─────────┴─────────┴─────────────┘    │
├─────────────────────────────────────────────────────────────┤
│  Data Layer                                                 │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ Content DB  │ Config DB   │ Log DB      │ Cache Layer │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心模块设计

#### 2.2.1 Content Manager (内容管理器)
**职责**: 文本内容的解析、处理、格式转换和存储
**核心类**:
```python
from typing import Protocol
from pydantic import BaseModel

class ContentManager(Protocol):
    async def parse_content(self, source: ContentSource) -> ParsedContent:
        """Parse content from source"""
        ...
    
    async def validate_content(self, content: ParsedContent) -> ValidationResult:
        """Validate content"""
        ...
    
    async def transform_content(self, content: ParsedContent, platform: Platform) -> TransformedContent:
        """Transform content for specific platform"""
        ...
    
    async def store_content(self, content: ParsedContent) -> str:
        """Store content and return content ID"""
        ...
```

#### 2.2.2 Platform Adapter (平台适配器)
**职责**: 各平台的API封装、认证管理、发布逻辑
**核心接口**:
```python
from typing import Protocol, Dict, Any, List
from abc import abstractmethod

class PlatformAdapter(Protocol):
    @abstractmethod
    async def authenticate(self, credentials: Dict[str, Any]) -> AuthResult:
        """Authenticate with platform"""
        ...
    
    @abstractmethod
    async def publish(self, content: TransformedContent, options: PublishOptions) -> PublishResult:
        """Publish content to platform"""
        ...
    
    @abstractmethod
    async def validate_format(self, content: Content) -> FormatValidationResult:
        """Validate content format for platform"""
        ...
    
    @abstractmethod
    def get_supported_formats(self) -> List[ContentFormat]:
        """Get supported content formats"""
        ...
```

#### 2.2.3 Schedule Engine (调度引擎)
**职责**: 定时任务管理、发布策略执行、重试机制
**核心类**:
```python
from typing import Protocol
from datetime import datetime
import asyncio

class ScheduleEngine(Protocol):
    async def schedule_publish(self, task: PublishTask) -> str:
        """Schedule a publish task"""
        ...
    
    async def execute_task(self, task_id: str) -> ExecutionResult:
        """Execute a scheduled task"""
        ...
    
    async def retry_failed_task(self, task_id: str, retry_policy: RetryPolicy) -> None:
        """Retry a failed task"""
        ...
    
    async def get_task_status(self, task_id: str) -> TaskStatus:
        """Get task status"""
        ...
```

## 功能需求规范

### 3.1 核心功能

#### 3.1.1 多平台发布
**功能描述**: 支持同一内容同时发布到多个平台
**支持平台**:
- 知乎 (API + Web自动化)
- 微博 (API + Web自动化)  
- 小红书 (Web自动化)
- 今日头条 (API + Web自动化)

**实现要求**:
```python
from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
from enum import Enum

class TaskStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class Platform(str, Enum):
    ZHIHU = "zhihu"
    WEIBO = "weibo"
    XIAOHONGSHU = "xiaohongshu"
    TOUTIAO = "toutiao"

class PublishTask(BaseModel):
    id: str = Field(description="任务唯一标识")
    content: Content = Field(description="要发布的内容")
    platforms: List[Platform] = Field(description="目标平台列表")
    publish_time: Optional[datetime] = Field(None, description="定时发布时间")
    tags: List[str] = Field(default_factory=list, description="标签列表")
    title: str = Field(description="内容标题")
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="任务状态")

class PlatformConfig(BaseModel):
    platform_id: str = Field(description="平台标识")
    credentials: dict = Field(description="认证凭证")
    publish_settings: dict = Field(description="发布设置")
    is_enabled: bool = Field(default=True, description="是否启用")
```

#### 3.1.2 内容格式转换
**功能描述**: 自动将源格式转换为目标平台支持的格式
**支持转换**:
- Markdown → HTML
- Markdown → PDF  
- Markdown → DOCX
- Plain Text → Rich Text
- 图片压缩和尺寸调整

**实现接口**:
```python
from typing import Protocol, Optional, List
from io import BytesIO

class ContentConverter(Protocol):
    def convert_markdown_to_html(self, markdown: str, options: Optional[dict] = None) -> str:
        """Convert markdown to HTML"""
        ...
    
    def convert_markdown_to_pdf(self, markdown: str, options: Optional[dict] = None) -> BytesIO:
        """Convert markdown to PDF"""
        ...
    
    def convert_markdown_to_docx(self, markdown: str, options: Optional[dict] = None) -> BytesIO:
        """Convert markdown to DOCX"""
        ...
    
    def optimize_images(self, images: List[dict], platform: Platform) -> List[dict]:
        """Optimize images for platform"""
        ...
```

#### 3.1.3 发布策略管理
**功能描述**: 支持多种发布策略和时间管理
**策略类型**:
- 立即发布
- 定时发布
- 轮询发布 (按时间间隔)
- 智能发布 (基于最佳发布时间)

**配置结构**:
```python
from enum import Enum
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime, timedelta

class PublishStrategy(str, Enum):
    IMMEDIATE = 'immediate'
    SCHEDULED = 'scheduled'
    POLLING = 'polling'
    INTELLIGENT = 'intelligent'

class PublishStrategyConfig(BaseModel):
    strategy: PublishStrategy
    schedule_time: Optional[datetime] = None
    polling_interval: Optional['Duration'] = None
    intelligent_rules: Optional['IntelligentPublishRules'] = None
    retry_policy: 'RetryPolicy'

# 智能发布规则
class IntelligentPublishRules(BaseModel):
    target_audience: str = Field(description="目标受众：general, professional, students, workers")
    content_type: str = Field(description="内容类型：tech, lifestyle, entertainment, education")
    optimize_for: str = Field(description="优化目标：engagement, reach, conversion")
    time_zone: str = Field(description="时区")
    custom_time_slots: Optional[List['TimeSlot']] = None

class TimeSlot(BaseModel):
    platform: Platform
    day_of_week: int = Field(description="0-6, 0=周日")
    start_hour: int = Field(description="0-23")
    end_hour: int = Field(description="0-23")
    priority: int = Field(description="1-10")

class RetryPolicy(BaseModel):
    max_attempts: int
    base_delay_ms: int
    backoff_multiplier: float
    max_delay_ms: int
    retryable_errors: List[str]

class Duration(BaseModel):
    value: int
    unit: str = Field(description="seconds, minutes, hours, days")
```

### 3.4 智能发布算法

#### 3.4.1 最佳发布时间算法
```python
from typing import Dict, List, NamedTuple

class TimeRange(NamedTuple):
    start: int
    end: int

class IntelligentPublishEngine:
    OPTIMAL_TIMES: Dict[str, Dict[str, List[TimeRange]]] = {
        'zhihu': {
            'weekday': [TimeRange(9, 11), TimeRange(20, 22)],
            'weekend': [TimeRange(10, 12), TimeRange(15, 17)]
        },
        'weibo': {
            'weekday': [TimeRange(8, 10), TimeRange(12, 14), TimeRange(18, 20)],
            'weekend': [TimeRange(10, 12), TimeRange(14, 16), TimeRange(19, 21)]
        },
        'xiaohongshu': {
            'weekday': [TimeRange(18, 21)],
            'weekend': [TimeRange(10, 12), TimeRange(14, 18)]
        },
        'toutiao': {
            'weekday': [TimeRange(7, 9), TimeRange(11, 13), TimeRange(17, 19)],
            'weekend': [TimeRange(9, 11), TimeRange(14, 16)]
        }
    }
    def calculate_optimal_time(
        self,
        platform: Platform,
        content_type: str,
        target_audience: str
    ) -> datetime:
        """Calculate optimal publish time"""
        now = datetime.now()
        tomorrow = now + timedelta(days=1)
        is_weekend = tomorrow.weekday() >= 5  # Saturday=5, Sunday=6
        
        time_slots = (
            self.OPTIMAL_TIMES[platform.value]['weekend'] 
            if is_weekend 
            else self.OPTIMAL_TIMES[platform.value]['weekday']
        )
        
        # 根据内容类型和目标受众调整时间
        adjusted_slots = self._adjust_for_audience(time_slots, target_audience, content_type)
        
        # 选择最优时间段
        best_slot = adjusted_slots[0]
        optimal_hour = (best_slot.start + best_slot.end) // 2
        
        optimal_time = tomorrow.replace(hour=optimal_hour, minute=0, second=0, microsecond=0)
        return optimal_time
    
    def _adjust_for_audience(
        self,
        slots: List[TimeRange],
        audience: str,
        content_type: str
    ) -> List[TimeRange]:
        """根据受众特征调整发布时间"""
        if audience == 'students':
            # 学生群体晚上和周末更活跃
            return [slot for slot in slots if slot.start >= 18 or slot.end <= 12]
        
        if audience == 'workers':
            # 上班族中午和晚上更活跃
            return [slot for slot in slots if 
                   (slot.start >= 11 and slot.end <= 14) or 
                   (slot.start >= 18 and slot.end <= 22)]
        
        return slots

### 3.5 配置管理

#### 3.5.1 配置文件结构
```yaml
# textup.config.yaml
project:
  name: "My Content Project"
  version: "1.0.0"
  
content:
  source_directory: "./content"
  supported_formats: ["md", "txt", "html"]
  default_tags: ["原创", "分享"]
  
platforms:
  zhihu:
    enabled: true
    auth_method: "oauth"
    credentials_file: "./credentials/zhihu.json"
    default_column: "技术分享"
    
  weibo:
    enabled: true
    auth_method: "cookie"
    credentials_file: "./credentials/weibo.json"
    
  xiaohongshu:
    enabled: false
    reason: "待开发"
    
  toutiao:
    enabled: true
    auth_method: "api_key"
    credentials_file: "./credentials/toutiao.json"

schedule:
  default_strategy: "scheduled"
  timezone: "Asia/Shanghai"
  retry_attempts: 3
  retry_delay: "5m"
  
logging:
  level: "info"
  output: "./logs/textup.log"
  rotate: true
```

#### 3.5.2 环境变量支持
```bash
# 优先级: 环境变量 > 命令行参数 > 配置文件 > 默认值
TEXTUP_CONFIG_PATH="./config/textup.yaml"
TEXTUP_CONTENT_DIR="./content"
TEXTUP_LOG_LEVEL="debug"
TEXTUP_ZHIHU_TOKEN="xxx"
TEXTUP_WEIBO_COOKIES="xxx"
```

### 3.6 命令行接口

#### 3.6.1 基础命令

##### 🎆 **textup init** - 项目初始化

```bash
textup init
```

**执行原理**:
- 检测当前目录是否已存在配置文件
- 创建默认的 `textup.config.yaml` 配置文件
- 初始化必要的目录结构（content, logs, credentials）
- 生成示例内容和模板文件

**最佳实践**:
- 在项目根目录执行，确保权限正确
- 初始化后立即配置平台凭证，避免后续错误

##### 📝 **textup publish** - 内容发布

```bash
# 基础发布
textup publish ./content/article.md --platforms zhihu,weibo

# 批量发布
textup publish ./content/ --recursive

# 定时发布
textup publish ./article.md --schedule "2024-01-01 10:00:00"
```

**参数详解**:
- `--platforms`: 目标平台列表，支持 zhihu,weibo,xiaohongshu,toutiao
- `--recursive`: 递归处理目录下所有文件
- `--schedule`: ISO 8601 格式的时间戳，支持时区
- `--tags`: 自定义标签，覆盖文件中的默认标签
- `--dry-run`: 模拟执行，不实际发布

**执行原理**:
```
1. 文件解析: 读取并解析 Markdown/HTML 文件
2. 内容验证: 检查内容格式、长度、必要字段
3. 任务创建: 生成发布任务并入队
4. 并发执行: 各平台并行发布
5. 状态跟踪: 实时反馈发布进度和结果
```

**错误处理**:
- 文件不存在: 提示正确的文件路径
- 平台未认证: 自动引导到认证流程
- 格式错误: 提供具体的修正建议

##### 🔍 **textup status** - 状态查询

```bash
# 查看所有任务
textup status

# 查看特定任务
textup status --task-id task_123

# 实时监控
textup status --watch
```

**输出格式**:
```
┌────────────┐┌────────────────┐┌────────────┐┌────────────┐
│ Task ID    │ Status           │ Platforms    │ Progress     │
├────────────┼────────────────┼────────────┼────────────┤
│ task_123   │ 🟢 Publishing    │ zhihu,weibo  │ ■■■□□ 60%   │
│ task_124   │ ✅ Completed     │ xiaohongshu  │ ■■■■■ 100%  │
└────────────┘└────────────────┘└────────────┘└────────────┘
```

##### 🔑 **textup auth** - 平台认证

```bash
# 登录平台
textup auth login zhihu
textup auth login weibo --method cookie

# 查看认证状态
textup auth status

# 退出登录
textup auth logout weibo
```

**认证原理**:
- **OAuth 2.0**: 知乎、微博等平台支持标准 OAuth 流程
- **Cookie 认证**: 小红书等平台使用浏览器 Cookie
- **API Key**: 今日头条等平台使用 API 密钥

**安全措施**:
- 凭证加密存储，使用 AES-256 算法
- 支持凭证过期检测和自动刷新
- 提供凭证退出和清理功能

##### ⚙️ **textup config** - 配置管理

```bash
# 查看配置
textup config get platforms.zhihu
textup config list

# 设置配置
textup config set platforms.zhihu.enabled true
textup config set default_tags "[原创,技术]"

# 验证配置
textup config validate
```

**配置层级** (优先级从高到低):
1. 命令行参数
2. 环境变量
3. 项目配置文件 (textup.config.yaml)
4. 全局配置文件 (~/.textup/config.yaml)
5. 默认值

**配置校验**:
- JSON Schema 验证配置格式
- 检测冲突和无效值
- 提供修复建议和示例

#### 3.6.2 高级功能
```bash
# 预览转换后的内容
textup preview ./article.md --platform zhihu

# 内容格式转换
textup convert ./article.md --output-format pdf --output ./output/

# 批量操作
textup batch-publish ./content/ --config ./batch-config.yaml

# 模板管理
textup template create article-template
textup template apply article-template ./new-article.md
```

## 技术实现规范

### 4.1 技术栈选择

#### 4.1.1 后端技术栈
- **运行环境**: Python 3.9+
- **框架**: FastAPI / Flask
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **缓存**: Redis
- **任务队列**: Celery / asyncio
- **Web自动化**: Playwright
- **文件格式**: 
  - Markdown: python-markdown / mistune
  - PDF: WeasyPrint / ReportLab
  - DOCX: python-docx
- **CLI框架**: Click / Typer
- **数据验证**: Pydantic
- **HTTP客户端**: httpx / requests
- **配置管理**: PyYAML / python-dotenv
- **加密**: cryptography
- **日志**: loguru / logging

#### 4.1.2 开发工具和辅助工具
- **包管理**: Poetry / pip
- **代码格式化**: Black + isort
- **代码检查**: Flake8 + mypy
- **测试框架**: pytest + pytest-cov
- **文档生成**: Sphinx / MkDocs
- **构建工具**: setuptools / Poetry build
- **CI/CD**: GitHub Actions

### 4.2 数据模型设计

#### 4.2.1 内容模型
```sql
-- 内容表
CREATE TABLE contents (
  id UUID PRIMARY KEY,
  title VARCHAR(500) NOT NULL,
  content TEXT NOT NULL,
  content_format VARCHAR(20) DEFAULT 'markdown',
  source_file_path VARCHAR(1000),
  tags JSON,
  metadata JSON,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 发布任务表
CREATE TABLE publish_tasks (
  id UUID PRIMARY KEY,
  content_id UUID REFERENCES contents(id),
  platforms JSON NOT NULL, -- ['zhihu', 'weibo']
  publish_strategy VARCHAR(20) DEFAULT 'immediate',
  scheduled_time TIMESTAMP,
  status VARCHAR(20) DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 发布记录表  
CREATE TABLE publish_records (
  id UUID PRIMARY KEY,
  task_id UUID REFERENCES publish_tasks(id),
  platform VARCHAR(50) NOT NULL,
  platform_post_id VARCHAR(200),
  status VARCHAR(20) DEFAULT 'pending',
  error_message TEXT,
  published_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

-- 平台配置表
CREATE TABLE platform_configs (
  id UUID PRIMARY KEY,
  platform VARCHAR(50) NOT NULL,
  user_id VARCHAR(100),
  config JSON NOT NULL,
  credentials_encrypted TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### 4.2.2 核心数据类型
```python
# Python Pydantic 模型定义
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum

class ContentFormat(str, Enum):
    MARKDOWN = 'markdown'
    HTML = 'html'
    TEXT = 'text'
    PDF = 'pdf'
    DOCX = 'docx'

class Platform(str, Enum):
    ZHIHU = 'zhihu'
    WEIBO = 'weibo'
    XIAOHONGSHU = 'xiaohongshu'
    TOUTIAO = 'toutiao'

class TaskStatus(str, Enum):
    PENDING = 'pending'
    RUNNING = 'running'
    COMPLETED = 'completed'
    FAILED = 'failed'
    CANCELLED = 'cancelled'

class PublishStatus(str, Enum):
    PENDING = 'pending'
    PUBLISHING = 'publishing'
    PUBLISHED = 'published'
    FAILED = 'failed'

class Content(BaseModel):
    id: str
    title: str
    content: str
    content_format: ContentFormat = ContentFormat.MARKDOWN
    source_file_path: Optional[str] = None
    tags: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: datetime
    updated_at: datetime

class PublishTask(BaseModel):
    id: str
    content_id: str
    platforms: List[Platform]
    publish_strategy: PublishStrategy
    scheduled_time: Optional[datetime] = None
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime
    updated_at: datetime

class PublishRecord(BaseModel):
    id: str
    task_id: str
    platform: Platform
    platform_post_id: Optional[str] = None
    status: PublishStatus = PublishStatus.PENDING
    error_message: Optional[str] = None
    published_at: Optional[datetime] = None
    created_at: datetime

# 平台特定模型
class ZhihuCredentials(BaseModel):
    access_token: str
    refresh_token: str
    user_id: str

class ZhihuPublishOptions(BaseModel):
    type: str = Field(description="article or answer")
    column_id: Optional[str] = None
    question_id: Optional[str] = None
    visibility: str = Field(default="public", description="public or private")

class WeiboCredentials(BaseModel):
    access_token: str
    uid: str
    cookies: Optional[str] = None

class WeiboPublishOptions(BaseModel):
    visibility: int = Field(default=0, description="0:公开 1:好友 2:自己")
    location: Optional[str] = None
    lat: Optional[float] = None
    long: Optional[float] = None

class XhsCredentials(BaseModel):
    cookies: str
    user_id: str

class XhsPublishOptions(BaseModel):
    content_type: str = Field(description="note or video")
    images: Optional[List[str]] = None
    location: Optional[str] = None

class ToutiaoCredentials(BaseModel):
    app_id: str
    secret: str

class ToutiaoPublishOptions(BaseModel):
    category: str
    original_url: Optional[str] = None
    draft_id: Optional[str] = None

# 通用模型
class TransformedContent(BaseModel):
    title: str
    content: str
    html: str
    text: str
    tags: List[str] = Field(default_factory=list)
    images: Optional[List[str]] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)

class PublishResult(BaseModel):
    success: bool
    platform_post_id: Optional[str] = None
    publish_url: Optional[str] = None
    error_message: Optional[str] = None

class AuthResult(BaseModel):
    success: bool
    token: Optional[str] = None
    error_message: Optional[str] = None

class ValidationError(BaseModel):
    field: str
    message: str
    level: str = Field(default="error", description="error or warning")

class ValidationResult(BaseModel):
    is_valid: bool
    errors: List[ValidationError] = Field(default_factory=list)

class FormatValidationResult(BaseModel):
    is_valid: bool
    errors: List[str] = Field(default_factory=list)

class ProcessedWeiboContent(BaseModel):
    text: str
    needs_split: bool = False
    full_text_url: Optional[str] = None
    image_ids: Optional[List[str]] = None
```

### 4.3 平台集成实现

#### 🔍 **平台集成策略分析**

**为什么需要不同的集成方式？**

| 平台 | 集成方式 | 选择原因 | 技术挑战 |
|------|------------|----------|----------|
| 知乎 | API + OAuth | 官方API稳定，功能完善 | 限流严格，需要企业认证 |
| 微博 | API + Cookie | API支持丰富，但需备用方案 | 用户认证复杂 |
| 小红书 | Web自动化 | 无公开API，只能模拟用户操作 | 反爬机制，页面变化 |
| 今日头条 | 开放平台API | 企业级API，稳定可靠 | 审核机制严格 |

#### 4.3.1 知乎平台集成

**技术方案**: OAuth 2.0 + RESTful API

```typescript
class ZhihuAdapter implements PlatformAdapter {
  private readonly API_BASE = 'https://www.zhihu.com/api/v4'
  private readonly RATE_LIMIT = {
    requests_per_hour: 1000,    // 每小时请求限制
    articles_per_day: 10,       // 每日发文限制
    concurrent_requests: 2      // 并发请求限制
  }
  
  async authenticate(credentials: ZhihuCredentials): Promise<AuthResult> {
    // OAuth 2.0 三步认证流程
    // 1. 获取授权码 (Authorization Code)
    const authUrl = `${this.API_BASE}/oauth/authorize?` +
      `client_id=${credentials.clientId}&` +
      `response_type=code&` +
      `redirect_uri=${credentials.redirectUri}&` +
      `scope=publish_article`
    
    // 2. 用户授权后获取 Access Token
    const tokenResponse = await this.exchangeCodeForToken(credentials.authCode)
    
    // 3. 验证 Token 有效性
    const userInfo = await this.validateToken(tokenResponse.access_token)
    
    return {
      success: true,
      token: tokenResponse.access_token,
      refreshToken: tokenResponse.refresh_token,
      expiresAt: new Date(Date.now() + tokenResponse.expires_in * 1000),
      userInfo
    }
  }
  
  async publish(content: TransformedContent, options: ZhihuPublishOptions): Promise<PublishResult> {
    // 限流控制: 使用令牌桶算法
    await this.rateLimiter.acquire()
    
    try {
      const endpoint = options.type === 'article' 
        ? `${this.API_BASE}/articles` 
        : `${this.API_BASE}/answers`
        
      const payload = {
        title: content.title,
        content: this.formatContentForZhihu(content.html),
        column_id: options.columnId,
        tags: content.tags,
        // 知乎特有字段
        excerpt: this.generateExcerpt(content.content),
        publish_time: options.publishTime || Date.now(),
        is_original: true
      }
      
      const response = await this.makeAuthenticatedRequest('POST', endpoint, payload)
      
      return {
        success: true,
        platformPostId: response.id,
        publishUrl: `https://zhuanlan.zhihu.com/p/${response.id}`,
        metrics: {
          publishTime: new Date(),
          estimatedReach: this.calculateEstimatedReach(content)
        }
      }
    } catch (error) {
      // 知乎特定错误处理
      if (error.code === 'DAILY_LIMIT_EXCEEDED') {
        throw new PlatformError('zhihu', 'RATE_LIMIT', '今日发文数量已达上限', true)
      }
      throw error
    }
  }
  
  private formatContentForZhihu(html: string): string {
    // 知乎特定的HTML格式处理
    return html
      .replace(/<h1>/g, '<h2>')              // H1 转 H2，知乎不支持 H1
      .replace(/<\/h1>/g, '</h2>')
      .replace(/<code>/g, '<code class="highlight">')  // 添加高亮样式
      .replace(/\n\n/g, '<br><br>')         // 段落间距处理
  }
}
```

**知乎集成的最佳实践**:
- **用户体验优化**: 提供専栏选择、文章分类功能
- **SEO 优化**: 自动生成摘要和关键词
- **限流应对**: 智能错峰发布，避开高峰期
```

#### 4.3.2 微博平台集成
```python
import aiohttp
from typing import Dict, Any

class WeiboAdapter:
    def __init__(self):
        self.api_base = 'https://api.weibo.com/2'
    
    async def publish(self, content: TransformedContent, options: WeiboPublishOptions) -> PublishResult:
        """微博字数限制处理"""
        processed_content = self._process_weibo_content(content.text)
        
        payload = {
            'status': processed_content.text,
            'pic_id': ','.join(processed_content.image_ids) if processed_content.image_ids else None,
            'visible': options.visibility or 0
        }
        
        return await self._make_request('POST', f'{self.api_base}/statuses/share.json', payload)
    
    def _process_weibo_content(self, text: str) -> ProcessedWeiboContent:
        """处理微博140字限制，自动分割长文本"""
        if len(text) <= 140:
            return ProcessedWeiboContent(text=text, needs_split=False)
        
        return ProcessedWeiboContent(
            text=text[:137] + '...',
            needs_split=True,
            full_text_url=self._upload_long_text(text)
        )
```

#### 4.3.3 小红书平台集成 (基于Web自动化)

**技术方案**: Playwright + 智能反爬策略

**为什么选择Web自动化？**
- 小红书没有公开的API接口
- 第三方API存在法律风险和稳定性问题
- Web自动化能更真实地模拟用户操作

```python
import asyncio
import random
from playwright.async_api import Browser, Page
from typing import List, Dict

class XiaohongshuAdapter:
    def __init__(self, browser: Browser):
        self.browser = browser
        # 智能选择器策略，适应页面变化
        self.selectors = {
            'title_input': ['[placeholder*="标题"]', '.title-input', 'input[maxlength="20"]'],
            'content_area': ['[placeholder*="正文"]', '.content-input', 'textarea'],
            'publish_btn': ['.publish-btn', '.submit-btn', 'button[type="submit"]'],
            'tag_input': ['.tag-input', '.topic-input', 'input[placeholder*="标签"]']
        }
    
    async def publish(self, content: TransformedContent, options: XhsPublishOptions) -> PublishResult:
        page = await self.browser.new_page()
        
        try:
            # 1. 智能反爬初始化
            await self._setup_anti_detection(page)
            
            # 2. 智能登录策略
            await self._smart_login(page, options.credentials)
            
            # 3. 导航到发布页面
            await self._navigate_to_publish_page(page)
            
            # 4. 模拟人类行为填写内容
            await self._fill_content_with_human_behavior(page, content)
            
            # 5. 发布并等待结果
            result = await self._publish_and_wait(page)
            
            return result
            
        except Exception as error:
            await self._capture_error_state(page, error)
            raise error
        finally:
            await page.close()
    
    async def _setup_anti_detection(self, page: Page) -> None:
        """移除自动化标识"""
        await page.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', { get: () => undefined });
            Object.defineProperty(navigator, 'plugins', { get: () => [1, 2, 3, 4, 5] });
            Object.defineProperty(navigator, 'languages', { get: () => ['zh-CN', 'zh'] });
        """)
        
        # 设置真实的浏览器环境
        await page.set_extra_http_headers({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
    
    async def _fill_content_with_human_behavior(self, page: Page, content: TransformedContent) -> None:
        """填写标题 - 模拟人类打字速度"""
        title_input = await self._find_element(page, self.selectors['title_input'])
        await self._human_type_text(title_input, content.title)
        
        # 随机延迟，模拟人类思考时间
        await self._random_delay(1000, 3000)
        
        # 填写正文
        content_area = await self._find_element(page, self.selectors['content_area'])
        await self._human_type_text(content_area, content.text)
        
        # 添加标签
        for tag in content.tags:
            await self._add_tag(page, tag)
            await self._random_delay(500, 1500)
    
    async def _human_type_text(self, element, text: str) -> None:
        """模拟人类打字的不规则速度"""
        for char in text:
            await element.type(char)
            # 随机打字间隔 50-150ms
            delay = random.random() * 100 + 50
            await asyncio.sleep(delay / 1000)
```
```

#### 4.3.4 今日头条平台集成
```python
import aiohttp
from typing import List

class ToutiaoAdapter:
    def __init__(self):
        self.api_base = 'https://mp.toutiao.com/mp/agw/openapi'
        self.access_token: str = ''
    
    async def authenticate(self, credentials: ToutiaoCredentials) -> AuthResult:
        """头条开放平台API认证"""
        auth_data = {
            'app_id': credentials.app_id,
            'secret': credentials.secret,
            'grant_type': 'client_credentials'
        }
        
        response = await self._make_request('POST', f'{self.api_base}/oauth2/access_token', auth_data)
        self.access_token = response['access_token']
        return AuthResult(success=True, token=response['access_token'])
    
    async def publish(self, content: TransformedContent, options: ToutiaoPublishOptions) -> PublishResult:
        payload = {
            'title': content.title,
            'content': content.html,
            'category': options.category or '科技',
            'tag': ','.join(content.tags),
            'article_type': 0,  # 图文
            'is_original': 1
        }
        
        response = await self._make_request('POST', f'{self.api_base}/article/create', payload)
        return PublishResult(
            success=True,
            platform_post_id=response['article_id'],
            publish_url=response['article_url']
        )
    
    async def validate_format(self, content: Content) -> FormatValidationResult:
        errors = []
        
        if len(content.title) > 100:
            errors.append('标题长度不能超过100字符')
        
        if len(content.content) < 300:
            errors.append('文章内容不能少于300字符')
        
        return FormatValidationResult(is_valid=len(errors) == 0, errors=errors)
    
    def get_supported_formats(self) -> List[ContentFormat]:
        return [ContentFormat.MARKDOWN, ContentFormat.HTML]
    
    async def _make_request(self, method: str, url: str, data: dict) -> dict:
        headers = {
            'Content-Type': 'application/json',
        }
        if self.access_token:
            headers['Authorization'] = f'Bearer {self.access_token}'
        
        async with aiohttp.ClientSession() as session:
            async with session.request(method, url, json=data, headers=headers) as response:
                return await response.json()
```
    
    if (!response.ok) {
      throw new PlatformError('toutiao', 'API_ERROR', response.statusText)
    }
    
    return await response.json()
  }
}
    }
  }
}
```

## 质量保证与最佳实践

### 5.1 错误处理机制

#### 🔍 **错误处理设计哲学**

**为什么需要分层错误处理？**
- **故障隔离**: 防止单个平台错误影响整个发布流程
- **错误分类**: 不同类型的错误需要不同的处理策略
- **用户体验**: 提供清晰的错误信息和解决建议
- **自动恢复**: 可恢复错误应能自动重试，不可恢复错误应立即停止

#### 5.1.1 分层错误处理

**错误分类体系**:
```python
from enum import Enum
from typing import Dict, Any, Optional
import json
from datetime import datetime

# 错误级别定义
class ErrorSeverity(str, Enum):
    LOW = 'low'           # 警告级别，不影响主流程
    MEDIUM = 'medium'     # 部分功能受影响
    HIGH = 'high'         # 主要功能受影响
    CRITICAL = 'critical' # 系统不可用

# 错误类型定义
class ErrorType(str, Enum):
    NETWORK = 'network'           # 网络错误
    AUTHENTICATION = 'auth'       # 认证错误
    RATE_LIMIT = 'rate_limit'     # 限流错误
    VALIDATION = 'validation'     # 数据验证错误
    PLATFORM = 'platform'        # 平台特定错误
    SYSTEM = 'system'             # 系统内部错误

# 错误基类
class TextUpError(Exception):
    def __init__(
        self,
        code: str,
        message: str,
        error_type: ErrorType,
        severity: ErrorSeverity,
        recoverable: bool = False,
        context: Optional[Dict[str, Any]] = None,
        suggestion: Optional[str] = None
    ):
        super().__init__(message)
        self.code = code
        self.message = message
        self.type = error_type
        self.severity = severity
        self.recoverable = recoverable
        self.context = context or {}
        self.suggestion = suggestion
        self.timestamp = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """错误序列化，用于日志和报告"""
        return {
            'name': self.__class__.__name__,
            'code': self.code,
            'message': self.message,
            'type': self.type.value,
            'severity': self.severity.value,
            'recoverable': self.recoverable,
            'context': self.context,
            'suggestion': self.suggestion,
            'timestamp': self.timestamp
        }

# 平台特定错误
class PlatformError(TextUpError):
    def __init__(
        self,
        platform: Platform,
        code: str,
        message: str,
        recoverable: bool = True,
        context: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            f"{platform.value.upper()}_{code}",
            f"[{platform.value}] {message}",
            ErrorType.PLATFORM,
            ErrorSeverity.MEDIUM,
            recoverable,
            {'platform': platform.value, **(context or {})}
        )
        self.platform = platform

# 错误处理器
class ErrorHandler:
    ERROR_RECOVERY_STRATEGIES = {
        # 网络错误的恢复策略
        ErrorType.NETWORK: {
            'max_retries': 3,
            'base_delay': 1000,
            'backoff_multiplier': 2,
            'retryable_status_codes': [408, 429, 500, 502, 503, 504]
        },
        # 限流错误的恢复策略
        ErrorType.RATE_LIMIT: {
            'max_retries': 5,
            'base_delay': 60000,  # 1分钟
            'backoff_multiplier': 1.5,
            'use_exponential_backoff': True
        },
        # 认证错误的恢复策略
        ErrorType.AUTHENTICATION: {
            'max_retries': 1,
            'base_delay': 0,
            'requires_user_intervention': True
        }
    }
  
  static async handleError(error: Error, context: ErrorContext): Promise<ErrorRecoveryAction> {
    // 1. 错误分类和标准化
    const standardError = this.standardizeError(error, context)
    
    // 2. 记录错误
    await this.logError(standardError, context)
    
    // 3. 错误告警
    await this.alertIfNeeded(standardError)
    
    // 4. 决定恢复策略
    const recoveryAction = this.determineRecoveryAction(standardError)
    
    // 5. 用户通知
    await this.notifyUser(standardError, recoveryAction)
    
    return recoveryAction
  }
  
  private static standardizeError(error: Error, context: ErrorContext): TextUpError {
    // 将各种错误转换为标准错误格式
    if (error instanceof TextUpError) {
      return error
    }
    
    // HTTP 错误处理
    if (error.name === 'FetchError' || error.name === 'HTTPError') {
      return new TextUpError(
        'HTTP_ERROR',
        `HTTP请求失败: ${error.message}`,
        ErrorType.NETWORK,
        ErrorSeverity.MEDIUM,
        true,
        { originalError: error, url: context.url }
      )
    }
    
    // 不知道的错误
    return new TextUpError(
      'UNKNOWN_ERROR',
      `未知错误: ${error.message}`,
      ErrorType.SYSTEM,
      ErrorSeverity.HIGH,
      false,
      { originalError: error }
    )
  }
  
  private static determineRecoveryAction(error: TextUpError): ErrorRecoveryAction {
    // 不可恢复错误直接失败
    if (!error.recoverable) {
      return { action: 'fail', reason: error.message }
    }
    
    // 根据错误类型选择策略
    const strategy = this.ERROR_RECOVERY_STRATEGIES[error.type]
    if (!strategy) {
      return { action: 'fail', reason: '无适用的恢复策略' }
    }
    
    // 需要用户干预的错误
    if (strategy.requiresUserIntervention) {
      return { 
        action: 'user_intervention', 
        message: '需要用户手动处理',
        suggestion: error.suggestion
      }
    }
    
    // 自动重试
    return {
      action: 'retry',
      delayMs: strategy.baseDelay,
      maxRetries: strategy.maxRetries,
      backoffMultiplier: strategy.backoffMultiplier
    }
  }
}
```

**错误处理最佳实践**:
- **早期检测**: 在问题扩大前尽早发现并处理
- **上下文保存**: 错误发生时保存充分的上下文信息
- **用户友好**: 提供清晰的错误信息和操作建议
- **监控告警**: 关键错误应立即触发告警机制

#### 5.1.2 重试机制
```python
import asyncio
from typing import Callable, TypeVar, Awaitable
from pydantic import BaseModel

T = TypeVar('T')

class RetryPolicy(BaseModel):
    max_attempts: int
    base_delay_ms: int
    backoff_multiplier: float
    max_delay_ms: int

class RetryManager:
    @staticmethod
    async def execute_with_retry(
        operation: Callable[[], Awaitable[T]],
        policy: RetryPolicy,
        context: str
    ) -> T:
        last_error: Exception = None
        
        for attempt in range(1, policy.max_attempts + 1):
            try:
                return await operation()
            except Exception as error:
                last_error = error
                
                if attempt == policy.max_attempts:
                    raise TextUpError(
                        'MAX_RETRIES_EXCEEDED',
                        f'Operation failed after {attempt} attempts: {context}',
                        ErrorType.SYSTEM,
                        ErrorSeverity.HIGH,
                        False,
                        {'original_error': str(error)}
                    )
                
                # 计算延迟时间 (指数退避)
                delay_ms = min(
                    policy.base_delay_ms * (policy.backoff_multiplier ** (attempt - 1)),
                    policy.max_delay_ms
                )
                
                print(f'Attempt {attempt} failed, retrying in {delay_ms}ms...')
                await asyncio.sleep(delay_ms / 1000)
```

### 5.2 安全性设计

#### 5.2.1 凭证管理
```python
import os
import json
from cryptography.fernet import Fernet
from typing import Any, Dict

# 凭证加密存储
class CredentialsManager:
    def __init__(self):
        self.encryption_key = os.getenv('TEXTUP_ENCRYPTION_KEY')
        if not self.encryption_key:
            raise ValueError("TEXTUP_ENCRYPTION_KEY environment variable is required")
        self.fernet = Fernet(self.encryption_key.encode())
    
    async def store_credentials(self, platform: Platform, credentials: Dict[str, Any]) -> None:
        encrypted = self._encrypt(json.dumps(credentials))
        
        # 使用数据库存储 (这里用伪代码表示)
        # await db.platform_configs.upsert({
        #     'where': {'platform': platform.value},
        #     'update': {'credentials_encrypted': encrypted},
        #     'create': {
        #         'platform': platform.value,
        #         'credentials_encrypted': encrypted,
        #         'config': {},
        #         'is_active': True
        #     }
        # })
        pass
    
    async def get_credentials(self, platform: Platform) -> Dict[str, Any]:
        # config = await db.platform_configs.find_unique({'where': {'platform': platform.value}})
        
        # if not config or not config.credentials_encrypted:
        #     raise TextUpError(
        #         'CREDENTIALS_NOT_FOUND', 
        #         f'No credentials found for {platform.value}',
        #         ErrorType.AUTHENTICATION,
        #         ErrorSeverity.HIGH
        #     )
        
        # return json.loads(self._decrypt(config.credentials_encrypted))
        pass
    
    def _encrypt(self, text: str) -> str:
        """使用 Fernet (基于 AES-128) 加密"""
        return self.fernet.encrypt(text.encode()).decode()
    
    def _decrypt(self, encrypted_text: str) -> str:
        return self.fernet.decrypt(encrypted_text.encode()).decode()
```

#### 5.2.2 输入验证
```python
import re
from typing import List

# 内容安全验证
class ContentValidator:
    SENSITIVE_WORDS = ['敏感词1', '敏感词2']  # 示例
    
    @staticmethod
    def validate_content(content: Content) -> ValidationResult:
        errors: List[ValidationError] = []
        
        # 标题验证
        if not content.title or content.title.strip() == '':
            errors.append(ValidationError(field='title', message='标题不能为空'))
        
        if len(content.title) > 100:
            errors.append(ValidationError(field='title', message='标题长度不能超过100字符'))
        
        # 内容验证
        if not content.content or content.content.strip() == '':
            errors.append(ValidationError(field='content', message='内容不能为空'))
        
        # 敏感词检测
        sensitive_words = ContentValidator._detect_sensitive_words(content.content)
        if sensitive_words:
            errors.append(ValidationError(
                field='content',
                message=f'内容包含敏感词: {", ".join(sensitive_words)}',
                level='warning'
            ))
        
        # 标签验证
        if len(content.tags) > 10:
            errors.append(ValidationError(field='tags', message='标签数量不能超过10个'))
        
        return ValidationResult(
            is_valid=len([e for e in errors if e.level != 'warning']) == 0,
            errors=errors
        )
    
    @staticmethod
    def _detect_sensitive_words(text: str) -> List[str]:
        found_words = []
        for word in ContentValidator.SENSITIVE_WORDS:
            if word in text:
                found_words.append(word)
        return found_words
```

### 5.3 性能优化

#### 5.3.1 并发控制
```python
import asyncio
from typing import Dict

# 并发发布控制
class ConcurrencyManager:
    def __init__(self):
        self.concurrent_limits = {
            Platform.ZHIHU: 2,      # 知乎限制较严
            Platform.WEIBO: 3,      # 微博相对宽松
            Platform.XIAOHONGSHU: 1, # 小红书需要Web自动化，限制为1
            Platform.TOUTIAO: 2
        }
        self.active_tasks: Dict[Platform, int] = {}
        self.locks: Dict[Platform, asyncio.Semaphore] = {
            platform: asyncio.Semaphore(limit) 
            for platform, limit in self.concurrent_limits.items()
        }
    
    async def acquire_slot(self, platform: Platform) -> None:
        """'获取平台发布槽位"""
        semaphore = self.locks.get(platform)
        if semaphore:
            await semaphore.acquire()
        
        current = self.active_tasks.get(platform, 0)
        self.active_tasks[platform] = current + 1
    
    def release_slot(self, platform: Platform) -> None:
        """释放平台发布槽位"""
        semaphore = self.locks.get(platform)
        if semaphore:
            semaphore.release()
        
        current = self.active_tasks.get(platform, 0)
        self.active_tasks[platform] = max(0, current - 1)
```

#### 5.3.2 缓存策略
```python
import hashlib
import json
from typing import Optional, Any
import aioredis

# 内容转换缓存
class ContentCache:
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis = None  # 将在 async 方法中初始化
        self.redis_url = redis_url
  
  async getCachedConversion(
    contentHash: string, 
    targetFormat: string
  ): Promise<string | null> {
    const key = `conversion:${contentHash}:${targetFormat}`
    return await this.redis.get(key)
  }
  
  async setCachedConversion(
    contentHash: string,
    targetFormat: string, 
    convertedContent: string,
    ttlSeconds: number = 86400 // 24小时
  ): Promise<void> {
    const key = `conversion:${contentHash}:${targetFormat}`
    await this.redis.setex(key, ttlSeconds, convertedContent)
  }
}
```

## 部署与运维

### 6.1 Docker 部署

#### 6.1.1 Dockerfile
```dockerfile
# 多阶段构建
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# 生产镜像
FROM node:18-alpine AS production

# 安装 Playwright 依赖
RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S textup -u 1001

WORKDIR /app

# 复制构建产物
COPY --from=builder --chown=textup:nodejs /app/dist ./dist
COPY --from=builder --chown=textup:nodejs /app/node_modules ./node_modules
COPY --chown=textup:nodejs package.json ./

USER textup

EXPOSE 3000

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

CMD ["node", "dist/server.js"]
```

#### 6.1.2 Docker Compose
```yaml
version: '3.8'

services:
  textup:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=******************************/textup
      - REDIS_URL=redis://redis:6379
      - TEXTUP_ENCRYPTION_KEY=${TEXTUP_ENCRYPTION_KEY}
    volumes:
      - ./content:/app/content:ro
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    restart: unless-stopped
    
  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=textup
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped
    
volumes:
  postgres_data:
  redis_data:
```

### 6.2 监控与日志

#### 6.2.1 健康检查
```typescript
// 健康检查端点
app.get('/health', async (req, res) => {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    services: {},
    version: process.env.npm_package_version
  }
  
  try {
    // 数据库连接检查
    await db.$queryRaw`SELECT 1`
    health.services.database = 'healthy'
  } catch (error) {
    health.services.database = 'unhealthy'
    health.status = 'degraded'
  }
  
  try {
    // Redis连接检查
    await redis.ping()
    health.services.redis = 'healthy'
  } catch (error) {
    health.services.redis = 'unhealthy'
    health.status = 'degraded'
  }
  
  // 平台服务检查
  for (const platform of ['zhihu', 'weibo', 'xiaohongshu', 'toutiao']) {
    try {
      const adapter = PlatformAdapterFactory.create(platform)
      await adapter.healthCheck()
      health.services[platform] = 'healthy'
    } catch (error) {
      health.services[platform] = 'unhealthy'
    }
  }
  
  const statusCode = health.status === 'ok' ? 200 : 503
  res.status(statusCode).json(health)
})
```

#### 6.2.2 结构化日志
```typescript
// 日志配置
import winston from 'winston'

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'textup' },
  transports: [
    new winston.transports.File({ 
# Python 日志配置
import logging
from typing import List, Dict, Any

# 配置日志记录器
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/error.log', level=logging.ERROR),
        logging.FileHandler('logs/combined.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('textup')

# 业务日志记录器
class PublishLogger:
    @staticmethod
    def log_publish_start(task_id: str, platforms: List[Platform]) -> None:
        logger.info('Publish task started', extra={
            'task_id': task_id,
            'platforms': [p.value for p in platforms],
            'event': 'publish_start'
        })
    
    @staticmethod
    def log_publish_complete(task_id: str, results: List[PublishResult]) -> None:
        logger.info('Publish task completed', extra={
            'task_id': task_id,
            'results': [{
                'platform': getattr(r, 'platform', None),
                'success': r.success,
                'post_id': r.platform_post_id
            } for r in results],
            'event': 'publish_complete'
        })
    
    @staticmethod
    def log_publish_error(task_id: str, platform: Platform, error: Exception) -> None:
        logger.error('Publish failed', extra={
            'task_id': task_id,
            'platform': platform.value,
            'error': str(error),
            'event': 'publish_error'
        }, exc_info=True)
```

## 测试策略

### 7.1 单元测试

```python
import pytest
from unittest.mock import Mock, patch
from textup.content_manager import ContentManager
from textup.adapters.zhihu import ZhihuAdapter

# ContentManager 单元测试
class TestContentManager:
    def setup_method(self):
        self.content_manager = ContentManager()
    
    def test_parse_markdown_content(self):
        """should parse markdown content correctly"""
        source = {
            'type': 'file',
            'path': './test/fixtures/sample.md',
            'content': '# Test Title\n\nTest content'
        }
        
        result = self.content_manager.parse_content(source)
        
        assert result.title == 'Test Title'
        assert result.content == '# Test Title\n\nTest content'
        assert result.content_format == ContentFormat.MARKDOWN
    
    def test_extract_metadata_from_frontmatter(self):
        """should extract metadata from frontmatter"""
        source = {
            'type': 'file',
            'content': '''---
title: Custom Title
tags: [test, demo]
---

# Content'''
        }
        
        result = self.content_manager.parse_content(source)
        
        assert result.metadata['title'] == 'Custom Title'
        assert result.metadata['tags'] == ['test', 'demo']

# Platform Adapter 测试
class TestZhihuAdapter:
    def setup_method(self):
        self.adapter = ZhihuAdapter()
    
    @patch('aiohttp.ClientSession.request')
    async def test_publish_article_successfully(self, mock_request):
        """should publish article successfully"""
        # Mock HTTP response
        mock_response = Mock()
        mock_response.json.return_value = {'id': 'article_123'}
        mock_request.return_value.__aenter__.return_value = mock_response
        
        content = TransformedContent(
            title='Test Article',
            content='# Test\n\nContent',
            html='<h1>Test</h1><p>Content</p>',
            text='Test\nContent',
            tags=['测试']
        )
        
        result = await self.adapter.publish(content, ZhihuPublishOptions(column_id='column_456'))
        
        assert result.success is True
        assert result.platform_post_id == 'article_123'
```

### 7.2 集成测试

```python
import pytest
import asyncio
from textup.database import Database
from textup.services.publish_service import PublishService

# 端到端发布流程测试
class TestEndToEndPublishFlow:
    @pytest.fixture(scope="class")
    async def test_db(self):
        # 设置测试数据库
        db = await self.setup_test_database()
        yield db
        await self.teardown_test_database(db)
    
    async def test_complete_full_publish_workflow(self, test_db):
        """should complete full publish workflow"""
        # 1. 创建内容
        content = await test_db.contents.create({
            'title': 'Integration Test Article',
            'content': '# Test\n\nThis is a test article',
            'content_format': ContentFormat.MARKDOWN,
            'tags': ['test']
        })
        
        # 2. 创建发布任务
        task = await test_db.publish_tasks.create({
            'content_id': content.id,
            'platforms': [Platform.ZHIHU],
            'publish_strategy': PublishStrategy.IMMEDIATE
        })
        
        # 3. 执行发布
        publish_service = PublishService()
        result = await publish_service.execute_task(task.id)
        
        # 4. 验证结果
        assert result.success is True
        
        publish_record = await test_db.publish_records.find_first(
            where={'task_id': task.id}
        )
        
        assert publish_record is not None
        assert publish_record.status == PublishStatus.PUBLISHED
        assert publish_record.platform_post_id is not None
```

## 用户指南

### 8.1 快速开始

#### 8.1.1 安装与初始化
```bash
# 全局安装
npm install -g textup

# 初始化项目配置
textup init

# 配置平台认证
textup auth login zhihu
textup auth login weibo
```

#### 8.1.2 基础使用
```bash
# 发布单个文件到知乎
textup publish ./article.md --platform zhihu

# 发布到多个平台
textup publish ./article.md --platforms zhihu,weibo,toutiao

# 定时发布
textup publish ./article.md --schedule "2024-01-01 10:00:00"
```

### 8.2 配置说明

#### 8.2.1 全局配置文件
`~/.textup/config.yaml`:
```yaml
default_platforms: ["zhihu", "weibo"]
default_tags: ["原创"]
default_schedule_strategy: "immediate"

platforms:
  zhihu:
    default_column: "技术分享"
  weibo:
    default_visibility: 0
```

## API 文档

### 9.1 RESTful API

#### 9.1.1 认证
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "password"
}
```

#### 9.1.2 内容管理
```http
# 创建内容
POST /api/contents
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "文章标题",
  "content": "# 标题\n\n内容...",
  "contentFormat": "markdown",
  "tags": ["技术", "分享"]
}

Response:
{
  "id": "content_123",
  "title": "文章标题",
  "status": "created",
  "createdAt": "2024-01-01T00:00:00Z"
}

# 获取内容列表
GET /api/contents?page=1&limit=20&tag=技术&format=markdown

Response:
{
  "data": [
    {
      "id": "content_123",
      "title": "文章标题",
      "contentFormat": "markdown",
      "tags": ["技术"],
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 20,
    "totalPages": 5
  }
}

# 获取单个内容
GET /api/contents/{id}

Response:
{
  "id": "content_123",
  "title": "文章标题",
  "content": "# 标题\n\n内容...",
  "contentFormat": "markdown",
  "tags": ["技术", "分享"],
  "metadata": {},
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}

# 更新内容
PUT /api/contents/{id}
Content-Type: application/json

{
  "title": "更新后的标题",
  "content": "更新后的内容",
  "tags": ["技术", "更新"]
}

# 删除内容
DELETE /api/contents/{id}
```

#### 9.1.3 发布管理
```http
# 创建发布任务
POST /api/publish/tasks
Content-Type: application/json

{
  "contentId": "content_123",
  "platforms": ["zhihu", "weibo"],
  "publishStrategy": "scheduled",
  "scheduledTime": "2024-01-01T10:00:00Z",
  "options": {
    "zhihu": {
      "columnId": "column_456",
      "type": "article"
    },
    "weibo": {
      "visibility": 0
    }
  }
}

Response:
{
  "taskId": "task_789",
  "status": "pending",
  "platforms": ["zhihu", "weibo"],
  "scheduledTime": "2024-01-01T10:00:00Z",
  "createdAt": "2024-01-01T00:00:00Z"
}

# 查看任务状态
GET /api/publish/tasks/{taskId}

Response:
{
  "id": "task_789",
  "contentId": "content_123",
  "platforms": ["zhihu", "weibo"],
  "status": "completed",
  "publishStrategy": "scheduled",
  "scheduledTime": "2024-01-01T10:00:00Z",
  "createdAt": "2024-01-01T00:00:00Z",
  "completedAt": "2024-01-01T10:05:00Z"
}

# 获取发布记录
GET /api/publish/records?taskId={taskId}

Response:
{
  "data": [
    {
      "id": "record_001",
      "taskId": "task_789",
      "platform": "zhihu",
      "status": "published",
      "platformPostId": "article_123",
      "publishedAt": "2024-01-01T10:02:00Z"
    },
    {
      "id": "record_002",
      "taskId": "task_789",
      "platform": "weibo",
      "status": "published",
      "platformPostId": "weibo_456",
      "publishedAt": "2024-01-01T10:03:00Z"
    }
  ]
}

# 取消任务
POST /api/publish/tasks/{taskId}/cancel

# 重试失败的任务
POST /api/publish/tasks/{taskId}/retry
```

#### 9.1.4 平台管理
```http
# 获取平台配置
GET /api/platforms

Response:
{
  "data": [
    {
      "platform": "zhihu",
      "isEnabled": true,
      "isAuthenticated": true,
      "lastAuthTime": "2024-01-01T00:00:00Z",
      "supportedFormats": ["markdown", "html"]
    }
  ]
}

# 更新平台配置
PUT /api/platforms/{platform}
Content-Type: application/json

{
  "isEnabled": true,
  "config": {
    "defaultColumn": "技术分享"
  }
}

# 平台认证
POST /api/platforms/{platform}/auth
Content-Type: application/json

{
  "credentials": {
    "accessToken": "xxx",
    "refreshToken": "yyy"
  }
}

# 测试平台连接
POST /api/platforms/{platform}/test
```

### 9.2 WebSocket API

#### 9.2.1 连接与认证
```typescript
// 建立WebSocket连接
const ws = new WebSocket('ws://localhost:3000/ws')

// 认证
ws.onopen = () => {
  ws.send(JSON.stringify({
    type: 'auth',
    token: 'your_jwt_token'
  }))
}

// 处理认证结果
ws.onmessage = (event) => {
  const data = JSON.parse(event.data)
  if (data.type === 'auth_success') {
    console.log('WebSocket authenticated successfully')
    // 现在可以订阅频道
  }
}
```

#### 9.2.2 实时状态更新
```typescript
// 订阅特定任务状态
ws.send(JSON.stringify({
  type: 'subscribe',
  channel: 'task_status',
  taskId: 'task_123'
}))

// 订阅所有任务状态
ws.send(JSON.stringify({
  type: 'subscribe',
  channel: 'all_tasks'
}))

// 接收状态更新
ws.onmessage = (event) => {
  const data = JSON.parse(event.data)
  
  switch (data.type) {
    case 'task_status_update':
      handleTaskStatusUpdate(data.payload)
      break
      
    case 'publish_progress':
      handlePublishProgress(data.payload)
      break
      
    case 'publish_success':
      handlePublishSuccess(data.payload)
      break
      
    case 'publish_error':
      handlePublishError(data.payload)
      break
  }
}

function handleTaskStatusUpdate(payload) {
  // { taskId: 'task_123', status: 'running', updatedAt: '2024-01-01T10:00:00Z' }
  console.log(`Task ${payload.taskId} status: ${payload.status}`)
}

function handlePublishProgress(payload) {
  // { taskId: 'task_123', platform: 'zhihu', progress: 75, message: 'Uploading images...' }
  console.log(`${payload.platform}: ${payload.progress}% - ${payload.message}`)
}

function handlePublishSuccess(payload) {
  // { taskId: 'task_123', platform: 'zhihu', postId: 'article_456', url: 'https://...' }
  console.log(`Published to ${payload.platform}: ${payload.url}`)
}

function handlePublishError(payload) {
  // { taskId: 'task_123', platform: 'weibo', error: 'Rate limit exceeded', retryAt: '...' }
  console.error(`${payload.platform} error: ${payload.error}`)
}
```

#### 9.2.3 实时日志订阅
```typescript
// 订阅实时日志
ws.send(JSON.stringify({
  type: 'subscribe',
  channel: 'logs',
  filters: {
    level: ['info', 'error'], // 只接收info和error级别的日志
    taskId: 'task_123',       // 只接收特定任务的日志
    platform: 'zhihu'        // 只接收特定平台的日志
  }
}))

// 处理日志消息
ws.onmessage = (event) => {
  const data = JSON.parse(event.data)
  if (data.type === 'log') {
    console.log(`[${data.timestamp}] [${data.level}] ${data.message}`, data.metadata)
  }
}
```

#### 9.2.4 取消订阅
```typescript
// 取消特定订阅
ws.send(JSON.stringify({
  type: 'unsubscribe',
  channel: 'task_status',
  taskId: 'task_123'
}))

// 取消所有订阅
ws.send(JSON.stringify({
  type: 'unsubscribe_all'
}))
```

## 扩展开发

### 10.1 插件架构

#### 10.1.1 自定义平台适配器
```typescript
// 实现平台适配器接口
export class CustomPlatformAdapter implements PlatformAdapter {
  private config: CustomPlatformConfig
  private httpClient: HttpClient
  
  constructor(config: CustomPlatformConfig) {
    this.config = config
    this.httpClient = new HttpClient(config.baseUrl)
  }
  
  async authenticate(credentials: CustomCredentials): Promise<AuthResult> {
    try {
      const response = await this.httpClient.post('/auth/login', {
        username: credentials.username,
        password: credentials.password
      })
      
      return {
        success: true,
        token: response.data.accessToken,
        expiresAt: new Date(response.data.expiresAt)
      }
    } catch (error) {
      return {
        success: false,
        errorMessage: error.message
      }
    }
  }
  
  async publish(content: TransformedContent, options: CustomPublishOptions): Promise<PublishResult> {
    // 验证内容格式
    const validation = await this.validateFormat(content)
    if (!validation.isValid) {
      throw new PlatformError('custom', 'VALIDATION_FAILED', validation.errors.join(', '))
    }
    
    // 转换内容格式
    const transformedContent = this.transformContent(content, options)
    
    try {
      const response = await this.httpClient.post('/posts', {
        title: transformedContent.title,
        content: transformedContent.content,
        tags: transformedContent.tags,
        category: options.category,
        visibility: options.visibility
      })
      
      return {
        success: true,
        platformPostId: response.data.postId,
        publishUrl: response.data.url
      }
    } catch (error) {
      throw new PlatformError('custom', 'PUBLISH_FAILED', error.message)
    }
  }
  
  async validateFormat(content: Content): Promise<FormatValidationResult> {
    const errors: string[] = []
    
    // 标题验证
    if (!content.title || content.title.length > this.config.maxTitleLength) {
      errors.push(`标题长度不能超过${this.config.maxTitleLength}字符`)
    }
    
    // 内容验证
    if (!content.content || content.content.length < this.config.minContentLength) {
      errors.push(`内容长度不能少于${this.config.minContentLength}字符`)
    }
    
    // 标签验证
    if (content.tags.length > this.config.maxTags) {
      errors.push(`标签数量不能超过${this.config.maxTags}个`)
    }
    
    return { isValid: errors.length === 0, errors }
  }
  
  getSupportedFormats(): ContentFormat[] {
    return this.config.supportedFormats
  }
  
  private transformContent(content: TransformedContent, options: CustomPublishOptions): TransformedContent {
    // 根据平台要求转换内容格式
    let transformedContent = { ...content }
    
    if (options.format === 'html') {
      transformedContent.content = this.convertMarkdownToHtml(content.content)
    }
    
    return transformedContent
  }
  
  private convertMarkdownToHtml(markdown: string): string {
    // 使用markdown解析器转换
    return marked(markdown)
  }
}

// 插件注册
PlatformAdapterRegistry.register('custom_platform', {
  name: '自定义平台',
  description: '自定义平台适配器示例',
  version: '1.0.0',
  adapter: CustomPlatformAdapter,
  configSchema: {
    baseUrl: { type: 'string', required: true },
    maxTitleLength: { type: 'number', default: 100 },
    minContentLength: { type: 'number', default: 50 },
    maxTags: { type: 'number', default: 5 },
    supportedFormats: { type: 'array', default: ['markdown', 'html'] }
  }
})
```

#### 10.1.2 内容处理插件
```typescript
// 内容预处理插件
export class CustomContentProcessor implements ContentProcessor {
  private config: ProcessorConfig
  
  constructor(config: ProcessorConfig) {
    this.config = config
  }
  
  async process(content: Content, context: ProcessingContext): Promise<Content> {
    let processedContent = { ...content }
    
    // 自定义标签处理
    if (this.config.autoAddTags) {
      processedContent = await this.addAutoTags(processedContent, context)
    }
    
    // 内容格式化
    if (this.config.formatContent) {
      processedContent.content = this.formatContent(processedContent.content)
    }
    
    // 图片优化
    if (this.config.optimizeImages && processedContent.images) {
      processedContent.images = await this.optimizeImages(processedContent.images)
    }
    
    return processedContent
  }
  
  private async addAutoTags(content: Content, context: ProcessingContext): Promise<Content> {
    // 基于内容自动提取标签
    const extractedTags = await this.extractTagsFromContent(content.content)
    const autoTags = extractedTags.filter(tag => 
      !content.tags.includes(tag) && this.config.allowedAutoTags.includes(tag)
    )
    
    return {
      ...content,
      tags: [...content.tags, ...autoTags].slice(0, this.config.maxTags)
    }
  }
  
  private formatContent(content: string): string {
    // 自定义格式化规则
    return content
      .replace(/\[highlight\](.+?)\[\/highlight\]/g, '<mark>$1</mark>')
      .replace(/\[note\](.+?)\[\/note\]/g, '<blockquote>$1</blockquote>')
      .replace(/\[tip\](.+?)\[\/tip\]/g, '<div class="tip">$1</div>')
  }
  
  private async optimizeImages(images: string[]): Promise<string[]> {
    // 图片压缩和优化
    const optimizedImages = []
    
    for (const image of images) {
```python
# 自定义内容处理器
from typing import List, Dict, Any
import re
from collections import Counter

class CustomContentProcessor:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
    
    async def process_content(self, content: Content) -> Content:
        processed_content = content.copy()
        
        # 1. 自动添加标签
        if self.config.get('auto_add_tags', True):
            auto_tags = await self._extract_tags_from_content(content.content)
            processed_content.tags.extend(auto_tags[:self.config.get('max_tags', 10)])
        
        # 2. 内容格式化
        if self.config.get('format_content', True):
            processed_content.content = self._format_content(content.content)
        
        # 3. 图片优化
        if self.config.get('optimize_images', True):
            if hasattr(processed_content, 'images') and processed_content.images:
                processed_content.images = await self._optimize_images(processed_content.images)
        
        return processed_content
    
    def _format_content(self, content: str) -> str:
        """内容格式化处理"""
        # 添加段落间距
        content = re.sub(r'\n{3,}', '\n\n', content)
        
        # 统一标点符号
        content = re.sub(r'[\u2022\u2023\u25e6]', '\u2022', content)
        
        # 清理多余空格
        content = re.sub(r' {2,}', ' ', content)
        
        return content.strip()
    
    async def _optimize_images(self, images: List[str]) -> List[str]:
        """图片优化处理"""
        optimized_images = []
        
        for image in images:
            optimized = await self._compress_image(image, {
                'quality': self.config.get('image_quality', 0.8),
                'max_width': self.config.get('max_image_width', 1920),
                'max_height': self.config.get('max_image_height', 1080)
            })
            optimized_images.append(optimized)
        
        return optimized_images
    
    async def _extract_tags_from_content(self, content: str) -> List[str]:
        """使用NLP或关键词提取算法"""
        keywords = self._extract_keywords(content)
        return [k['word'] for k in keywords if k['confidence'] > 0.8]
    
    def _extract_keywords(self, text: str) -> List[Dict[str, Any]]:
        """简单的关键词提取实现"""
        words = re.split(r'\s+', text)
        # 清理和过滤单词
        clean_words = []
        for word in words:
            clean_word = re.sub(r'[^\u4e00-\u9fa5a-z]', '', word.lower())
            if len(clean_word) > 1:
                clean_words.append(clean_word)
        
        # 计算词频
        word_freq = Counter(clean_words)
        total_words = len(words)
        
        # 返回结果
        results = []
        for word, freq in word_freq.most_common():
            results.append({
                'word': word,
                'confidence': freq / total_words
            })
        
        return results

# 注册内容处理器
class ContentProcessorRegistry:
    _processors = {}
    
    @classmethod
    def register(cls, name: str, config: Dict[str, Any]):
        cls._processors[name] = config

# 用法示例
ContentProcessorRegistry.register('auto_tagger', {
    'name': '自动标签处理器',
    'description': '自动从内容中提取和添加标签',
    'processor': CustomContentProcessor,
    'config_schema': {
        'auto_add_tags': {'type': 'boolean', 'default': True},
        'format_content': {'type': 'boolean', 'default': True},
        'optimize_images': {'type': 'boolean', 'default': True},
        'max_tags': {'type': 'number', 'default': 10},
        'image_quality': {'type': 'number', 'default': 0.8},
        'max_image_width': {'type': 'number', 'default': 1920},
        'max_image_height': {'type': 'number', 'default': 1080},
        'allowed_auto_tags': {'type': 'array', 'default': []}
    }
})
```
```

### 10.2 Webhook 支持

#### 10.2.1 Webhook 配置
```python
from typing import List, Dict, Any, Optional
from enum import Enum
from pydantic import BaseModel
import hmac
import hashlib
import asyncio
from datetime import datetime
import aiohttp

class WebhookEvent(str, Enum):
    TASK_CREATED = 'task.created'
    TASK_STARTED = 'task.started'
    TASK_COMPLETED = 'task.completed'
    TASK_FAILED = 'task.failed'
    PUBLISH_STARTED = 'publish.started'
    PUBLISH_SUCCESS = 'publish.success'
    PUBLISH_FAILED = 'publish.failed'
    CONTENT_CREATED = 'content.created'
    CONTENT_UPDATED = 'content.updated'
    CONTENT_DELETED = 'content.deleted'

class WebhookConfig(BaseModel):
    id: str
    name: str
    url: str
    events: List[WebhookEvent]
    secret: Optional[str] = None
    headers: Dict[str, str] = {}
    retries: int = 3
    timeout: int = 30
    is_active: bool = True

# Webhook 管理器
class WebhookManager:
    def __init__(self, event_emitter):
        self.webhooks: Dict[str, WebhookConfig] = {}
        self.event_emitter = event_emitter
        self._setup_event_listeners()
    
    def register_webhook(self, config: WebhookConfig) -> None:
        self.webhooks[config.id] = config
    
    def _setup_event_listeners(self) -> None:
        self.event_emitter.on('task:created', lambda data: self._trigger_webhooks('task.created', data))
        self.event_emitter.on('task:started', lambda data: self._trigger_webhooks('task.started', data))
        self.event_emitter.on('task:completed', lambda data: self._trigger_webhooks('task.completed', data))
        self.event_emitter.on('task:failed', lambda data: self._trigger_webhooks('task.failed', data))
        self.event_emitter.on('publish:success', lambda data: self._trigger_webhooks('publish.success', data))
        self.event_emitter.on('publish:failed', lambda data: self._trigger_webhooks('publish.failed', data))
    
    async def _trigger_webhooks(self, event: WebhookEvent, payload: Any) -> None:
        applicable_webhooks = [
            webhook for webhook in self.webhooks.values()
            if webhook.is_active and event in webhook.events
        ]
        
        tasks = [
            self._send_webhook(webhook, event, payload)
            for webhook in applicable_webhooks
        ]
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _send_webhook(self, config: WebhookConfig, event: WebhookEvent, payload: Any) -> None:
        webhook_payload = {
            'event': event.value,
            'timestamp': datetime.now().isoformat(),
            'data': payload
        }
        
        headers = {
            'Content-Type': 'application/json',
            'X-TextUp-Event': event.value,
            'X-TextUp-Timestamp': webhook_payload['timestamp'],
            **config.headers
        }
        
        if config.secret:
            signature = self._generate_signature(str(webhook_payload), config.secret)
            headers['X-TextUp-Signature'] = signature
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=config.timeout)) as session:
                async with session.post(
                    config.url,
                    json=webhook_payload,
                    headers=headers
                ) as response:
                    if response.status >= 400:
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status
                        )
        except Exception as error:
            print(f'Webhook {config.name} failed: {error}')
            # 实现重试逻辑
    
    def _generate_signature(self, payload: str, secret: str) -> str:
        return hmac.new(
            secret.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
```

## 结论

### 11.1 项目总结

TextUp 是一个功能完整、架构清晰的多平台内容发布工具。本文档详细规定了：

- **系统架构**: 分层设计，模块化开发
- **功能规范**: 完整的发布流程和管理功能
- **技术实现**: 具体的代码结构和实现方案
- **质量保证**: 错误处理、安全性、性能优化
- **部署运维**: Docker化部署和监控方案
- **测试策略**: 单元测试和集成测试覆盖
- **用户指南**: 详细的使用说明和API文档
- **扩展能力**: 插件架构和自定义开发

### 11.2 开发优先级

**第一阶段 (MVP)**:
1. 核心内容管理功能
2. 知乎和微博平台集成
3. 基础CLI工具
4. 简单的配置管理

**第二阶段 (功能完善)**:
1. 小红书和今日头条集成
2. 定时发布功能
3. Web UI界面
4. 完整的错误处理

**第三阶段 (高级特性)**:
1. 智能发布策略
2. 内容格式转换
3. 插件系统
4. API和Webhook支持

### 11.3 成功指标

- **功能完整性**: 支持所有主流平台的发布
- **稳定性**: 99.9%的发布成功率
- **性能**: 单次发布响应时间<30秒
- **易用性**: 5分钟内完成首次发布
- **扩展性**: 支持新增平台插件
- **安全性**: 通过安全审计，零数据泄露

本文档为AI开发提供了完整的技术规范和实现指导，确保生成的代码具有高质量、高可维护性和高扩展性，符合软件开发的最佳实践。
```
```