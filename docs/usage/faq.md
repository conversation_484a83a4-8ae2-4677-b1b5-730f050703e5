# TextUp 常见问题解答 (FAQ) ❓

本文档收集了用户在使用 TextUp 过程中遇到的常见问题和解决方案。

## 📋 目录

- [安装和环境问题](#安装和环境问题)
- [配置和认证问题](#配置和认证问题)
- [发布和内容问题](#发布和内容问题)
- [性能和稳定性问题](#性能和稳定性问题)
- [平台特定问题](#平台特定问题)
- [高级使用问题](#高级使用问题)

---

## 🔧 安装和环境问题

### Q1: 提示 `textup: command not found`，怎么办？

**原因**：虚拟环境未激活或安装不完整

**解决方案**：
```bash
# 1. 确保在项目目录中
cd /path/to/textup

# 2. 激活虚拟环境
source .venv/bin/activate  # macOS/Linux
# 或
.venv\Scripts\activate     # Windows

# 3. 重新安装
uv pip install -e .

# 4. 验证安装
textup --version
```

### Q2: Python 版本不兼容怎么办？

**要求**：Python 3.9 或更高版本

**解决方案**：
```bash
# 检查当前版本
python --version

# 如果版本过低，安装新版本
# macOS (使用 Homebrew)
brew install python@3.11

# Ubuntu/Debian
sudo apt update
sudo apt install python3.11

# 使用新版本创建虚拟环境
python3.11 -m venv .venv
```

### Q3: uv 包管理器安装失败？

**解决方案**：
```bash
# 方法1: 使用官方安装脚本
curl -LsSf https://astral.sh/uv/install.sh | sh

# 方法2: 使用 pip 安装
pip install uv

# 方法3: 如果都失败，使用传统 pip
pip install -e .
```

---

## 🔐 配置和认证问题

### Q4: 如何获取平台 API 凭证？

**微博平台**：
1. 访问 [微博开放平台](https://open.weibo.com/)
2. 注册开发者账号
3. 创建应用获取 App Key 和 App Secret
4. 设置回调地址：`http://localhost:8080/callback`

**知乎平台**：
1. 访问 [知乎开发者平台](https://developers.zhihu.com/)
2. 注册开发者账号
3. 创建应用获取 client_id 和 client_secret
4. 设置回调地址：`http://localhost:8080/zhihu/callback`

### Q5: 认证失败，提示 `AuthenticationError`？

**常见原因和解决方案**：

1. **凭证错误**：
```bash
# 检查配置
textup config --get platforms.weibo.client_id

# 重新设置正确的凭证
textup config --set platforms.weibo.client_id=正确的值
textup config --set platforms.weibo.client_secret=正确的值
```

2. **回调地址不匹配**：
```bash
# 确保回调地址与平台设置一致
textup config --set platforms.weibo.redirect_uri=http://localhost:8080/callback
```

3. **令牌过期**：
```bash
# 重新认证
textup auth --interactive --platform weibo
```

### Q6: 配置文件在哪里？

**默认位置**：
- **Linux/macOS**: `~/.textup/config/config.yaml`
- **Windows**: `%USERPROFILE%\.textup\config\config.yaml`

**查看配置**：
```bash
# 查看所有配置
textup config --list

# 查看配置文件位置
textup config --get-config-path
```

---

## 📝 发布和内容问题

### Q7: 发布失败，提示网络错误？

**解决方案**：

1. **检查网络连接**：
```bash
# 测试网络连接
ping api.weibo.com
ping www.zhihu.com
```

2. **配置代理**（如果需要）：
```bash
export HTTP_PROXY=http://your-proxy:port
export HTTPS_PROXY=http://your-proxy:port
```

3. **增加超时时间**：
```bash
textup config --set app.timeout=60
```

### Q8: 内容格式验证失败？

**解决方案**：

1. **验证内容格式**：
```bash
textup validate --file your-article.md --platform weibo
```

2. **查看平台限制**：
```bash
textup info --platform weibo
```

3. **使用预览功能**：
```bash
textup preview --file your-article.md --platform weibo
```

### Q9: 如何处理图片和链接？

**当前版本限制**：
- 主要支持文本内容发布
- 图片需要预先上传到图床
- 链接会自动处理和转换

**最佳实践**：
```markdown
# 使用图床链接
![图片描述](https://your-image-host.com/image.jpg)

# 使用短链接
[链接文本](https://short-url.com/abc123)
```

---

## ⚡ 性能和稳定性问题

### Q10: 发布速度太慢怎么办？

**优化方案**：

1. **调整并发数**：
```bash
textup config --set app.max_concurrent=5
```

2. **启用缓存**：
```bash
textup config --set app.enable_cache=true
```

3. **使用批量发布**：
```bash
textup publish --directory ./articles --platforms weibo,zhihu
```

### Q11: 系统占用内存过多？

**解决方案**：

1. **调整并发数**：
```bash
textup config --set app.max_concurrent=2
```

2. **清理缓存**：
```bash
textup cache --clear
```

3. **重启应用**：
```bash
# 如果使用系统服务
sudo systemctl restart textup
```

---

## 🌐 平台特定问题

### Q12: 微博发布有字数限制吗？

**限制说明**：
- 微博单条内容限制 140 字符（中文）
- 长文本会自动截断或分段发布
- 建议使用预览功能检查效果

### Q13: 知乎发布需要注意什么？

**注意事项**：
- 知乎对内容质量要求较高
- 建议发布原创、有价值的内容
- 避免频繁发布相似内容

### Q14: 今日头条发布失败？

**可能原因**：
- 今日头条 API 集成仍在完善中
- 建议先使用微博和知乎平台
- 关注项目更新获取最新支持

---

## 🚀 高级使用问题

### Q15: 如何实现定时发布？

**方法1：使用系统 cron**：
```bash
# 编辑 crontab
crontab -e

# 添加定时任务（每天上午9点发布）
0 9 * * * cd /path/to/textup && textup publish --file daily-article.md --platforms weibo,zhihu
```

**方法2：使用内置调度**（开发中）：
```bash
textup publish --file article.md --schedule "2024-01-01 09:00" --platforms weibo,zhihu
```

### Q16: 如何批量管理多个账号？

**当前版本**：
- 支持单账号多平台发布
- 多账号功能在开发计划中

**临时解决方案**：
- 使用不同的配置文件
- 通过环境变量切换账号

### Q17: 如何扩展支持新平台？

**开发指南**：
1. 查看 [API 参考文档](../api/)
2. 参考现有适配器实现
3. 实现平台特定的适配器类
4. 添加相应的测试用例

**示例**：
```python
# 创建新平台适配器
class NewPlatformAdapter(BaseAdapter):
    def publish(self, content: Content) -> PublishResult:
        # 实现发布逻辑
        pass
```

---

## 🆘 仍然无法解决？

### 获取更多帮助

1. **查看详细日志**：
```bash
# 启用调试模式
textup --debug <command>

# 查看日志文件
tail -f ~/.textup/logs/textup.log
```

2. **查看相关文档**：
- [故障排除指南](troubleshooting-guide.md)
- [部署指南](deployment-guide.md)
- [本地测试指南](local-testing-guide.md)

3. **联系技术支持**：
- 提交 GitHub Issue
- 查看项目讨论区
- 联系项目维护团队

4. **提供问题信息**：
```bash
# 收集系统信息
textup --version
python --version
uv --version
cat ~/.textup/config/config.yaml
```

---

## 📝 文档贡献

发现新问题或有更好的解决方案？欢迎：

1. 提交 GitHub Issue 报告问题
2. 发起 Pull Request 完善文档
3. 在讨论区分享经验

**让我们一起完善 TextUp 的使用体验！** 🚀

---

**最后更新**：2025-09-04  
**文档版本**：v1.0  
**维护团队**：TextUp 开发团队
