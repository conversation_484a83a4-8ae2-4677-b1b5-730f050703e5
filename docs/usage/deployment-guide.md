# TextUp 部署和使用指南

## 📋 目录

- [系统要求](#系统要求)
- [安装部署](#安装部署)
- [配置设置](#配置设置)
- [平台认证](#平台认证)
- [使用说明](#使用说明)
- [故障排除](#故障排除)
- [生产部署](#生产部署)
- [API参考](#api参考)

## 🔧 系统要求

### 最低系统要求

- **操作系统**: Windows 10+, macOS 10.14+, Linux (Ubuntu 18.04+)
- **Python版本**: Python 3.9 或更高版本
- **内存**: 至少 512MB 可用内存
- **存储**: 至少 100MB 可用磁盘空间
- **网络**: 稳定的互联网连接

### 推荐系统配置

- **Python版本**: Python 3.11+
- **内存**: 2GB+ 可用内存
- **存储**: 1GB+ 可用磁盘空间
- **依赖管理**: uv 或 Poetry

## 📦 安装部署

### 方法1: 使用 uv (推荐)

```bash
# 安装 uv (如果尚未安装)
curl -LsSf https://astral.sh/uv/install.sh | sh

# 克隆项目
git clone https://github.com/textup-team/textup.git
cd textup

# 创建虚拟环境并安装依赖
uv venv
source .venv/bin/activate  # Linux/macOS
# 或 .venv\Scripts\activate  # Windows

# 安装项目
uv pip install -e .
```

### 方法2: 使用 pip

```bash
# 克隆项目
git clone https://github.com/textup-team/textup.git
cd textup

# 创建虚拟环境
python -m venv .venv
source .venv/bin/activate  # Linux/macOS
# 或 .venv\Scripts\activate  # Windows

# 升级 pip
pip install --upgrade pip

# 安装项目
pip install -e .
```

### 方法3: 从 PyPI 安装 (发布后)

```bash
pip install textup
```

### 验证安装

```bash
# 检查版本
textup --version

# 查看帮助
textup --help

# 运行基本命令
textup config --list
```

## ⚙️ 配置设置

### 初始化配置

```bash
# 交互式配置向导
textup config --interactive

# 或手动创建配置目录
mkdir -p ~/.textup/config
```

### 配置文件结构

TextUp 使用 YAML 格式的配置文件，默认位置：

- **Linux/macOS**: `~/.textup/config/config.yaml`
- **Windows**: `%USERPROFILE%\.textup\config\config.yaml`

#### 基础配置示例

```yaml
# ~/.textup/config/config.yaml
app:
  name: "TextUp"
  version: "1.0.0"
  log_level: "INFO"
  max_concurrent: 5

platforms:
  weibo:
    enabled: true
    client_id: "your_weibo_client_id"
    client_secret: "your_weibo_client_secret"
    redirect_uri: "https://your-domain.com/callback"
    
  zhihu:
    enabled: true
    client_id: "your_zhihu_client_id"
    client_secret: "your_zhihu_client_secret"
    redirect_uri: "https://your-domain.com/zhihu_callback"

database:
  type: "sqlite"
  path: "~/.textup/data/textup.db"

logging:
  level: "INFO"
  file: "~/.textup/logs/textup.log"
  rotation: "10MB"
  retention: "30 days"
```

### 环境变量配置

也可以使用环境变量进行配置：

```bash
export TEXTUP_CONFIG_DIR="/custom/path/config"
export TEXTUP_LOG_LEVEL="DEBUG"
export TEXTUP_WEIBO_CLIENT_ID="your_client_id"
export TEXTUP_WEIBO_CLIENT_SECRET="your_client_secret"
```

## 🔐 平台认证

### 微博认证

1. **获取开发者凭据**
   ```bash
   # 访问微博开放平台
   # https://open.weibo.com/
   ```

2. **配置认证信息**
   ```bash
   textup config --set platforms.weibo.client_id=YOUR_CLIENT_ID
   textup config --set platforms.weibo.client_secret=YOUR_CLIENT_SECRET
   textup config --set platforms.weibo.redirect_uri=YOUR_REDIRECT_URI
   ```

3. **执行OAuth认证**
   ```bash
   textup auth --interactive --platform weibo
   ```

### 知乎认证

1. **获取开发者凭据**
   ```bash
   # 访问知乎开发者平台
   # https://developers.zhihu.com/
   ```

2. **配置认证信息**
   ```bash
   textup config --set platforms.zhihu.client_id=YOUR_CLIENT_ID
   textup config --set platforms.zhihu.client_secret=YOUR_CLIENT_SECRET
   textup config --set platforms.zhihu.redirect_uri=YOUR_REDIRECT_URI
   ```

3. **执行OAuth认证**
   ```bash
   textup auth --interactive --platform zhihu
   ```

### 认证状态检查

```bash
# 检查所有平台认证状态
textup auth --status

# 检查特定平台
textup auth --status --platform weibo
```

## 📝 使用说明

### 基本发布流程

#### 1. 准备内容文件

创建 Markdown 格式的内容文件：

```markdown
# 我的第一篇文章

这是文章的正文内容。支持 **粗体**、*斜体* 和其他 Markdown 语法。

## 二级标题

- 列表项1
- 列表项2

> 这是引用内容

```python
# 代码块示例
print("Hello, TextUp!")
```

#### 2. 发布到单个平台

```bash
# 发布到微博
textup publish --file article.md --platform weibo

# 发布到知乎
textup publish --file article.md --platform zhihu
```

#### 3. 发布到多个平台

```bash
# 同时发布到多个平台
textup publish --file article.md --platforms weibo,zhihu

# 交互式发布
textup publish --interactive
```

### 高级使用功能

#### 批量发布

```bash
# 批量发布多个文件
textup publish --directory ./articles --platforms weibo,zhihu

# 使用通配符
textup publish --pattern "*.md" --platforms weibo,zhihu
```

#### 定时发布

```bash
# 定时发布 (需要 cron 配合)
textup publish --file article.md --platform weibo --schedule "2024-01-01 09:00"
```

#### 内容预览

```bash
# 预览转换后的内容
textup preview --file article.md --platform weibo

# 验证内容格式
textup validate --file article.md --platform zhihu
```

### 配置管理

```bash
# 查看当前配置
textup config --list

# 设置配置值
textup config --set app.log_level=DEBUG

# 获取配置值
textup config --get platforms.weibo.client_id

# 备份配置
textup config --backup config_backup.yaml

# 恢复配置
textup config --restore config_backup.yaml
```

### 认证管理

```bash
# 刷新过期的令牌
textup auth --refresh --platform weibo

# 撤销认证
textup auth --revoke --platform zhihu

# 重新认证
textup auth --interactive --platform weibo
```

## 🚨 故障排除

### 常见问题

#### 1. 导入错误

**问题**: `ModuleNotFoundError: No module named 'textup'`

**解决方案**:
```bash
# 确保在正确的虚拟环境中
source .venv/bin/activate

# 重新安装
pip install -e .

# 或使用 uv
uv pip install -e .
```

#### 2. 认证失败

**问题**: `AuthenticationError: Invalid credentials`

**解决方案**:
```bash
# 检查配置
textup config --get platforms.weibo

# 重新设置凭据
textup config --set platforms.weibo.client_id=CORRECT_ID
textup config --set platforms.weibo.client_secret=CORRECT_SECRET

# 重新认证
textup auth --interactive --platform weibo
```

#### 3. 网络连接问题

**问题**: `ConnectionError: Unable to connect`

**解决方案**:
```bash
# 检查网络连接
ping api.weibo.com

# 检查代理设置
export HTTP_PROXY=http://your-proxy:port
export HTTPS_PROXY=http://your-proxy:port

# 增加超时时间
textup config --set app.timeout=60
```

#### 4. 内容格式问题

**问题**: `ContentValidationError: Invalid format`

**解决方案**:
```bash
# 验证内容格式
textup validate --file your_article.md --platform weibo

# 查看平台限制
textup info --platform weibo

# 使用预览功能检查
textup preview --file your_article.md --platform weibo
```

### 日志调试

```bash
# 启用调试模式
textup --debug command

# 设置日志级别
textup config --set app.log_level=DEBUG

# 查看日志文件
tail -f ~/.textup/logs/textup.log
```

### 性能优化

```bash
# 调整并发数
textup config --set app.max_concurrent=10

# 启用缓存
textup config --set app.enable_cache=true

# 调整超时设置
textup config --set app.timeout=30
```

## 🏭 生产部署

### Docker 部署

```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装 uv
RUN pip install uv

# 复制项目文件
COPY . .

# 安装依赖
RUN uv pip install -e .

# 创建配置目录
RUN mkdir -p /app/config /app/data /app/logs

# 设置环境变量
ENV TEXTUP_CONFIG_DIR=/app/config
ENV TEXTUP_DATA_DIR=/app/data
ENV TEXTUP_LOG_DIR=/app/logs

# 暴露配置卷
VOLUME ["/app/config", "/app/data", "/app/logs"]

ENTRYPOINT ["textup"]
```

```bash
# 构建镜像
docker build -t textup:latest .

# 运行容器
docker run -v /path/to/config:/app/config \
           -v /path/to/data:/app/data \
           -v /path/to/logs:/app/logs \
           textup:latest --help
```

### Kubernetes 部署

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: textup-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: textup
  template:
    metadata:
      labels:
        app: textup
    spec:
      containers:
      - name: textup
        image: textup:latest
        env:
        - name: TEXTUP_CONFIG_DIR
          value: "/app/config"
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        - name: data-volume
          mountPath: /app/data
      volumes:
      - name: config-volume
        configMap:
          name: textup-config
      - name: data-volume
        persistentVolumeClaim:
          claimName: textup-data-pvc
```

### 系统服务部署

```ini
# /etc/systemd/system/textup.service
[Unit]
Description=TextUp Multi-platform Publishing Service
After=network.target

[Service]
Type=simple
User=textup
Group=textup
WorkingDirectory=/opt/textup
Environment=PATH=/opt/textup/.venv/bin
ExecStart=/opt/textup/.venv/bin/textup daemon
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# 启用服务
sudo systemctl enable textup
sudo systemctl start textup
sudo systemctl status textup
```

### 监控和日志

```yaml
# 使用 Prometheus 监控
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'textup'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: /metrics
    scrape_interval: 10s
```

## 📚 API参考

### CLI命令参考

#### config 命令

```bash
textup config [OPTIONS]

选项:
  -l, --list              列出所有配置
  -g, --get TEXT          获取配置值
  -s, --set TEXT          设置配置键
  --value TEXT            设置配置值
  --backup                备份配置文件
  --restore TEXT          从备份恢复配置
  -i, --interactive       交互式配置模式
  --help                  显示帮助信息
```

#### auth 命令

```bash
textup auth [OPTIONS]

选项:
  -i, --interactive       交互式认证模式
  --platform TEXT         指定平台 [weibo|zhihu]
  --status                显示认证状态
  --refresh               刷新访问令牌
  --revoke                撤销认证
  --help                  显示帮助信息
```

#### publish 命令

```bash
textup publish [OPTIONS]

选项:
  -f, --file TEXT         内容文件路径
  -d, --directory TEXT    批量发布目录
  --pattern TEXT          文件匹配模式
  --platform TEXT         目标平台
  --platforms TEXT        多个平台(逗号分隔)
  -i, --interactive       交互式发布模式
  --schedule TEXT         定时发布时间
  --dry-run              预览模式(不实际发布)
  --help                  显示帮助信息
```

### Python API参考

```python
from textup import TextUpClient
from textup.models import Content, Platform

# 创建客户端
client = TextUpClient(config_dir="/path/to/config")

# 创建内容
content = Content(
    title="API 发布测试",
    content="这是通过 API 发布的内容"
)

# 发布到单个平台
result = await client.publish_async(content, Platform.WEIBO)

# 发布到多个平台
results = await client.publish_to_multiple(
    content, 
    [Platform.WEIBO, Platform.ZHIHU]
)

# 批量发布
batch_results = await client.batch_publish(contents, Platform.WEIBO)
```

## 🔧 开发和贡献

### 开发环境设置

```bash
# 克隆开发版本
git clone https://github.com/textup-team/textup.git
cd textup

# 安装开发依赖
uv pip install -e ".[dev]"

# 安装 pre-commit hooks
pre-commit install

# 运行测试
pytest

# 代码格式化
black src/ tests/
flake8 src/ tests/
mypy src/textup/
```

### 测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_cli.py

# 生成覆盖率报告
pytest --cov=src/textup --cov-report=html

# 查看覆盖率报告
open htmlcov/index.html
```

## 📞 支持和社区

- **文档**: https://textup.readthedocs.io/
- **问题报告**: https://github.com/textup-team/textup/issues
- **功能请求**: https://github.com/textup-team/textup/discussions
- **邮件支持**: <EMAIL>

## 📄 许可证

本项目基于 MIT 许可证开源。详情请参阅 [LICENSE](../LICENSE) 文件。