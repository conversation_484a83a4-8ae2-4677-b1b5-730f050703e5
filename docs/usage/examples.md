# TextUp 使用案例和示例 📝

本文档提供 TextUp 在不同场景下的实际使用案例、配置示例和最佳实践，帮助您快速上手并充分利用工具的功能。

## 📋 目录

- [个人博主案例](#个人博主案例)
- [企业营销团队案例](#企业营销团队案例)
- [技术团队案例](#技术团队案例)
- [内容创作工作室案例](#内容创作工作室案例)
- [配置模板示例](#配置模板示例)
- [自动化脚本示例](#自动化脚本示例)

---

## 👤 个人博主案例

### 案例背景
**用户**：小李，技术博主  
**需求**：将技术文章同时发布到知乎、微博等平台  
**挑战**：手动发布耗时，格式适配困难

### 解决方案

**1. 环境配置**
```bash
# 安装和初始化
cd ~/textup
./start-textup.sh

# 配置平台凭证
textup config --set platforms.weibo.client_id=YOUR_WEIBO_ID
textup config --set platforms.zhihu.client_id=YOUR_ZHIHU_ID

# 完成认证
textup auth --interactive --platform weibo
textup auth --interactive --platform zhihu
```

**2. 内容创作工作流**
```bash
# 创建内容目录结构
mkdir -p ~/content/{drafts,ready,published}

# 创建技术文章模板
cat > ~/content/templates/tech-article.md << 'EOF'
# 技术文章标题

## 背景介绍
简要介绍技术背景和问题

## 解决方案
详细的技术方案和实现

## 代码示例
```python
# 示例代码
def example():
    pass
```

## 总结
总结要点和经验

**标签**: #技术分享 #编程 #开发经验
EOF
```

**3. 发布流程**
```bash
# 日常发布脚本
#!/bin/bash
# ~/scripts/daily-publish.sh

CONTENT_DIR="$HOME/content/ready"
PUBLISHED_DIR="$HOME/content/published"

for file in "$CONTENT_DIR"/*.md; do
    if [ -f "$file" ]; then
        echo "发布文章: $(basename "$file")"
        
        # 预览检查
        textup preview --file "$file" --platform zhihu
        
        # 发布到多平台
        textup publish --file "$file" --platforms weibo,zhihu
        
        # 移动到已发布目录
        mv "$file" "$PUBLISHED_DIR/"
        
        echo "发布完成: $(basename "$file")"
    fi
done
```

**4. 效果评估**
- **时间节省**：从每篇文章20分钟缩短到5分钟
- **发布频率**：从每周2篇提升到每周5篇
- **平台覆盖**：同时覆盖知乎和微博用户群体

---

## 🏢 企业营销团队案例

### 案例背景
**用户**：某科技公司营销团队  
**需求**：批量发布产品资讯和营销内容  
**挑战**：多人协作，内容审核，定时发布

### 解决方案

**1. 团队配置管理**
```yaml
# /etc/textup/team-config.yaml
app:
  name: "公司营销发布系统"
  log_level: "INFO"
  max_concurrent: 5

platforms:
  weibo:
    enabled: true
    client_id: "${COMPANY_WEIBO_ID}"
    client_secret: "${COMPANY_WEIBO_SECRET}"
  
  zhihu:
    enabled: true
    client_id: "${COMPANY_ZHIHU_ID}"
    client_secret: "${COMPANY_ZHIHU_SECRET}"

workflow:
  approval_required: true
  auto_schedule: true
  backup_enabled: true
```

**2. 内容管理流程**
```bash
# 内容目录结构
/company/content/
├── templates/          # 内容模板
├── drafts/            # 草稿内容
├── pending/           # 待审核内容
├── approved/          # 已审核内容
├── scheduled/         # 定时发布内容
└── published/         # 已发布内容

# 内容审核脚本
#!/bin/bash
# /company/scripts/content-review.sh

PENDING_DIR="/company/content/pending"
APPROVED_DIR="/company/content/approved"

echo "=== 内容审核系统 ==="
for file in "$PENDING_DIR"/*.md; do
    if [ -f "$file" ]; then
        echo "审核文件: $(basename "$file")"
        
        # 显示内容预览
        textup preview --file "$file" --platform weibo
        
        # 询问审核结果
        read -p "是否批准发布? (y/n): " approval
        
        if [[ "$approval" =~ ^[Yy]$ ]]; then
            mv "$file" "$APPROVED_DIR/"
            echo "✅ 已批准: $(basename "$file")"
        else
            echo "❌ 已拒绝: $(basename "$file")"
        fi
    fi
done
```

**3. 定时发布系统**
```bash
# 定时发布配置
# /company/config/schedule.yaml
schedule:
  morning_news:
    time: "09:00"
    directory: "/company/content/approved/news"
    platforms: ["weibo", "zhihu"]
    
  product_update:
    time: "14:00"
    directory: "/company/content/approved/products"
    platforms: ["weibo"]
    
  weekly_summary:
    time: "18:00"
    day: "friday"
    directory: "/company/content/approved/summary"
    platforms: ["weibo", "zhihu"]

# Crontab 配置
0 9 * * * /company/scripts/scheduled-publish.sh morning_news
0 14 * * * /company/scripts/scheduled-publish.sh product_update
0 18 * * 5 /company/scripts/scheduled-publish.sh weekly_summary
```

**4. 监控和报告**
```bash
# 营销效果监控脚本
#!/bin/bash
# /company/scripts/marketing-report.sh

REPORT_DATE=$(date +%Y-%m-%d)
REPORT_FILE="/company/reports/marketing-report-$REPORT_DATE.html"

echo "生成营销报告: $REPORT_DATE"

# 生成发布统计
textup analytics --period 7d --output json > /tmp/stats.json

# 生成HTML报告
cat > "$REPORT_FILE" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>营销发布报告 - $REPORT_DATE</title>
</head>
<body>
    <h1>营销发布周报</h1>
    <h2>发布统计</h2>
    <div id="stats">
        <!-- 统计数据 -->
    </div>
</body>
</html>
EOF

echo "报告已生成: $REPORT_FILE"
```

---

## 👨‍💻 技术团队案例

### 案例背景
**用户**：开源项目维护团队  
**需求**：发布项目更新、技术分享、社区动态  
**挑战**：技术内容适配，代码格式保持

### 解决方案

**1. 技术内容模板**
```markdown
# 项目更新模板
# ~/templates/project-update.md

# 项目名称 v版本号 发布

## 🚀 新功能
- 功能1：详细描述
- 功能2：详细描述

## 🐛 问题修复
- 修复问题1
- 修复问题2

## 📈 性能改进
- 性能提升点1
- 性能提升点2

## 💻 代码示例
```python
# 新功能使用示例
import project

# 使用新功能
result = project.new_feature()
print(result)
```

## 🔗 相关链接
- [GitHub Release](https://github.com/project/releases)
- [文档更新](https://docs.project.com)
- [讨论区](https://github.com/project/discussions)

**标签**: #开源项目 #技术更新 #编程
```

**2. 自动化发布流程**
```bash
# 项目发布自动化脚本
#!/bin/bash
# ~/scripts/release-publish.sh

VERSION=$1
if [ -z "$VERSION" ]; then
    echo "使用方法: $0 <version>"
    exit 1
fi

echo "准备发布 v$VERSION"

# 1. 生成发布说明
cat > "/tmp/release-$VERSION.md" << EOF
# 项目名称 v$VERSION 发布

## 更新内容
$(git log --oneline $(git describe --tags --abbrev=0)..HEAD)

## 下载地址
- [GitHub Release](https://github.com/project/releases/tag/v$VERSION)
- [Docker Image](https://hub.docker.com/r/project/app:$VERSION)

**标签**: #开源项目 #版本发布 #技术更新
EOF

# 2. 预览内容
textup preview --file "/tmp/release-$VERSION.md" --platform zhihu

# 3. 确认发布
read -p "确认发布到所有平台? (y/n): " confirm
if [[ "$confirm" =~ ^[Yy]$ ]]; then
    textup publish --file "/tmp/release-$VERSION.md" --platforms weibo,zhihu
    echo "✅ 发布完成"
else
    echo "❌ 发布取消"
fi
```

**3. 技术分享系列**
```bash
# 技术分享系列管理
mkdir -p ~/tech-series/{algorithms,architecture,tools}

# 系列文章发布脚本
#!/bin/bash
# ~/scripts/tech-series.sh

SERIES=$1
EPISODE=$2

if [ -z "$SERIES" ] || [ -z "$EPISODE" ]; then
    echo "使用方法: $0 <series> <episode>"
    echo "可用系列: algorithms, architecture, tools"
    exit 1
fi

SERIES_DIR="$HOME/tech-series/$SERIES"
ARTICLE_FILE="$SERIES_DIR/episode-$EPISODE.md"

if [ ! -f "$ARTICLE_FILE" ]; then
    echo "文章不存在: $ARTICLE_FILE"
    exit 1
fi

echo "发布技术系列: $SERIES - 第$EPISODE期"

# 添加系列标识
sed -i "1i# 技术系列：$SERIES - 第$EPISODE期\n" "$ARTICLE_FILE"

# 发布到平台
textup publish --file "$ARTICLE_FILE" --platforms weibo,zhihu

echo "✅ 系列文章发布完成"
```

---

## 🎨 内容创作工作室案例

### 案例背景
**用户**：多人内容创作工作室  
**需求**：管理多个账号，批量内容发布，效果跟踪  
**挑战**：多账号管理，内容分发策略，数据分析

### 解决方案

**1. 多账号配置管理**
```bash
# 账号配置目录结构
~/studio/
├── accounts/
│   ├── tech-account/
│   │   └── config.yaml
│   ├── lifestyle-account/
│   │   └── config.yaml
│   └── business-account/
│       └── config.yaml
├── content/
│   ├── tech/
│   ├── lifestyle/
│   └── business/
└── scripts/

# 账号切换脚本
#!/bin/bash
# ~/studio/scripts/switch-account.sh

ACCOUNT=$1
if [ -z "$ACCOUNT" ]; then
    echo "可用账号:"
    ls ~/studio/accounts/
    exit 1
fi

ACCOUNT_DIR="$HOME/studio/accounts/$ACCOUNT"
if [ ! -d "$ACCOUNT_DIR" ]; then
    echo "账号不存在: $ACCOUNT"
    exit 1
fi

# 设置配置目录
export TEXTUP_CONFIG_DIR="$ACCOUNT_DIR"
echo "已切换到账号: $ACCOUNT"

# 验证配置
textup config --list
```

**2. 内容分发策略**
```yaml
# ~/studio/config/distribution-strategy.yaml
strategies:
  tech_content:
    primary_platforms: ["zhihu", "weibo"]
    optimal_times: ["09:00", "20:00"]
    tags: ["技术", "编程", "开发"]
    
  lifestyle_content:
    primary_platforms: ["weibo", "xiaohongshu"]
    optimal_times: ["12:00", "18:00"]
    tags: ["生活", "分享", "日常"]
    
  business_content:
    primary_platforms: ["zhihu", "toutiao"]
    optimal_times: ["10:00", "15:00"]
    tags: ["商业", "创业", "管理"]
```

**3. 批量内容处理**
```bash
# 批量内容处理脚本
#!/bin/bash
# ~/studio/scripts/batch-process.sh

CONTENT_TYPE=$1
if [ -z "$CONTENT_TYPE" ]; then
    echo "使用方法: $0 <tech|lifestyle|business>"
    exit 1
fi

CONTENT_DIR="$HOME/studio/content/$CONTENT_TYPE"
STRATEGY_FILE="$HOME/studio/config/distribution-strategy.yaml"

echo "处理 $CONTENT_TYPE 类型内容"

# 读取分发策略
PLATFORMS=$(yq eval ".strategies.${CONTENT_TYPE}_content.primary_platforms[]" "$STRATEGY_FILE" | tr '\n' ',' | sed 's/,$//')
TIMES=$(yq eval ".strategies.${CONTENT_TYPE}_content.optimal_times[]" "$STRATEGY_FILE")

echo "目标平台: $PLATFORMS"
echo "发布时间: $TIMES"

# 批量发布
for file in "$CONTENT_DIR"/*.md; do
    if [ -f "$file" ]; then
        echo "处理文件: $(basename "$file")"
        
        # 选择最佳发布时间
        OPTIMAL_TIME=$(echo "$TIMES" | head -1)
        
        # 定时发布
        textup publish --file "$file" --platforms "$PLATFORMS" --schedule "$OPTIMAL_TIME"
        
        echo "已安排发布: $(basename "$file") at $OPTIMAL_TIME"
    fi
done
```

---

## ⚙️ 配置模板示例

### 个人用户配置模板
```yaml
# ~/.textup/config/personal-template.yaml
app:
  name: "个人内容发布工具"
  log_level: "INFO"
  max_concurrent: 2
  timeout: 30

platforms:
  weibo:
    enabled: true
    client_id: "${WEIBO_CLIENT_ID}"
    client_secret: "${WEIBO_CLIENT_SECRET}"
    redirect_uri: "http://localhost:8080/callback"
    
  zhihu:
    enabled: true
    client_id: "${ZHIHU_CLIENT_ID}"
    client_secret: "${ZHIHU_CLIENT_SECRET}"
    redirect_uri: "http://localhost:8080/zhihu/callback"

content:
  default_tags: ["原创", "分享"]
  auto_summary: true
  max_length: 2000

logging:
  level: "INFO"
  file: "~/.textup/logs/textup.log"
  rotation: "5MB"
  retention: "14 days"
```

### 企业用户配置模板
```yaml
# /etc/textup/enterprise-template.yaml
app:
  name: "企业内容发布系统"
  log_level: "INFO"
  max_concurrent: 10
  timeout: 60
  enable_cache: true

platforms:
  weibo:
    enabled: true
    client_id: "${ENTERPRISE_WEIBO_ID}"
    client_secret: "${ENTERPRISE_WEIBO_SECRET}"
    redirect_uri: "https://company.com/textup/callback"
    
  zhihu:
    enabled: true
    client_id: "${ENTERPRISE_ZHIHU_ID}"
    client_secret: "${ENTERPRISE_ZHIHU_SECRET}"
    redirect_uri: "https://company.com/textup/zhihu/callback"

workflow:
  approval_required: true
  auto_backup: true
  audit_log: true

security:
  encrypt_tokens: true
  mask_sensitive_logs: true
  session_timeout: 3600

monitoring:
  enable_metrics: true
  alert_on_failure: true
  report_interval: "daily"
```

---

## 🤖 自动化脚本示例

### 每日发布自动化
```bash
#!/bin/bash
# ~/scripts/daily-automation.sh

# 设置变量
DATE=$(date +%Y-%m-%d)
CONTENT_DIR="$HOME/content/scheduled/$DATE"
LOG_FILE="$HOME/logs/daily-publish-$DATE.log"

echo "开始每日发布任务: $DATE" | tee "$LOG_FILE"

# 检查是否有待发布内容
if [ ! -d "$CONTENT_DIR" ] || [ -z "$(ls -A "$CONTENT_DIR")" ]; then
    echo "没有待发布内容" | tee -a "$LOG_FILE"
    exit 0
fi

# 发布内容
for file in "$CONTENT_DIR"/*.md; do
    if [ -f "$file" ]; then
        echo "发布: $(basename "$file")" | tee -a "$LOG_FILE"
        
        # 发布到多平台
        if textup publish --file "$file" --platforms weibo,zhihu >> "$LOG_FILE" 2>&1; then
            echo "✅ 发布成功: $(basename "$file")" | tee -a "$LOG_FILE"
            
            # 移动到已发布目录
            mv "$file" "$HOME/content/published/"
        else
            echo "❌ 发布失败: $(basename "$file")" | tee -a "$LOG_FILE"
        fi
    fi
done

echo "每日发布任务完成: $DATE" | tee -a "$LOG_FILE"
```

### 内容备份脚本
```bash
#!/bin/bash
# ~/scripts/content-backup.sh

BACKUP_DIR="$HOME/backups/content"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/content-backup-$TIMESTAMP.tar.gz"

echo "开始内容备份..."

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 打包内容
tar -czf "$BACKUP_FILE" \
    "$HOME/content/" \
    "$HOME/.textup/config/" \
    "$HOME/.textup/logs/" \
    --exclude="*.tmp" \
    --exclude="cache/*"

echo "备份完成: $BACKUP_FILE"

# 清理旧备份（保留最近30个）
ls -t "$BACKUP_DIR"/content-backup-*.tar.gz | tail -n +31 | xargs rm -f

echo "备份清理完成"
```

---

这些案例展示了 TextUp 在不同场景下的灵活应用。您可以根据自己的需求调整和定制这些示例。

**记住：最好的配置是适合您具体需求的配置！** 🚀

---

**最后更新**：2025-09-04  
**文档版本**：v1.0  
**维护团队**：TextUp 开发团队
