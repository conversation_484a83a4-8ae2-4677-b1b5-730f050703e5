# TextUp 故障排除指南 🛠️

本指南提供系统性的问题诊断和解决方案，帮助您快速定位和修复 TextUp 使用过程中遇到的各种问题。

## 📋 目录

- [问题诊断流程](#问题诊断流程)
- [环境和安装问题](#环境和安装问题)
- [配置和认证问题](#配置和认证问题)
- [发布功能问题](#发布功能问题)
- [性能和稳定性问题](#性能和稳定性问题)
- [日志分析和调试](#日志分析和调试)

---

## 🔍 问题诊断流程

### 第一步：收集基本信息

```bash
# 1. 检查系统环境
echo "=== 系统信息 ==="
uname -a
python --version
uv --version

# 2. 检查 TextUp 状态
echo "=== TextUp 状态 ==="
textup --version
textup config --list

# 3. 检查最近日志
echo "=== 最近日志 ==="
tail -20 ~/.textup/logs/textup.log
```

### 第二步：问题分类

根据错误信息确定问题类型：

| 错误类型 | 关键词 | 跳转章节 |
|----------|--------|----------|
| 命令找不到 | `command not found` | [环境问题](#环境和安装问题) |
| 导入错误 | `ModuleNotFoundError` | [环境问题](#环境和安装问题) |
| 认证失败 | `AuthenticationError` | [认证问题](#配置和认证问题) |
| 网络错误 | `ConnectionError` | [发布问题](#发布功能问题) |
| 配置错误 | `ConfigurationError` | [配置问题](#配置和认证问题) |
| 内容错误 | `ContentValidationError` | [发布问题](#发布功能问题) |

### 第三步：启用调试模式

```bash
# 启用详细日志
textup config --set app.log_level=DEBUG

# 使用调试模式运行命令
textup --debug <your-command>

# 查看详细日志
tail -f ~/.textup/logs/textup.log
```

---

## 🔧 环境和安装问题

### 问题1：`textup: command not found`

**诊断步骤**：
```bash
# 1. 检查虚拟环境
echo $VIRTUAL_ENV

# 2. 检查 Python 路径
which python
which pip

# 3. 检查安装状态
pip list | grep textup
```

**解决方案**：
```bash
# 方案A：重新激活虚拟环境
cd /path/to/textup
source .venv/bin/activate  # macOS/Linux
# 或 .venv\Scripts\activate  # Windows

# 方案B：重新安装
uv pip install -e .

# 方案C：检查 PATH 环境变量
echo $PATH
export PATH=$PATH:~/.local/bin
```

### 问题2：`ModuleNotFoundError: No module named 'textup'`

**诊断步骤**：
```bash
# 1. 检查安装位置
pip show textup

# 2. 检查 Python 路径
python -c "import sys; print(sys.path)"

# 3. 检查项目结构
ls -la src/textup/
```

**解决方案**：
```bash
# 方案A：开发模式安装
pip install -e .

# 方案B：检查 PYTHONPATH
export PYTHONPATH=$PYTHONPATH:$(pwd)/src

# 方案C：重新创建虚拟环境
rm -rf .venv
uv venv
source .venv/bin/activate
uv pip install -e .
```

### 问题3：依赖冲突或版本不兼容

**诊断步骤**：
```bash
# 1. 检查依赖冲突
pip check

# 2. 查看依赖树
pip list --format=freeze

# 3. 检查 Python 版本兼容性
python --version
```

**解决方案**：
```bash
# 方案A：更新依赖
pip install --upgrade pip
uv pip install --upgrade -e .

# 方案B：清理重装
pip uninstall textup
pip cache purge
uv pip install -e .

# 方案C：使用兼容的 Python 版本
pyenv install 3.11.0
pyenv local 3.11.0
```

---

## 🔐 配置和认证问题

### 问题4：配置文件找不到或损坏

**诊断步骤**：
```bash
# 1. 检查配置文件位置
ls -la ~/.textup/config/

# 2. 验证配置文件格式
textup config --validate

# 3. 查看配置内容
cat ~/.textup/config/config.yaml
```

**解决方案**：
```bash
# 方案A：重新初始化配置
textup config --init

# 方案B：从备份恢复
textup config --restore backup.yaml

# 方案C：手动创建配置目录
mkdir -p ~/.textup/config
mkdir -p ~/.textup/logs
mkdir -p ~/.textup/data
```

### 问题5：平台认证失败

**诊断步骤**：
```bash
# 1. 检查认证状态
textup auth --status

# 2. 验证凭证配置
textup config --get platforms.weibo.client_id
textup config --get platforms.weibo.client_secret

# 3. 测试网络连接
curl -I https://api.weibo.com
curl -I https://www.zhihu.com
```

**解决方案**：
```bash
# 方案A：重新设置凭证
textup config --set platforms.weibo.client_id=YOUR_CLIENT_ID
textup config --set platforms.weibo.client_secret=YOUR_CLIENT_SECRET

# 方案B：重新认证
textup auth --revoke --platform weibo
textup auth --interactive --platform weibo

# 方案C：检查回调地址
textup config --set platforms.weibo.redirect_uri=http://localhost:8080/callback
```

### 问题6：权限和访问问题

**诊断步骤**：
```bash
# 1. 检查文件权限
ls -la ~/.textup/
ls -la ~/.textup/config/

# 2. 检查磁盘空间
df -h ~/.textup/

# 3. 检查进程权限
ps aux | grep textup
```

**解决方案**：
```bash
# 方案A：修复权限
chmod -R 755 ~/.textup/
chown -R $USER:$USER ~/.textup/

# 方案B：清理空间
rm -rf ~/.textup/logs/*.log.old
rm -rf ~/.textup/cache/*

# 方案C：使用不同位置
export TEXTUP_CONFIG_DIR=/tmp/textup_config
mkdir -p $TEXTUP_CONFIG_DIR
```

---

## 📝 发布功能问题

### 问题7：网络连接失败

**诊断步骤**：
```bash
# 1. 测试基本网络连接
ping -c 3 api.weibo.com
ping -c 3 www.zhihu.com

# 2. 测试 HTTPS 连接
curl -I https://api.weibo.com/2/statuses/update.json

# 3. 检查代理设置
echo $HTTP_PROXY
echo $HTTPS_PROXY
```

**解决方案**：
```bash
# 方案A：配置代理
export HTTP_PROXY=http://proxy.company.com:8080
export HTTPS_PROXY=http://proxy.company.com:8080

# 方案B：增加超时时间
textup config --set app.timeout=60
textup config --set app.retry_count=3

# 方案C：使用不同网络
# 尝试切换网络环境或使用移动热点
```

### 问题8：内容格式验证失败

**诊断步骤**：
```bash
# 1. 验证文件格式
file your-article.md
head -10 your-article.md

# 2. 检查内容长度
wc -c your-article.md
wc -w your-article.md

# 3. 验证平台兼容性
textup validate --file your-article.md --platform weibo
```

**解决方案**：
```bash
# 方案A：修复文件编码
iconv -f UTF-8 -t UTF-8 your-article.md > fixed-article.md

# 方案B：简化内容格式
# 移除复杂的 Markdown 语法
# 减少特殊字符使用

# 方案C：分段发布
textup publish --file article-part1.md --platform weibo
textup publish --file article-part2.md --platform weibo
```

### 问题9：发布状态异常

**诊断步骤**：
```bash
# 1. 检查发布历史
textup history --platform weibo --limit 10

# 2. 查看任务状态
textup status --verbose

# 3. 检查平台限制
textup info --platform weibo
```

**解决方案**：
```bash
# 方案A：重试失败任务
textup retry --task-id <task-id>

# 方案B：清理异常状态
textup cleanup --platform weibo

# 方案C：重置发布状态
textup reset --platform weibo --confirm
```

---

## ⚡ 性能和稳定性问题

### 问题10：内存使用过高

**诊断步骤**：
```bash
# 1. 监控内存使用
top -p $(pgrep -f textup)
ps aux | grep textup

# 2. 检查并发设置
textup config --get app.max_concurrent

# 3. 查看缓存使用
du -sh ~/.textup/cache/
```

**解决方案**：
```bash
# 方案A：调整并发数
textup config --set app.max_concurrent=2

# 方案B：清理缓存
textup cache --clear
rm -rf ~/.textup/cache/*

# 方案C：重启服务
# 如果使用系统服务
sudo systemctl restart textup
```

### 问题11：响应速度慢

**诊断步骤**：
```bash
# 1. 测试网络延迟
ping -c 10 api.weibo.com

# 2. 检查系统负载
uptime
iostat 1 5

# 3. 分析性能瓶颈
textup --profile publish --file test.md --platform weibo --dry-run
```

**解决方案**：
```bash
# 方案A：优化配置
textup config --set app.enable_cache=true
textup config --set app.batch_size=5

# 方案B：使用本地缓存
textup config --set cache.type=local
textup config --set cache.ttl=3600

# 方案C：分时发布
# 避开平台高峰期
textup publish --schedule "02:00" --file article.md
```

---

## 📊 日志分析和调试

### 启用详细日志

```bash
# 1. 设置日志级别
textup config --set app.log_level=DEBUG

# 2. 启用文件日志
textup config --set logging.file=~/.textup/logs/debug.log

# 3. 设置日志轮转
textup config --set logging.rotation=10MB
textup config --set logging.retention="7 days"
```

### 日志分析技巧

```bash
# 1. 查看错误日志
grep -i error ~/.textup/logs/textup.log

# 2. 分析网络请求
grep -i "http" ~/.textup/logs/textup.log

# 3. 跟踪特定操作
grep -A 5 -B 5 "publish" ~/.textup/logs/textup.log

# 4. 实时监控日志
tail -f ~/.textup/logs/textup.log | grep -i error
```

### 性能分析

```bash
# 1. 启用性能分析
textup --profile --debug publish --file test.md --dry-run

# 2. 分析执行时间
time textup publish --file test.md --platform weibo --dry-run

# 3. 内存使用分析
/usr/bin/time -v textup publish --file test.md --dry-run
```

---

## 🆘 高级故障排除

### 完全重置系统

```bash
# 警告：这将删除所有配置和数据
echo "确认要重置系统吗？(y/N)"
read -r response
if [[ "$response" =~ ^[Yy]$ ]]; then
    # 停止服务
    sudo systemctl stop textup 2>/dev/null || true
    
    # 删除配置和数据
    rm -rf ~/.textup/
    
    # 重新安装
    pip uninstall textup -y
    pip install -e .
    
    # 重新初始化
    textup config --init
    
    echo "系统重置完成"
fi
```

### 生成诊断报告

```bash
#!/bin/bash
# 生成完整的诊断报告

REPORT_FILE="textup-diagnostic-$(date +%Y%m%d-%H%M%S).txt"

echo "=== TextUp 诊断报告 ===" > $REPORT_FILE
echo "生成时间: $(date)" >> $REPORT_FILE
echo "" >> $REPORT_FILE

echo "=== 系统信息 ===" >> $REPORT_FILE
uname -a >> $REPORT_FILE
python --version >> $REPORT_FILE
pip --version >> $REPORT_FILE
echo "" >> $REPORT_FILE

echo "=== TextUp 状态 ===" >> $REPORT_FILE
textup --version >> $REPORT_FILE 2>&1
textup config --list >> $REPORT_FILE 2>&1
textup auth --status >> $REPORT_FILE 2>&1
echo "" >> $REPORT_FILE

echo "=== 最近日志 ===" >> $REPORT_FILE
tail -50 ~/.textup/logs/textup.log >> $REPORT_FILE 2>&1

echo "诊断报告已生成: $REPORT_FILE"
```

---

## 📞 获取更多帮助

### 联系技术支持

如果以上方法都无法解决问题，请：

1. **生成诊断报告**（使用上面的脚本）
2. **收集错误信息**：
   - 完整的错误消息
   - 操作步骤
   - 系统环境信息
3. **提交 GitHub Issue**，包含：
   - 问题描述
   - 重现步骤
   - 诊断报告
   - 期望结果

### 相关文档

- [常见问题解答](faq.md)
- [部署指南](deployment-guide.md)
- [本地测试指南](local-testing-guide.md)
- [API 参考文档](../api/)

---

**记住：大多数问题都有解决方案，保持耐心，系统性地排查问题！** 🚀

---

**最后更新**：2025-09-04  
**文档版本**：v1.0  
**维护团队**：TextUp 开发团队
