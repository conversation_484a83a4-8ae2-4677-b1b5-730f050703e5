# TextUp 快速上手指南 🚀

## 欢迎使用 TextUp！

TextUp 是一个多平台内容发布工具，可以让您一次性将文章发布到微博、知乎、今日头条等多个平台。

本指南将帮助您在**10分钟内**完成安装和首次发布！

---

## 📋 第一步：系统准备

### 检查Python版本
打开终端（Terminal），输入：
```bash
python --version
```
或者
```bash
python3 --version
```

**要求**：Python 3.9 或更高版本。如果版本过低，请先升级Python。

### 安装uv（推荐的包管理器）
```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows (PowerShell)
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

---

## 📦 第二步：安装TextUp

### 1. 下载项目
```bash
# 如果您有项目源码
cd /path/to/textup  # 替换为实际路径

# 或者克隆项目（如果有Git仓库）
git clone <repository-url>
cd textup
```

### 2. 安装依赖
```bash
# 创建虚拟环境
uv venv

# 激活虚拟环境
# macOS/Linux:
source .venv/bin/activate
# Windows:
.venv\Scripts\activate

# 安装项目
uv pip install -e .
```

### 3. 验证安装
```bash
textup --version
```
如果显示版本号，说明安装成功！

---

## ⚙️ 第三步：基础配置

### 1. 初始化配置
```bash
textup config --interactive
```

按照提示输入基本信息：
- 应用名称：随便填，比如"我的发布工具"
- 日志级别：选择"INFO"

### 2. 查看配置
```bash
textup config --list
```

---

## 🔐 第四步：平台认证设置

### 微博平台设置

#### 1. 获取微博开发者凭证
1. 访问 https://open.weibo.com/
2. 注册开发者账号
3. 创建应用，获取：
   - `App Key` (客户端ID)
   - `App Secret` (客户端密钥)
   - 设置回调地址：`http://localhost:8080/callback`

#### 2. 配置微博凭证
```bash
# 设置微博凭证
textup config --set platforms.weibo.client_id=你的微博App_Key
textup config --set platforms.weibo.client_secret=你的微博App_Secret
textup config --set platforms.weibo.redirect_uri=http://localhost:8080/callback
```

#### 3. 进行微博认证
```bash
textup auth --interactive --platform weibo
```
系统会：
1. 自动打开浏览器
2. 跳转到微博授权页面
3. 授权后获得授权码
4. 将授权码粘贴到终端

### 知乎平台设置

#### 1. 获取知乎开发者凭证
1. 访问 https://developers.zhihu.com/
2. 注册开发者账号
3. 创建应用，获取：
   - `client_id`
   - `client_secret`
   - 设置回调地址：`http://localhost:8080/zhihu/callback`

#### 2. 配置知乎凭证
```bash
textup config --set platforms.zhihu.client_id=你的知乎client_id
textup config --set platforms.zhihu.client_secret=你的知乎client_secret
textup config --set platforms.zhihu.redirect_uri=http://localhost:8080/zhihu/callback
```

#### 3. 进行知乎认证
```bash
textup auth --interactive --platform zhihu
```

### 检查认证状态
```bash
textup auth --status
```
应该显示已认证的平台状态。

---

## 📝 第五步：准备您的第一篇文章

### 1. 创建文章文件
创建一个 Markdown 文件，比如 `my-first-article.md`：

```markdown
# 我的第一篇文章

这是我使用 TextUp 发布的第一篇文章！

## 主要内容

TextUp 是一个很棒的多平台发布工具，它可以：

- 同时发布到多个平台
- 支持 Markdown 格式
- 自动处理格式转换
- 简单易用

## 结论

现在我可以轻松地管理我的内容发布了！

**标签**: #TextUp #多平台发布 #效率工具
```

### 2. 保存文件
将文件保存为 `my-first-article.md`

---

## 🚀 第六步：发布文章

### 方法1：交互式发布（推荐新手）
```bash
textup publish --interactive
```

按照提示：
1. 选择文章文件：输入 `my-first-article.md` 的完整路径
2. 选择发布平台：输入 `weibo` 或 `zhihu` 或 `weibo,zhihu`
3. 确认发布：输入 `y`

### 方法2：直接命令发布
```bash
# 发布到微博
textup publish --file my-first-article.md --platform weibo

# 发布到知乎
textup publish --file my-first-article.md --platform zhihu

# 同时发布到两个平台
textup publish --file my-first-article.md --platforms weibo,zhihu
```

### 3. 查看发布结果
成功发布后，系统会显示：
- ✅ 发布成功
- 📄 文章标题
- 🔗 发布链接
- ⏰ 发布时间

---

## 📚 常用命令速查

### 配置管理
```bash
# 查看所有配置
textup config --list

# 设置单个配置项
textup config --set key=value

# 交互式配置
textup config --interactive

# 备份配置
textup config --backup backup.yaml

# 恢复配置
textup config --restore backup.yaml
```

### 认证管理
```bash
# 查看认证状态
textup auth --status

# 交互式认证
textup auth --interactive --platform 平台名

# 撤销认证
textup auth --revoke --platform 平台名
```

### 内容发布
```bash
# 交互式发布
textup publish --interactive

# 指定文件和平台
textup publish --file 文件路径 --platform 平台名

# 多平台发布
textup publish --file 文件路径 --platforms 平台1,平台2

# 预览模式（不实际发布）
textup publish --file 文件路径 --platform 平台名 --dry-run
```

---

## ❗ 常见问题解决

### 1. 命令找不到：`textup: command not found`

**解决方案**：
```bash
# 确保虚拟环境已激活
source .venv/bin/activate  # macOS/Linux
# 或
.venv\Scripts\activate     # Windows

# 重新安装
uv pip install -e .
```

### 2. 认证失败：`AuthenticationError`

**解决方案**：
1. 检查凭证是否正确：
```bash
textup config --get platforms.weibo.client_id
```
2. 重新设置凭证：
```bash
textup config --set platforms.weibo.client_id=正确的值
```
3. 重新认证：
```bash
textup auth --interactive --platform weibo
```

### 3. 发布失败：`Network Error`

**解决方案**：
1. 检查网络连接
2. 检查认证状态：
```bash
textup auth --status
```
3. 如果令牌过期，重新认证

### 4. 文件找不到：`File not found`

**解决方案**：
1. 检查文件路径是否正确
2. 使用绝对路径：
```bash
textup publish --file /完整/路径/my-article.md --platform weibo
```

---

## 🎯 高级使用技巧

### 1. 批量发布
```bash
# 发布目录下所有 .md 文件
textup publish --directory ./articles --platforms weibo,zhihu

# 使用通配符
textup publish --pattern "*.md" --platforms weibo,zhihu
```

### 2. 内容预览
```bash
# 预览转换后的内容
textup preview --file my-article.md --platform weibo
```

### 3. 内容验证
```bash
# 验证内容格式
textup validate --file my-article.md --platform zhihu
```

---

## 📞 获取帮助

### 查看帮助信息
```bash
# 主命令帮助
textup --help

# 子命令帮助
textup config --help
textup auth --help
textup publish --help
```

### 启用调试模式
如果遇到问题，可以启用调试模式获取更多信息：
```bash
textup --debug publish --file my-article.md --platform weibo
```

### 查看日志
```bash
# 查看日志文件
tail -f ~/.textup/logs/textup.log
```

---

## 🎉 恭喜！

您已经成功完成了 TextUp 的基础设置和使用！

### 下一步建议：
1. 尝试发布几篇测试文章
2. 探索更多平台的接入
3. 学习批量发布功能
4. 自定义配置优化

### 更多资源：
- 📖 [详细部署指南](deployment-guide.md) - 生产环境部署
- 🧪 [本地测试指南](local-testing-guide.md) - 完整功能测试
- ❓ [常见问题解答](faq.md) - 快速解决问题
- 🛠️ [故障排除指南](troubleshooting-guide.md) - 问题诊断
- 📊 [最佳实践](best-practices.md) - 使用技巧
- 📝 [使用案例](examples.md) - 实际应用示例
- 🔧 [API参考文档](../api/) - 开发者文档
- 🐛 [问题反馈](https://github.com/textup-team/textup/issues)

**需要帮助？** 查看 [用户文档中心](README.md) 获取完整的文档导航。

祝您使用愉快！🎈