#!/bin/bash
# 文档链接验证脚本

set -e

USAGE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DOCS_DIR="$(dirname "$USAGE_DIR")"
PROJECT_ROOT="$(dirname "$DOCS_DIR")"

echo "=== TextUp 文档链接验证 ==="
echo "验证目录: $USAGE_DIR"
echo "项目根目录: $PROJECT_ROOT"
echo ""

# 验证计数器
TOTAL_LINKS=0
VALID_LINKS=0
INVALID_LINKS=0

# 验证单个文件的链接
validate_file_links() {
    local file="$1"
    local filename=$(basename "$file")

    echo "检查文件: $filename"

    # 提取所有 Markdown 链接并处理
    local file_total=0
    local file_valid=0
    local file_invalid=0

    while IFS= read -r line; do
        # 查找链接模式，包括被粗体包围的链接
        echo "$line" | grep -oE '\*?\*?\[[^\]]*\]\([^)]+\)\*?\*?' | while IFS= read -r link; do
            # 提取链接路径，去除粗体标记
            link_path=$(echo "$link" | sed 's/.*](\([^)]*\)).*/\1/' | sed 's/\*//g')

            # 跳过外部链接和锚点链接
            if [[ "$link_path" =~ ^https?:// ]] || [[ "$link_path" =~ ^mailto: ]] || [[ "$link_path" =~ ^# ]]; then
                continue
            fi

            file_total=$((file_total + 1))

            # 解析相对路径
            if [[ "$link_path" =~ ^\.\. ]]; then
                # 相对于 docs 目录的路径
                target_path="$DOCS_DIR/${link_path#../}"
            elif [[ "$link_path" =~ ^\. ]]; then
                # 相对于当前目录的路径
                target_path="$USAGE_DIR/${link_path#./}"
            else
                # 相对于当前目录的路径
                target_path="$USAGE_DIR/$link_path"
            fi

            # 检查文件是否存在
            if [ -f "$target_path" ] || [ -d "$target_path" ]; then
                echo "  ✅ $link_path"
                file_valid=$((file_valid + 1))
            else
                echo "  ❌ $link_path (目标不存在: $target_path)"
                file_invalid=$((file_invalid + 1))
            fi
        done
    done < "$file"

    # 更新全局计数器
    TOTAL_LINKS=$((TOTAL_LINKS + file_total))
    VALID_LINKS=$((VALID_LINKS + file_valid))
    INVALID_LINKS=$((INVALID_LINKS + file_invalid))

    if [ $file_total -gt 0 ]; then
        echo "  文件统计: $file_total 个链接, $file_valid 个有效, $file_invalid 个无效"
    else
        echo "  无链接"
    fi
    echo ""
}

# 验证所有 Markdown 文件
for file in "$USAGE_DIR"/*.md; do
    if [ -f "$file" ]; then
        validate_file_links "$file"
    fi
done

echo "=== 验证结果 ==="
echo "总链接数: $TOTAL_LINKS"
echo "有效链接: $VALID_LINKS"
echo "无效链接: $INVALID_LINKS"

if [ $INVALID_LINKS -eq 0 ]; then
    echo "🎉 所有链接验证通过！"
    exit 0
else
    echo "⚠️  发现 $INVALID_LINKS 个无效链接，请检查修复"
    exit 1
fi
