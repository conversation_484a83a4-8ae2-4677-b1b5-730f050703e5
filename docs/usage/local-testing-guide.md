# TextUp 本地测试完整指南 🧪

> **目标**: 帮助完全不了解系统的人，通过简单的命令行操作，完成TextUp的本地安装、配置和功能测试

---

## 🎯 测试目标

本指南将帮助您：
- ✅ 在本地启动TextUp应用
- ✅ 配置多平台发布功能  
- ✅ 测试文章发布到微博、知乎、今日头条
- ✅ 验证系统功能完整性
- ✅ 生成测试报告

**预计用时**: 30-45分钟

---

## 📋 第一部分：环境准备

### 1.1 检查系统环境
```bash
# 检查Python版本（需要3.9+）
python3 --version

# 检查是否有pip
pip3 --version

# 检查磁盘空间（需要至少500MB）
df -h .
```

### 1.2 安装uv包管理器
```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# 重新加载shell配置
source ~/.bashrc
# 或者
source ~/.zshrc

# 验证安装
uv --version
```

### 1.3 进入项目目录
```bash
# 进入TextUp项目根目录
cd /Volumes/mini_matrix/github/uploader/textup

# 确认项目结构
ls -la
# 应该看到：src/ tests/ docs/ pyproject.toml 等文件
```

---

## 📦 第二部分：安装和启动

### 2.1 创建虚拟环境
```bash
# 创建Python虚拟环境
uv venv

# 激活虚拟环境
source .venv/bin/activate

# 确认激活成功（命令提示符前应该有(.venv)）
which python
```

### 2.2 安装TextUp
```bash
# 安装项目依赖
uv pip install -e .

# 验证安装成功
textup --version

# 查看帮助信息
textup --help
```

### 2.3 初始化配置目录
```bash
# 创建配置目录
mkdir -p ~/.textup/config
mkdir -p ~/.textup/logs
mkdir -p ~/.textup/data

# 查看当前配置
textup config --list
```

---

## ⚙️ 第三部分：基础配置测试

### 3.1 配置应用基本信息
```bash
# 设置应用名称
textup config --set app.name="TextUp测试应用"

# 设置日志级别
textup config --set app.log_level="DEBUG"

# 设置最大并发数
textup config --set app.max_concurrent=3

# 验证配置
textup config --list
```

### 3.2 测试配置功能
```bash
# 测试配置读取
textup config --get app.name

# 测试配置备份
textup config --backup test-backup.yaml

# 验证备份文件
ls -la test-backup.yaml
cat test-backup.yaml
```

---

## 🔐 第四部分：平台认证配置

### 4.1 配置微博平台（测试用）
```bash
# 设置微博测试凭证
textup config --set platforms.weibo.client_id="test_weibo_client_id"
textup config --set platforms.weibo.client_secret="test_weibo_client_secret"
textup config --set platforms.weibo.redirect_uri="http://localhost:8080/auth/weibo/callback"

# 验证微博配置
textup config --get platforms.weibo
```

### 4.2 配置知乎平台（测试用）
```bash
# 设置知乎测试凭证
textup config --set platforms.zhihu.client_id="test_zhihu_client_id"
textup config --set platforms.zhihu.client_secret="test_zhihu_client_secret"
textup config --set platforms.zhihu.redirect_uri="http://localhost:8080/auth/zhihu/callback"

# 验证知乎配置
textup config --get platforms.zhihu
```

### 4.3 配置今日头条平台（测试用）
```bash
# 设置今日头条测试凭证
textup config --set platforms.toutiao.app_id="test_toutiao_app_id"
textup config --set platforms.toutiao.secret="test_toutiao_app_secret"
textup config --set platforms.toutiao.redirect_uri="http://localhost:8080/auth/toutiao/callback"

# 验证今日头条配置
textup config --get platforms.toutiao
```

### 4.4 查看认证状态
```bash
# 查看所有平台认证状态
textup auth --status
```

---

## 📝 第五部分：内容准备和测试

### 5.1 创建测试文章
```bash
# 创建测试目录
mkdir -p test-articles

# 创建测试文章文件
cat > test-articles/test-article.md << 'EOF'
# TextUp功能测试文章

## 简介

这是一篇用于测试TextUp多平台发布功能的文章。

## 主要功能

TextUp支持以下功能：

- **多平台发布**: 同时发布到微博、知乎、今日头条
- **格式转换**: 自动将Markdown转换为各平台支持的格式
- **认证管理**: OAuth2.0安全认证
- **错误处理**: 完善的错误处理和重试机制

## 测试内容

### 文本格式测试

这里包含**粗体文本**和*斜体文本*。

### 列表测试

1. 有序列表项目1
2. 有序列表项目2
3. 有序列表项目3

- 无序列表项目A
- 无序列表项目B
- 无序列表项目C

### 链接测试

访问[TextUp项目主页](https://github.com/textup-team/textup)了解更多信息。

### 代码测试

```python
# Python代码示例
def hello_textup():
    print("Hello, TextUp!")
    return "发布成功"
```

## 结论

如果您能看到这篇文章，说明TextUp的发布功能运行正常！

**测试标签**: #TextUp #多平台发布 #功能测试 #自动化
EOF

# 验证文件创建
ls -la test-articles/
cat test-articles/test-article.md
```

### 5.2 创建更多测试内容
```bash
# 创建短文章测试
cat > test-articles/short-article.md << 'EOF'
# 短文章测试

这是一篇短文章，用于测试TextUp对不同长度内容的处理能力。

简洁明了的内容同样重要！

#短文测试 #TextUp
EOF

# 创建长文章测试
cat > test-articles/long-article.md << 'EOF'
# 长文章测试：中国古代诗词赏析

## 引言

中国古代诗词是中华文化的瑰宝，承载着千年文明的智慧与情感。

## 唐诗代表作品

### 李白《静夜思》

床前明月光，疑是地上霜。
举头望明月，低头思故乡。

这首诗以简洁的语言描绘了游子的思乡之情。

### 杜甫《春望》

国破山河在，城春草木深。
感时花溅泪，恨别鸟惊心。
烽火连三月，家书抵万金。
白头搔更短，浑欲不胜簪。

杜甫通过描写春天的景象，表达了对国家命运的忧虑。

## 宋词精品

### 苏轼《水调歌头》

明月几时有？把酒问青天。
不知天上宫阙，今夕是何年。
我欲乘风归去，又恐琼楼玉宇，高处不胜寒。
起舞弄清影，何似在人间。

转朱阁，低绮户，照无眠。
不应有恨，何事长向别时圆？
人有悲欢离合，月有阴晴圆缺，此事古难全。
但愿人长久，千里共婵娟。

这首词表达了对人生哲理的思考和对美好生活的向往。

## 结语

古代诗词不仅是文学作品，更是文化传承的载体，值得我们细心品味和传承。

#古诗词 #中华文化 #文学赏析 #传统文化
EOF

# 验证所有测试文件
ls -la test-articles/
```

---

## 🔧 第六部分：功能测试

### 6.1 内容验证测试
```bash
# 验证测试文章格式
textup validate --file test-articles/test-article.md --platform weibo
textup validate --file test-articles/test-article.md --platform zhihu
textup validate --file test-articles/test-article.md --platform toutiao

# 预览内容转换效果
textup preview --file test-articles/test-article.md --platform weibo
textup preview --file test-articles/test-article.md --platform zhihu
```

### 6.2 模拟发布测试
```bash
# 预览模式发布（不实际发布）
textup publish --file test-articles/test-article.md --platform weibo --dry-run
textup publish --file test-articles/short-article.md --platform zhihu --dry-run
textup publish --file test-articles/long-article.md --platform toutiao --dry-run

# 多平台预览
textup publish --file test-articles/test-article.md --platforms weibo,zhihu,toutiao --dry-run
```

---

## 🧪 第七部分：运行自动化测试

### 7.1 运行项目测试套件
```bash
# 运行基础功能测试
uv run pytest tests/test_working_features.py -v

# 运行CLI测试
uv run pytest tests/test_cli_simple_focus.py -v

# 运行覆盖率测试
uv run pytest tests/test_phase3_coverage_breakthrough.py -v
```

### 7.2 运行今日头条专项测试
```bash
# 运行今日头条功能测试脚本
uv run python test-toutiao-publish.py

# 查看测试结果
ls -la test-results/
cat test-results/test-summary.md
```

### 7.3 生成测试覆盖率报告
```bash
# 生成完整覆盖率报告
uv run pytest --cov=src/textup --cov-report=html --cov-report=term-missing

# 查看覆盖率报告
open htmlcov/index.html  # macOS
# 或者直接查看终端输出
```

---

## 📊 第八部分：系统状态检查

### 8.1 检查配置完整性
```bash
# 显示完整配置
textup config --list

# 检查所有平台配置
textup config --get platforms

# 验证配置文件
cat ~/.textup/config/config.yaml
```

### 8.2 检查认证状态
```bash
# 查看详细认证状态
textup auth --status

# 查看认证配置
ls -la ~/.textup/config/
```

### 8.3 检查日志系统
```bash
# 查看日志目录
ls -la ~/.textup/logs/

# 查看最新日志
tail -20 ~/.textup/logs/textup.log

# 启用详细日志模式测试
textup --debug config --list
```

---

## 🎯 第九部分：端到端功能验证

### 9.1 完整发布流程测试（模拟模式）
```bash
# 交互式发布测试
echo "test-articles/test-article.md
weibo
y" | textup publish --interactive --dry-run
```

### 9.2 批量发布测试
```bash
# 批量发布目录中所有文章（预览模式）
textup publish --directory test-articles --platforms weibo,zhihu,toutiao --dry-run
```

### 9.3 错误处理测试
```bash
# 测试无效文件
textup publish --file nonexistent.md --platform weibo --dry-run

# 测试无效平台
textup publish --file test-articles/test-article.md --platform invalid_platform --dry-run

# 测试权限错误
textup auth --status --platform invalid_platform
```

---

## 📈 第十部分：性能和稳定性测试

### 10.1 压力测试
```bash
# 创建多个测试文件
for i in {1..5}; do
  cp test-articles/test-article.md test-articles/test-article-$i.md
done

# 批量处理测试
textup publish --pattern "test-articles/test-article-*.md" --platforms weibo,zhihu --dry-run
```

### 10.2 并发测试
```bash
# 设置高并发配置
textup config --set app.max_concurrent=5

# 测试并发处理
textup publish --directory test-articles --platforms weibo,zhihu,toutiao --dry-run
```

---

## 🔍 第十一部分：结果验证和报告

### 11.1 生成测试报告
```bash
# 创建测试结果目录
mkdir -p test-reports

# 运行完整测试并生成报告
uv run pytest --cov=src/textup --cov-report=html --html=test-reports/test-report.html --self-contained-html

# 复制覆盖率报告
cp -r htmlcov test-reports/coverage-report

# 创建测试总结
cat > test-reports/test-summary.txt << EOF
TextUp 本地测试完成报告
========================

测试时间: $(date)
测试环境: $(uname -a)
Python版本: $(python --version)

测试项目:
✅ 环境配置
✅ 应用安装
✅ 基础配置
✅ 平台认证配置
✅ 内容创建和验证
✅ 功能测试
✅ 自动化测试套件
✅ 系统状态检查
✅ 端到端流程验证
✅ 性能测试

测试文件:
$(ls -la test-articles/)

配置状态:
$(textup config --list)

认证状态:
$(textup auth --status)

测试结论: TextUp系统功能正常，可以投入使用！
EOF

# 显示测试报告
cat test-reports/test-summary.txt
```

### 11.2 清理测试环境（可选）
```bash
# 清理测试文件
rm -rf test-articles/
rm -f test-backup.yaml

# 重置配置（如果需要）
# textup config --reset

# 退出虚拟环境
deactivate
```

---

## ✅ 测试完成检查清单

完成以下所有步骤，在每一项后面打✅：

- [ ] **环境准备**: Python 3.9+, uv安装完成
- [ ] **应用安装**: TextUp安装成功，`textup --version`显示版本
- [ ] **基础配置**: 应用配置完成，`textup config --list`显示配置
- [ ] **平台配置**: 微博、知乎、今日头条凭证配置完成
- [ ] **内容创建**: 测试文章创建成功，3个测试文件存在
- [ ] **内容验证**: `textup validate`命令执行成功
- [ ] **预览功能**: `textup preview`显示转换后内容
- [ ] **模拟发布**: `--dry-run`模式发布测试成功
- [ ] **自动化测试**: pytest测试套件运行完成
- [ ] **今日头条测试**: 专项测试脚本执行成功
- [ ] **覆盖率报告**: 测试覆盖率报告生成成功
- [ ] **配置检查**: 所有配置项验证通过
- [ ] **认证状态**: 认证状态检查完成
- [ ] **日志系统**: 日志文件生成正常
- [ ] **错误处理**: 错误场景测试通过
- [ ] **性能测试**: 批量和并发测试完成
- [ ] **测试报告**: 完整测试报告生成

---

## 🎉 恭喜！测试完成

如果您完成了上述所有步骤，说明：

✅ **TextUp系统功能完整**  
✅ **本地环境配置正确**  
✅ **所有核心功能正常运行**  
✅ **系统已准备好投入使用**

### 下一步建议：
1. 获取真实的平台API凭证
2. 进行真实环境发布测试
3. 根据实际需求定制配置
4. 开始日常内容发布工作

### 技术支持：
- 查看详细文档：`docs/DEPLOYMENT_GUIDE.md`
- 问题反馈：GitHub Issues
- 更多示例：`docs/` 目录

**祝您使用愉快！** 🚀🎈