# TextUp 用户文档中心 📚

欢迎使用 TextUp - 多平台文本内容发布工具！本文档中心为不同需求的用户提供完整的使用指南。

## 🎯 快速导航

### 👋 新手用户
如果您是第一次使用 TextUp，建议按以下顺序阅读：

1. **[📘 快速上手指南](quick-start-guide.md)** - 10分钟完成安装和首次发布
2. **[🧪 本地测试指南](local-testing-guide.md)** - 完整的功能测试和验证
3. **[❓ 常见问题解答](faq.md)** - 快速解决常见问题

### 🔧 系统管理员
如果您需要部署和管理 TextUp 系统：

1. **[🚀 部署指南](deployment-guide.md)** - 生产环境部署和配置详解
2. **[🛠️ 故障排除指南](troubleshooting-guide.md)** - 系统问题诊断和解决
3. **[📊 最佳实践](best-practices.md)** - 使用建议和优化技巧

### 👨‍💻 开发者
如果您想了解技术细节或参与开发：

1. **[📖 API 参考文档](../api/)** - 完整的API接口文档
2. **[🧪 测试文档](../testing/)** - 测试指南和报告
3. **[🏗️ 项目架构](../project-structure.md)** - 项目结构和设计说明

## 📋 文档概览

### 核心使用文档

| 文档 | 适用人群 | 预计用时 | 描述 |
|------|----------|----------|------|
| [快速上手指南](quick-start-guide.md) | 所有用户 | 10分钟 | 从安装到首次发布的完整流程 |
| [本地测试指南](local-testing-guide.md) | 技术用户 | 30-45分钟 | 完整的本地安装、配置和功能测试 |
| [部署指南](deployment-guide.md) | 系统管理员 | 1-2小时 | 生产环境部署、配置和维护 |

### 辅助文档

| 文档 | 适用场景 | 描述 |
|------|----------|------|
| [常见问题解答](faq.md) | 遇到问题时 | 收集用户常见问题和解决方案 |
| [故障排除指南](troubleshooting-guide.md) | 系统异常时 | 详细的问题诊断和修复步骤 |
| [最佳实践](best-practices.md) | 优化使用体验 | 使用技巧和性能优化建议 |
| [使用案例](examples.md) | 学习参考 | 实际使用场景和配置示例 |

## 🚀 快速开始

### 第一次使用？
```bash
# 1. 进入项目目录
cd textup

# 2. 运行一键启动脚本
./start-textup.sh

# 3. 按照提示完成配置
```

### 已经安装？
```bash
# 查看版本
textup --version

# 查看帮助
textup --help

# 快速发布测试
textup publish --file my-article.md --platform weibo --dry-run
```

## 🎯 使用场景导航

### 场景1：内容创作者
**目标**：将文章一键发布到多个平台
- 📖 阅读：[快速上手指南](quick-start-guide.md)
- 🎯 重点：平台认证配置、内容发布流程
- 💡 技巧：使用批量发布和定时发布功能

### 场景2：企业营销团队
**目标**：批量管理和发布营销内容
- 📖 阅读：[部署指南](deployment-guide.md) + [最佳实践](best-practices.md)
- 🎯 重点：生产环境部署、团队协作配置
- 💡 技巧：使用配置模板和批量操作

### 场景3：技术团队
**目标**：集成到现有系统或二次开发
- 📖 阅读：[API 参考文档](../api/) + [项目架构](../project-structure.md)
- 🎯 重点：API接口、扩展开发、系统集成
- 💡 技巧：使用Python API和自定义适配器

### 场景4：系统运维
**目标**：部署、监控和维护系统
- 📖 阅读：[部署指南](deployment-guide.md) + [故障排除指南](troubleshooting-guide.md)
- 🎯 重点：Docker部署、监控配置、日志管理
- 💡 技巧：使用系统服务和监控工具

## 🔗 相关资源

### 项目信息
- **项目主页**：[README.md](../../README.md)
- **更新日志**：[changelog](../changelog/)
- **项目状态**：[实施文档](../implementation/)

### 技术支持
- **问题反馈**：GitHub Issues
- **功能建议**：GitHub Discussions
- **技术交流**：查看 [AI 工作流文档](../ai/)

### 开发资源
- **API 文档**：[docs/api/](../api/)
- **测试指南**：[docs/testing/](../testing/)
- **产品规范**：[docs/product/](../product/)

## 📞 获取帮助

### 快速帮助
```bash
# 查看命令帮助
textup --help
textup config --help
textup publish --help

# 启用调试模式
textup --debug <command>

# 查看日志
tail -f ~/.textup/logs/textup.log
```

### 文档反馈
如果您发现文档有任何问题或建议改进，请：
1. 提交 GitHub Issue
2. 发起 Pull Request
3. 联系项目维护团队

---

## 📝 文档维护信息

**最后更新**：2025-09-04
**文档版本**：v2.0
**维护团队**：TextUp 开发团队
**更新频率**：随项目版本同步更新

---

**开始您的 TextUp 之旅吧！** 🚀✨
