# TextUp - 多平台文本内容发布工具

[![Python Version](https://img.shields.io/badge/python-3.9+-blue.svg)](https://python.org)
[![Poetry](https://img.shields.io/badge/dependency%20manager-poetry-blue.svg)](https://python-poetry.org/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Code Style](https://img.shields.io/badge/code%20style-black-black.svg)](https://github.com/psf/black)

## 📝 项目简介

TextUp 是一个企业级的多平台文本内容自动化发布工具，旨在帮助内容创作者、营销团队和自媒体从业者实现文本内容在多个主流媒体平台的一键批量发布。

### 🌟 核心特性

- **🚀 多平台发布**: 支持知乎、微博、今日头条等主流平台
- **⚡ 异步高效**: 基于 asyncio 的并发发布，显著提升效率
- **🎯 智能调度**: 基于平台特性的最优发布时间算法
- **🔒 安全可靠**: 加密存储认证凭证，完整的错误处理机制
- **🛠️ 易于使用**: 友好的 CLI 界面，简单的配置管理
- **🔄 格式转换**: 自动适配不同平台的内容格式要求
- **🧪 测试完备**: 48%+ 测试覆盖率，企业级质量保证
- **📚 文档齐全**: 详细的部署指南和使用手册

## 🚀 一键启动

TextUp 提供了一键启动脚本，让您快速开始使用：

```bash
# 进入项目目录
cd textup

# 运行一键启动脚本
./start-textup.sh
```

启动脚本将自动：
- ✅ 检查Python环境（需要3.9+）
- ✅ 安装uv包管理器
- ✅ 创建虚拟环境
- ✅ 安装所有依赖
- ✅ 初始化配置
- ✅ 提供图形化操作菜单

## ⚡ 快速上手

### 10分钟快速体验

1. **启动应用**
   ```bash
   ./start-textup.sh
   ```

2. **配置平台认证**
   - 选择菜单项 "3. 平台认证"
   - 按照提示配置微博/知乎/今日头条

3. **发布第一篇文章**
   - 准备Markdown文章
   - 选择菜单项 "4. 发布文章"
   - 选择目标平台，开始发布！

## 📚 完整文档

TextUp 提供了详细的文档和指南，适合不同需求的用户：

### 📖 核心文档

- **[📘 快速上手指南](docs/quick-start-guide.md)** - 10分钟完成安装和首次发布
- **[🧪 本地测试指南](docs/local-testing-guide.md)** - 完整的本地测试和验证流程
- **[🚀 部署指南](docs/deployment-guide.md)** - 生产环境部署和配置详解
- **[📊 最终交付报告](docs/delivery-report.md)** - 项目交付状态和技术指标

### 🛠️ 技术文档

- **[🤖 AI工作流](docs/ai-automated-workflow.md)** - AI自动化开发工作流程
- **[📋 API参考](docs/api/)** - 完整的API接口文档
- **[🔧 开发指南](docs/deployment-guide.md#开发和贡献)** - 开发环境设置和贡献指南

### 🎯 使用场景

- **新手用户** → 推荐从 [快速上手指南](docs/quick-start-guide.md) 开始
- **本地测试** → 使用 [本地测试指南](docs/local-testing-guide.md) 验证功能
- **生产部署** → 参考 [部署指南](docs/deployment-guide.md) 进行配置
- **开发贡献** → 查看开发指南了解项目架构

### 🎯 支持平台

| 平台 | 集成方式 | 状态 | 特性 |
|------|----------|------|------|
| 知乎 | Playwright自动化 | ✅ 已支持 | 文章发布、专栏管理 |
| 微博 | API + Cookie | ✅ 已支持 | 微博发布、图片上传 |
| 今日头条 | API + OAuth | ✅ 已支持 | 文章发布、分类管理 |
| 小红书 | Web自动化 | 🚧 开发中 | 笔记发布、图片支持 |

## 🚀 快速开始

### 📋 环境要求

- Python 3.9+
- Poetry (推荐) 或 pip
- Git

### 📦 安装

#### 使用 Poetry (推荐)

```bash
# 克隆项目
git clone https://github.com/textup/textup.git
cd textup

# 安装依赖
poetry install

# 激活虚拟环境
poetry shell
```

#### 使用 pip

```bash
# 克隆项目
git clone https://github.com/textup/textup.git
cd textup

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

#### 通过 PyPI 安装 (即将支持)

```bash
pip install textup
```

### ⚙️ 初始化配置

```bash
# 初始化项目配置
textup init

# 配置平台认证
textup auth login zhihu
textup auth login weibo
```

### 📝 基础使用

```bash
# 发布单个文件
textup publish ./article.md --platforms zhihu,weibo

# 批量发布
textup publish ./content/ --recursive

# 定时发布
textup publish ./article.md --schedule "2024-01-01 10:00:00"

# 查看发布状态
textup status

# 查看发布历史
textup history
```

## 📖 详细文档

- [🔧 配置指南](docs/configuration.md)
- [🚀 使用教程](docs/tutorial.md)
- [🔌 平台集成](docs/platforms.md)
- [🛠️ 开发指南](docs/development.md)
- [❓ 常见问题](docs/faq.md)

## 🏗️ 项目结构

```
textup/
├── src/textup/              # 主要源代码
│   ├── models/              # 数据模型
│   ├── adapters/            # 平台适配器
│   ├── services/            # 业务服务
│   ├── cli/                 # 命令行接口
│   ├── config/              # 配置管理
│   └── utils/               # 工具函数
├── tests/                   # 测试代码
│   ├── unit/                # 单元测试
│   └── integration/         # 集成测试
├── docs/                    # 项目文档
├── config/                  # 配置文件模板
└── examples/                # 示例文件
```

## 🔧 开发环境

### 安装开发依赖

```bash
# 使用 Poetry
poetry install --with dev

# 使用 pip
pip install -r requirements-dev.txt
```

### 代码质量工具

```bash
# 代码格式化
black src tests

# 代码检查
flake8 src tests

# 导入排序
isort src tests

# 类型检查
mypy src

# 运行所有检查
pre-commit run --all-files
```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/unit/test_models.py

# 测试覆盖率
pytest --cov=textup --cov-report=html
```

## 🤝 贡献指南

我们欢迎所有形式的贡献！请参阅 [贡献指南](CONTRIBUTING.md) 了解详情。

### 开发流程

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Pydantic](https://pydantic.dev/) - 数据验证库
- [Click](https://click.palletsprojects.com/) - CLI框架
- [Playwright](https://playwright.dev/) - Web自动化
- [Rich](https://rich.readthedocs.io/) - 终端美化
- [SQLAlchemy](https://sqlalchemy.org/) - ORM框架

## 📞 支持与反馈

- 🐛 [报告问题](https://github.com/textup/textup/issues)
- 💡 [功能建议](https://github.com/textup/textup/discussions)
- 📧 [联系我们](mailto:<EMAIL>)
- 📖 [官方文档](https://textup.dev/docs)

---

**TextUp** - 让内容发布变得简单高效 🚀