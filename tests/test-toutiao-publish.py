#!/usr/bin/env python3
"""
今日头条发布功能完整测试脚本
Comprehensive Test Script for Toutiao Publishing Workflow

测试流程：
1. 加载历史故事内容
2. 配置今日头条适配器
3. 模拟OAuth认证流程
4. 内容格式转换和验证
5. 执行发布操作
6. 验证发布结果
7. 生成测试报告

作者: TextUp Development Team
创建时间: 2024-12-19
"""

import asyncio
import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目路径到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

try:
    from textup.adapters.toutiao import ToutiaoAdapter
    from textup.models import (
        Content, TransformedContent, PublishResult, AuthResult,
        Platform, ContentFormat, ToutiaoCredentials
    )
    from textup.services.config_manager import ConfigManager
    from textup.services.content_manager import ContentManager
    from textup.utils.exceptions import (
        PlatformAPIError, AuthenticationError, ContentValidationError
    )
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保在正确的环境中运行脚本")
    sys.exit(1)


class ToutiaoPublishTest:
    """今日头条发布测试类"""

    def __init__(self):
        self.test_results = {
            "start_time": datetime.now().isoformat(),
            "end_time": None,
            "duration": 0,
            "tests": [],
            "success_count": 0,
            "failure_count": 0,
            "summary": {}
        }
        self.adapter = None
        self.content_manager = None

    def log_test(self, test_name: str, status: str, message: str, details: Optional[Dict] = None):
        """记录测试结果"""
        test_result = {
            "test_name": test_name,
            "status": status,  # "PASS", "FAIL", "SKIP"
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "details": details or {}
        }
        self.test_results["tests"].append(test_result)

        if status == "PASS":
            self.test_results["success_count"] += 1
            print(f"✅ {test_name}: {message}")
        elif status == "FAIL":
            self.test_results["failure_count"] += 1
            print(f"❌ {test_name}: {message}")
        else:
            print(f"⏭️  {test_name}: {message}")

    async def test_environment_setup(self):
        """测试环境设置"""
        print("\n🔧 第1步: 环境设置测试")

        try:
            # 检查测试文件是否存在
            story_file = project_root / "test-story.md"
            if story_file.exists():
                self.log_test("story_file_check", "PASS", f"测试故事文件存在: {story_file}")
            else:
                self.log_test("story_file_check", "FAIL", f"测试故事文件不存在: {story_file}")
                return False

            # 检查配置文件
            config_file = project_root / "test-config.yaml"
            if config_file.exists():
                self.log_test("config_file_check", "PASS", f"配置文件存在: {config_file}")
            else:
                self.log_test("config_file_check", "FAIL", f"配置文件不存在: {config_file}")

            # 创建测试目录
            test_dirs = ["test-data", "logs", "test-results"]
            for dir_name in test_dirs:
                dir_path = project_root / dir_name
                dir_path.mkdir(exist_ok=True)
                self.log_test(f"create_dir_{dir_name}", "PASS", f"创建测试目录: {dir_path}")

            return True

        except Exception as e:
            self.log_test("environment_setup", "FAIL", f"环境设置失败: {str(e)}")
            return False

    async def test_adapter_initialization(self):
        """测试适配器初始化"""
        print("\n⚙️ 第2步: 今日头条适配器初始化测试")

        try:
            # 初始化适配器
            self.adapter = ToutiaoAdapter()
            self.log_test("adapter_init", "PASS", "今日头条适配器初始化成功")

            # 测试适配器属性
            assert self.adapter.platform == Platform.TOUTIAO.value
            self.log_test("adapter_platform", "PASS", f"平台标识正确: {self.adapter.platform}")

            assert self.adapter.base_url == "https://developer.toutiao.com/api"
            self.log_test("adapter_base_url", "PASS", f"API基础URL正确: {self.adapter.base_url}")

            assert self.adapter.oauth_base_url == "https://developer.toutiao.com/oauth"
            self.log_test("adapter_oauth_url", "PASS", f"OAuth URL正确: {self.adapter.oauth_base_url}")

            # 测试必需凭据字段
            required_creds = self.adapter.required_credentials
            expected_creds = ["app_id", "secret", "redirect_uri"]
            assert set(required_creds) == set(expected_creds)
            self.log_test("adapter_credentials", "PASS", f"必需凭据字段正确: {required_creds}")

            return True

        except Exception as e:
            self.log_test("adapter_initialization", "FAIL", f"适配器初始化失败: {str(e)}")
            return False

    async def test_content_loading(self):
        """测试内容加载"""
        print("\n📖 第3步: 内容加载测试")

        try:
            # 初始化内容管理器
            self.content_manager = ContentManager()
            self.log_test("content_manager_init", "PASS", "内容管理器初始化成功")

            # 读取测试故事文件
            story_file = project_root / "test-story.md"
            content_text = story_file.read_text(encoding='utf-8')

            # 解析标题和内容
            lines = content_text.split('\n')
            title = lines[0].replace('# ', '') if lines[0].startswith('# ') else "唐朝茶圣的秘密"

            # 创建Content对象
            self.test_content = Content(
                title=title,
                content=content_text,
                format=ContentFormat.MARKDOWN
            )

            self.log_test("content_loading", "PASS", f"成功加载故事内容: 标题='{title}', 长度={len(content_text)}字符")

            return True

        except Exception as e:
            self.log_test("content_loading", "FAIL", f"内容加载失败: {str(e)}")
            return False

    async def test_content_transformation(self):
        """测试内容转换"""
        print("\n🔄 第4步: 内容格式转换测试")

        try:
            # 转换Markdown到HTML
            transformed_content = await self.adapter.transform_content(self.test_content)

            # 验证转换结果
            assert isinstance(transformed_content, TransformedContent)
            assert transformed_content.title == self.test_content.title
            assert transformed_content.format == ContentFormat.HTML
            assert len(transformed_content.content) > 0

            self.log_test("content_transformation", "PASS",
                         f"内容转换成功: {ContentFormat.MARKDOWN} -> {ContentFormat.HTML}")

            # 保存转换后的内容用于检查
            html_file = project_root / "test-results" / "transformed-content.html"
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(f"<h1>{transformed_content.title}</h1>\n")
                f.write(transformed_content.content)

            self.log_test("save_transformed_content", "PASS", f"保存转换后内容到: {html_file}")

            self.transformed_content = transformed_content
            return True

        except Exception as e:
            self.log_test("content_transformation", "FAIL", f"内容转换失败: {str(e)}")
            return False

    async def test_content_validation(self):
        """测试内容验证"""
        print("\n✅ 第5步: 内容验证测试")

        try:
            # 验证转换后的内容
            validation_result = self.adapter._validate_format_impl(self.transformed_content)

            if validation_result.is_valid:
                self.log_test("content_validation", "PASS", "内容格式验证通过")
            else:
                error_messages = [error.message for error in validation_result.errors]
                self.log_test("content_validation", "FAIL", f"内容验证失败: {', '.join(error_messages)}")
                return False

            # 测试各种边界情况
            test_cases = [
                # (title, content, expected_valid, test_name)
                ("", "正常内容" * 50, False, "empty_title"),
                ("正常标题", "", False, "empty_content"),
                ("超长标题" * 20, "正常内容" * 50, False, "title_too_long"),
                ("正常标题", "短内容", False, "content_too_short"),
                ("正常标题", "正常内容" * 50, True, "valid_content"),
            ]

            for title, content, expected_valid, test_name in test_cases:
                test_content = TransformedContent(title, content, ContentFormat.HTML)
                result = self.adapter._validate_format_impl(test_content)

                if result.is_valid == expected_valid:
                    self.log_test(f"validation_{test_name}", "PASS", f"验证测试通过: {test_name}")
                else:
                    self.log_test(f"validation_{test_name}", "FAIL", f"验证测试失败: {test_name}")

            return True

        except Exception as e:
            self.log_test("content_validation", "FAIL", f"内容验证失败: {str(e)}")
            return False

    async def test_credentials_validation(self):
        """测试凭据验证"""
        print("\n🔐 第6步: 凭据验证测试")

        try:
            # 测试有效凭据
            valid_credentials = {
                "app_id": "1234567890",
                "secret": "abcdef1234567890abcdef1234567890abcd",
                "redirect_uri": "https://example.com/callback"
            }

            result = self.adapter._validate_credentials(valid_credentials)
            if result.is_valid:
                self.log_test("valid_credentials", "PASS", "有效凭据验证通过")
            else:
                self.log_test("valid_credentials", "FAIL", f"有效凭据验证失败: {result.errors}")

            # 测试无效凭据
            invalid_test_cases = [
                ({}, "empty_credentials"),
                ({"app_id": ""}, "empty_app_id"),
                ({"app_id": "abc", "secret": "short", "redirect_uri": "invalid"}, "invalid_format"),
                ({"app_id": "123", "secret": "toolong" * 10, "redirect_uri": "http://valid.com"}, "invalid_app_id"),
            ]

            for invalid_creds, test_name in invalid_test_cases:
                result = self.adapter._validate_credentials(invalid_creds)
                if not result.is_valid:
                    self.log_test(f"invalid_credentials_{test_name}", "PASS", f"无效凭据正确被拒绝: {test_name}")
                else:
                    self.log_test(f"invalid_credentials_{test_name}", "FAIL", f"无效凭据未被拒绝: {test_name}")

            return True

        except Exception as e:
            self.log_test("credentials_validation", "FAIL", f"凭据验证测试失败: {str(e)}")
            return False

    async def test_auth_url_generation(self):
        """测试认证URL生成"""
        print("\n🔗 第7步: OAuth认证URL生成测试")

        try:
            test_credentials = {
                "app_id": "test_app_id_123",
                "secret": "test_secret_456",
                "redirect_uri": "https://example.com/callback"
            }

            # 生成认证URL
            auth_url = self.adapter.generate_auth_url(test_credentials, state="test_state_789")

            # 验证URL格式
            assert auth_url.startswith("https://developer.toutiao.com/oauth/authorize/")
            assert "client_id=test_app_id_123" in auth_url
            assert "redirect_uri=https%3A//example.com/callback" in auth_url
            assert "state=test_state_789" in auth_url
            assert "response_type=code" in auth_url
            assert "scope=article.publish" in auth_url

            self.log_test("auth_url_generation", "PASS", f"认证URL生成成功: {auth_url}")

            return True

        except Exception as e:
            self.log_test("auth_url_generation", "FAIL", f"认证URL生成失败: {str(e)}")
            return False

    async def test_mock_authentication(self):
        """测试模拟认证流程"""
        print("\n🎭 第8步: 模拟认证流程测试")

        try:
            # 模拟认证凭据
            mock_credentials = {
                "app_id": "mock_app_id",
                "secret": "mock_secret_12345678901234567890123456",
                "redirect_uri": "https://localhost:8080/callback",
                "access_token": "mock_access_token_123456",
                "expires_at": datetime.now().isoformat()
            }

            # 测试认证
            auth_result = await self.adapter._authenticate_impl(mock_credentials)

            if hasattr(auth_result, 'success'):
                if auth_result.success:
                    self.log_test("mock_authentication", "PASS", "模拟认证成功")
                else:
                    self.log_test("mock_authentication", "PASS", f"模拟认证按预期失败: {auth_result.error_message}")
            else:
                self.log_test("mock_authentication", "FAIL", "认证结果格式不正确")

            return True

        except Exception as e:
            self.log_test("mock_authentication", "FAIL", f"模拟认证失败: {str(e)}")
            return False

    async def test_mock_publish(self):
        """测试模拟发布"""
        print("\n🚀 第9步: 模拟发布流程测试")

        try:
            # 设置模拟访问令牌
            self.adapter._current_access_token = "mock_access_token_for_publish"

            # 注意：这里会因为网络请求失败而抛出异常，这是正常的
            # 我们主要测试发布流程的逻辑
            try:
                result = await self.adapter._publish_impl(self.transformed_content)
                self.log_test("mock_publish_unexpected_success", "FAIL",
                             "模拟发布意外成功（应该因为网络失败）")
            except PlatformAPIError as e:
                if "网络请求失败" in str(e) or "Network" in str(e):
                    self.log_test("mock_publish_network_error", "PASS",
                                 f"模拟发布按预期网络失败: {str(e)}")
                else:
                    self.log_test("mock_publish_other_error", "PASS",
                                 f"模拟发布遇到其他错误: {str(e)}")
            except AuthenticationError as e:
                self.log_test("mock_publish_auth_error", "PASS",
                             f"模拟发布认证错误: {str(e)}")
            except Exception as e:
                self.log_test("mock_publish_exception", "PASS",
                             f"模拟发布抛出异常（预期行为）: {str(e)}")

            return True

        except Exception as e:
            self.log_test("mock_publish", "FAIL", f"模拟发布测试失败: {str(e)}")
            return False

    async def test_error_handling(self):
        """测试错误处理"""
        print("\n🛠️ 第10步: 错误处理测试")

        try:
            # 测试各种错误场景的处理
            error_responses = [
                ({"err_no": 40001, "err_msg": "访问令牌无效"}, "invalid_token"),
                ({"err_no": 40003, "err_msg": "访问令牌已过期"}, "expired_token"),
                ({"err_no": 40006, "err_msg": "API调用频率超限"}, "rate_limit"),
                ({"err_no": 40007, "err_msg": "内容审核失败"}, "content_review"),
                ({"err_no": 40011, "err_msg": "内容包含敏感信息"}, "sensitive_content"),
            ]

            for error_response, test_name in error_responses:
                try:
                    await self.adapter._handle_api_error(error_response)
                    self.log_test(f"error_handling_{test_name}", "FAIL", f"错误未被正确处理: {test_name}")
                except Exception as e:
                    expected_errors = [
                        "InvalidCredentialsError", "AuthenticationError",
                        "RateLimitError", "ContentValidationError", "PlatformAPIError"
                    ]
                    error_type = type(e).__name__
                    if error_type in expected_errors:
                        self.log_test(f"error_handling_{test_name}", "PASS",
                                     f"错误正确处理: {error_type} - {str(e)}")
                    else:
                        self.log_test(f"error_handling_{test_name}", "FAIL",
                                     f"错误处理类型不正确: {error_type}")

            return True

        except Exception as e:
            self.log_test("error_handling", "FAIL", f"错误处理测试失败: {str(e)}")
            return False

    async def generate_test_report(self):
        """生成测试报告"""
        print("\n📊 生成测试报告...")

        end_time = datetime.now()
        start_time = datetime.fromisoformat(self.test_results["start_time"])
        duration = (end_time - start_time).total_seconds()

        self.test_results.update({
            "end_time": end_time.isoformat(),
            "duration": duration,
            "summary": {
                "total_tests": len(self.test_results["tests"]),
                "passed": self.test_results["success_count"],
                "failed": self.test_results["failure_count"],
                "pass_rate": f"{(self.test_results['success_count'] / len(self.test_results['tests']) * 100):.1f}%"
            }
        })

        # 保存详细报告
        report_file = project_root / "test-results" / "toutiao-test-report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2)

        # 生成简要报告
        summary_file = project_root / "test-results" / "test-summary.md"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("# 今日头条发布功能测试报告\n\n")
            f.write(f"**测试时间**: {self.test_results['start_time']} - {self.test_results['end_time']}\n")
            f.write(f"**测试耗时**: {duration:.2f}秒\n")
            f.write(f"**测试结果**: {self.test_results['summary']['pass_rate']} 通过率\n\n")
            f.write(f"## 测试统计\n\n")
            f.write(f"- 总测试数: {self.test_results['summary']['total_tests']}\n")
            f.write(f"- 通过测试: {self.test_results['summary']['passed']}\n")
            f.write(f"- 失败测试: {self.test_results['summary']['failed']}\n\n")
            f.write("## 详细结果\n\n")

            for test in self.test_results["tests"]:
                status_emoji = "✅" if test["status"] == "PASS" else "❌" if test["status"] == "FAIL" else "⏭️"
                f.write(f"{status_emoji} **{test['test_name']}**: {test['message']}\n")

        print(f"📊 测试报告已保存:")
        print(f"   详细报告: {report_file}")
        print(f"   简要报告: {summary_file}")

    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始今日头条发布功能完整测试\n")
        print("=" * 80)

        test_methods = [
            self.test_environment_setup,
            self.test_adapter_initialization,
            self.test_content_loading,
            self.test_content_transformation,
            self.test_content_validation,
            self.test_credentials_validation,
            self.test_auth_url_generation,
            self.test_mock_authentication,
            self.test_mock_publish,
            self.test_error_handling,
        ]

        for test_method in test_methods:
            try:
                success = await test_method()
                if not success and test_method.__name__ in ["test_environment_setup", "test_adapter_initialization"]:
                    print(f"\n💥 关键测试失败，停止后续测试: {test_method.__name__}")
                    break
            except Exception as e:
                print(f"\n💥 测试方法异常: {test_method.__name__} - {str(e)}")
                self.log_test(test_method.__name__, "FAIL", f"测试方法异常: {str(e)}")

        await self.generate_test_report()

        print("\n" + "=" * 80)
        print("🏁 测试完成!")
        print(f"📈 总结: {self.test_results['summary']['passed']}/{self.test_results['summary']['total_tests']} 通过 ({self.test_results['summary']['pass_rate']})")


async def main():
    """主函数"""
    print("🎯 TextUp - 今日头条发布功能端到端测试")
    print("📖 测试场景: 发布《唐朝茶圣的秘密》历史故事")
    print()

    # 运行测试
    test_runner = ToutiaoPublishTest()
    await test_runner.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
