"""
Targeted CLI tests to boost coverage significantly

This module contains comprehensive tests specifically designed to increase
CLI coverage by testing command line interfaces, argument parsing, and
CLI-specific logic paths.
"""

import pytest
import tempfile
import asyncio
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock, mock_open
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
import yaml
import json

from textup.cli.main import (
    app,
    console,
    get_config_manager,
    get_content_manager,
    parse_config_value,
)
from textup.models import Platform, Content, ContentFormat, PublishResult, AuthResult
from textup.utils.exceptions import ConfigurationError, PublishError, NetworkError


class TestCLIMainCommands:
    """Test main CLI commands with various parameters"""

    @pytest.fixture
    def runner(self):
        """CLI test runner"""
        return CliRunner()

    def test_main_app_help(self, runner):
        """Test main app help command"""
        result = runner.invoke(app, ["--help"])
        assert result.exit_code == 0
        assert "textup" in result.output.lower()

    def test_main_app_version(self, runner):
        """Test version flag"""
        result = runner.invoke(app, ["--version"])
        # Version command might exit with 0 or 1
        assert result.exit_code in [0, 1]

    def test_main_app_with_debug(self, runner):
        """Test main app with debug flag"""
        result = runner.invoke(app, ["--debug", "--help"])
        assert result.exit_code == 0

    def test_main_app_with_config_dir(self, runner):
        """Test main app with custom config directory"""
        result = runner.invoke(app, ["--config-dir", "/tmp", "--help"])
        assert result.exit_code == 0

    @patch("textup.cli.main.get_config_manager")
    def test_config_list_all(self, mock_get_config_manager, runner):
        """Test config --list command"""
        mock_config_manager = Mock()
        mock_config_manager.load_config = AsyncMock(
            return_value={
                "platforms": {"weibo": {"enabled": True}},
                "general": {"debug": False, "timeout": 30},
            }
        )
        mock_get_config_manager.return_value = mock_config_manager

        result = runner.invoke(app, ["config", "--list"])
        assert result.exit_code == 0
        mock_config_manager.load_config.assert_called_once()

    @patch("textup.cli.main.get_config_manager")
    def test_config_get_value(self, mock_get_config_manager, runner):
        """Test config --get command"""
        mock_config_manager = Mock()
        mock_config_manager.get_config_value = AsyncMock(return_value="test_value")
        mock_get_config_manager.return_value = mock_config_manager

        result = runner.invoke(app, ["config", "--get", "platforms.weibo.enabled"])
        assert result.exit_code == 0
        mock_config_manager.get_config_value.assert_called_once_with("platforms.weibo.enabled")

    @patch("textup.cli.main.get_config_manager")
    def test_config_set_value(self, mock_get_config_manager, runner):
        """Test config --set command"""
        mock_config_manager = Mock()
        mock_config_manager.set_config_value = AsyncMock(return_value=True)
        mock_get_config_manager.return_value = mock_config_manager

        result = runner.invoke(
            app, ["config", "--set", "platforms.weibo.enabled", "--value", "true"]
        )
        assert result.exit_code == 0
        mock_config_manager.set_config_value.assert_called_once_with(
            "platforms.weibo.enabled", True
        )

    @patch("textup.cli.main.get_config_manager")
    def test_config_set_value_failed(self, mock_get_config_manager, runner):
        """Test config --set command failure"""
        mock_config_manager = Mock()
        mock_config_manager.set_config_value = AsyncMock(return_value=False)
        mock_get_config_manager.return_value = mock_config_manager

        result = runner.invoke(app, ["config", "--set", "test.key", "--value", "test"])
        assert result.exit_code == 0
        assert "失败" in result.output

    @patch("textup.cli.main.get_config_manager")
    def test_config_backup(self, mock_get_config_manager, runner):
        """Test config --backup command"""
        mock_config_manager = Mock()
        mock_config_manager.backup_config = AsyncMock(return_value=True)
        mock_get_config_manager.return_value = mock_config_manager

        result = runner.invoke(app, ["config", "--backup"])
        assert result.exit_code == 0
        mock_config_manager.backup_config.assert_called_once()

    @patch("textup.cli.main.get_config_manager")
    def test_config_backup_failed(self, mock_get_config_manager, runner):
        """Test config --backup command failure"""
        mock_config_manager = Mock()
        mock_config_manager.backup_config = AsyncMock(return_value=False)
        mock_get_config_manager.return_value = mock_config_manager

        result = runner.invoke(app, ["config", "--backup"])
        assert result.exit_code == 0
        assert "失败" in result.output

    @patch("textup.cli.main.get_config_manager")
    def test_auth_list_platforms(self, mock_get_config_manager, runner):
        """Test auth --list command"""
        from textup.models import Platform

        mock_platform_config = Mock()
        mock_platform_config.is_active = True
        mock_platform_config.user_id = "test_user"

        mock_config_manager = Mock()
        mock_config_manager.get_all_platform_configs = AsyncMock(
            return_value={Platform.WEIBO: mock_platform_config}
        )
        mock_get_config_manager.return_value = mock_config_manager

        result = runner.invoke(app, ["auth", "--list"])
        assert result.exit_code == 0
        mock_config_manager.get_all_platform_configs.assert_called_once()

    @patch("textup.cli.main.get_config_manager")
    def test_auth_list_empty(self, mock_get_config_manager, runner):
        """Test auth --list with no configured platforms"""
        mock_config_manager = Mock()
        mock_config_manager.get_all_platform_configs = AsyncMock(return_value={})
        mock_get_config_manager.return_value = mock_config_manager

        result = runner.invoke(app, ["auth", "--list"])
        assert result.exit_code == 0
        assert "尚未配置" in result.output

    @patch("textup.cli.main.get_content_manager")
    def test_publish_single_file(self, mock_get_content_manager, runner):
        """Test publish command with single file"""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".md", delete=False) as temp_file:
            temp_file.write("# Test Content\n\nThis is test content.")
            temp_file_path = temp_file.name

        mock_content_manager = Mock()
        mock_content_manager.process_content_file = AsyncMock(return_value=Mock())
        mock_get_content_manager.return_value = mock_content_manager

        result = runner.invoke(app, ["publish", temp_file_path])
        # May exit with 0 or 1 depending on implementation
        assert result.exit_code in [0, 1]

        # Cleanup
        Path(temp_file_path).unlink(missing_ok=True)

    @patch("textup.cli.main.get_content_manager")
    def test_publish_with_platform(self, mock_get_content_manager, runner):
        """Test publish command with specific platform"""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".md", delete=False) as temp_file:
            temp_file.write("# Test Content")
            temp_file_path = temp_file.name

        mock_content_manager = Mock()
        mock_content_manager.process_content_file = AsyncMock(return_value=Mock())
        mock_get_content_manager.return_value = mock_content_manager

        result = runner.invoke(app, ["publish", temp_file_path, "--platform", "weibo"])
        assert result.exit_code in [0, 1]

        Path(temp_file_path).unlink(missing_ok=True)

    @patch("textup.cli.main.get_content_manager")
    def test_publish_dry_run(self, mock_get_content_manager, runner):
        """Test publish command with --dry-run"""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".md", delete=False) as temp_file:
            temp_file.write("# Test Content")
            temp_file_path = temp_file.name

        mock_content_manager = Mock()
        mock_content_manager.process_content_file = AsyncMock(return_value=Mock())
        mock_get_content_manager.return_value = mock_content_manager

        result = runner.invoke(app, ["publish", temp_file_path, "--dry-run"])
        assert result.exit_code in [0, 1]

        Path(temp_file_path).unlink(missing_ok=True)

    @patch("textup.cli.main.get_content_manager")
    def test_publish_recursive(self, mock_get_content_manager, runner):
        """Test publish command with --recursive"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test file in directory
            test_file = Path(temp_dir) / "test.md"
            test_file.write_text("# Test Content")

            mock_content_manager = Mock()
            mock_content_manager.process_content_file = AsyncMock(return_value=Mock())
            mock_get_content_manager.return_value = mock_content_manager

            result = runner.invoke(app, ["publish", temp_dir, "--recursive"])
            assert result.exit_code in [0, 1]


class TestCLIErrorHandling:
    """Test CLI error handling paths"""

    @pytest.fixture
    def runner(self):
        """CLI test runner"""
        return CliRunner()

    @patch("textup.cli.main.get_config_manager")
    def test_config_command_exception(self, mock_get_config_manager, runner):
        """Test config command with exception"""
        mock_config_manager = Mock()
        mock_config_manager.load_config = AsyncMock(side_effect=Exception("Test error"))
        mock_get_config_manager.return_value = mock_config_manager

        result = runner.invoke(app, ["config", "--list"])
        assert result.exit_code == 1
        assert "失败" in result.output

    @patch("textup.cli.main.get_config_manager")
    def test_auth_command_exception(self, mock_get_config_manager, runner):
        """Test auth command with exception"""
        mock_config_manager = Mock()
        mock_config_manager.get_all_platform_configs = AsyncMock(
            side_effect=Exception("Test error")
        )
        mock_get_config_manager.return_value = mock_config_manager

        result = runner.invoke(app, ["auth", "--list"])
        assert result.exit_code == 1
        assert "失败" in result.output

    def test_publish_nonexistent_file(self, runner):
        """Test publish command with non-existent file"""
        result = runner.invoke(app, ["publish", "/nonexistent/file.md"])
        # Should handle the error gracefully
        assert result.exit_code in [0, 1, 2]

    def test_invalid_platform(self, runner):
        """Test publish command with invalid platform"""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".md", delete=False) as temp_file:
            temp_file.write("# Test")
            temp_file_path = temp_file.name

        result = runner.invoke(app, ["publish", temp_file_path, "--platform", "invalid_platform"])
        # Should handle invalid platform
        assert result.exit_code in [0, 1, 2]

        Path(temp_file_path).unlink(missing_ok=True)


class TestCLIUtilityFunctions:
    """Test CLI utility functions"""

    def test_parse_config_value_boolean_true(self):
        """Test parse_config_value with boolean true"""
        assert parse_config_value("true") is True
        assert parse_config_value("True") is True
        assert parse_config_value("TRUE") is True

    def test_parse_config_value_boolean_false(self):
        """Test parse_config_value with boolean false"""
        assert parse_config_value("false") is False
        assert parse_config_value("False") is False
        assert parse_config_value("FALSE") is False

    def test_parse_config_value_integer(self):
        """Test parse_config_value with integer"""
        assert parse_config_value("42") == 42
        assert parse_config_value("0") == 0
        assert parse_config_value("-10") == -10

    def test_parse_config_value_float(self):
        """Test parse_config_value with float"""
        assert parse_config_value("3.14") == 3.14
        assert parse_config_value("0.0") == 0.0
        assert parse_config_value("-2.5") == -2.5

    def test_parse_config_value_string(self):
        """Test parse_config_value with string"""
        assert parse_config_value("hello") == "hello"
        assert parse_config_value("test string") == "test string"

    def test_parse_config_value_yaml_dict(self):
        """Test parse_config_value with YAML dictionary"""
        yaml_str = "key1: value1\nkey2: value2"
        result = parse_config_value(yaml_str)
        assert isinstance(result, dict)
        assert result["key1"] == "value1"
        assert result["key2"] == "value2"

    def test_parse_config_value_yaml_list(self):
        """Test parse_config_value with YAML list"""
        yaml_str = "- item1\n- item2\n- item3"
        result = parse_config_value(yaml_str)
        assert isinstance(result, list)
        assert result == ["item1", "item2", "item3"]

    def test_parse_config_value_invalid_yaml(self):
        """Test parse_config_value with invalid YAML"""
        # Should return the string as-is if YAML parsing fails
        result = parse_config_value("invalid: yaml: [")
        assert isinstance(result, str)

    def test_parse_config_value_empty_string(self):
        """Test parse_config_value with empty string"""
        result = parse_config_value("")
        # Should handle empty string appropriately
        assert result is None or result == ""

    def test_parse_config_value_none_input(self):
        """Test parse_config_value with edge cases"""
        # Test various edge cases
        assert (
            parse_config_value("null") == "null"
        )  # YAML null would be None, string null is "null"
        assert parse_config_value("~") is None  # YAML null


class TestCLIDisplayFunctions:
    """Test CLI display and output functions"""

    def test_display_config_simple(self):
        """Test _display_config with simple config"""
        from textup.cli.main import _display_config

        config = {"key1": "value1", "key2": 42, "key3": True}

        # Should not raise an exception
        try:
            _display_config(config)
            result = True
        except Exception:
            result = False
        assert result

    def test_display_config_nested(self):
        """Test _display_config with nested config"""
        from textup.cli.main import _display_config

        config = {
            "platforms": {"weibo": {"enabled": True, "timeout": 30}, "zhihu": {"enabled": False}},
            "general": {"debug": False, "log_level": "INFO"},
        }

        try:
            _display_config(config)
            result = True
        except Exception:
            result = False
        assert result

    def test_display_config_empty(self):
        """Test _display_config with empty config"""
        from textup.cli.main import _display_config

        try:
            _display_config({})
            result = True
        except Exception:
            result = False
        assert result

    def test_get_config_manager_function(self):
        """Test get_config_manager function"""
        config_mgr = get_config_manager()
        assert config_mgr is not None
        assert hasattr(config_mgr, "config_dir")

    def test_get_content_manager_function(self):
        """Test get_content_manager function"""
        content_mgr = get_content_manager()
        assert content_mgr is not None


class TestCLIAsyncFunctions:
    """Test CLI async function paths"""

    @pytest.fixture
    def runner(self):
        """CLI test runner"""
        return CliRunner()

    @patch("textup.cli.main.get_config_manager")
    def test_manage_config_async_paths(self, mock_get_config_manager, runner):
        """Test various async paths in _manage_config"""
        mock_config_manager = Mock()

        # Test get_key path
        mock_config_manager.get_config_value = AsyncMock(return_value="test_value")
        mock_get_config_manager.return_value = mock_config_manager

        result = runner.invoke(app, ["config", "--get", "test.key"])
        assert result.exit_code == 0

        # Test set_key path
        mock_config_manager.set_config_value = AsyncMock(return_value=True)
        result = runner.invoke(app, ["config", "--set", "test.key", "--value", "new_value"])
        assert result.exit_code == 0

    @patch("textup.cli.main.get_config_manager")
    def test_manage_auth_async_paths(self, mock_get_config_manager, runner):
        """Test various async paths in _manage_auth"""
        mock_config_manager = Mock()
        mock_config_manager.get_all_platform_configs = AsyncMock(return_value={})
        mock_get_config_manager.return_value = mock_config_manager

        # Test list platforms path
        result = runner.invoke(app, ["auth", "--list"])
        assert result.exit_code == 0

        # Test specific platform
        result = runner.invoke(app, ["auth", "weibo"])
        assert result.exit_code in [0, 1]


class TestCLIInteractiveFeatures:
    """Test CLI interactive features (when possible in testing)"""

    @pytest.fixture
    def runner(self):
        """CLI test runner"""
        return CliRunner()

    def test_auth_help_shows_interactive_option(self, runner):
        """Test that auth command shows interactive option"""
        result = runner.invoke(app, ["auth", "--help"])
        assert result.exit_code == 0
        assert "--interactive" in result.output or "-i" in result.output

    def test_config_help_shows_interactive_option(self, runner):
        """Test that config command shows interactive option"""
        result = runner.invoke(app, ["config", "--help"])
        assert result.exit_code == 0
        assert "--interactive" in result.output or "-i" in result.output


class TestCLIValidationAndEdgeCases:
    """Test CLI validation and edge cases"""

    @pytest.fixture
    def runner(self):
        """CLI test runner"""
        return CliRunner()

    def test_config_command_no_args(self, runner):
        """Test config command with no arguments"""
        result = runner.invoke(app, ["config"])
        # Should show help or handle gracefully
        assert result.exit_code in [0, 1, 2]

    def test_auth_command_no_args(self, runner):
        """Test auth command with no arguments"""
        result = runner.invoke(app, ["auth"])
        # Should show help or handle gracefully
        assert result.exit_code in [0, 1, 2]

    def test_publish_command_no_args(self, runner):
        """Test publish command with no arguments"""
        result = runner.invoke(app, ["publish"])
        # Should show error about missing arguments
        assert result.exit_code in [1, 2]

    def test_multiple_platforms(self, runner):
        """Test publish with multiple platforms"""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".md", delete=False) as temp_file:
            temp_file.write("# Test")
            temp_file_path = temp_file.name

        result = runner.invoke(
            app, ["publish", temp_file_path, "--platform", "weibo", "--platform", "zhihu"]
        )
        assert result.exit_code in [0, 1, 2]

        Path(temp_file_path).unlink(missing_ok=True)

    @patch("textup.cli.main.get_config_manager")
    def test_config_with_all_options(self, mock_get_config_manager, runner):
        """Test config command with multiple options"""
        mock_config_manager = Mock()
        mock_config_manager.load_config = AsyncMock(return_value={})
        mock_config_manager.backup_config = AsyncMock(return_value=True)
        mock_get_config_manager.return_value = mock_config_manager

        result = runner.invoke(app, ["config", "--list", "--backup"])
        assert result.exit_code == 0


class TestCLIConsoleUsage:
    """Test CLI console and rich integration"""

    def test_console_object_exists(self):
        """Test that console object exists and is usable"""
        from textup.cli.main import console

        assert console is not None
        assert hasattr(console, "print")

    def test_rich_imports_successful(self):
        """Test that rich imports are successful"""
        try:
            from textup.cli.main import Console, Table, Panel

            assert Console is not None
            assert Table is not None
            assert Panel is not None
            success = True
        except ImportError:
            success = False
        assert success

    def test_typer_app_structure(self):
        """Test typer app structure"""
        from textup.cli.main import app

        assert app is not None
        # App should have commands or be callable
        assert callable(app) or hasattr(app, "commands")
