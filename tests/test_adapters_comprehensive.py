"""
TextUp 适配器综合测试

本模块包含对所有适配器组件的全面测试，旨在提升测试覆盖率并验证平台集成功能。
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import aiohttp
from datetime import datetime
import json

import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent / "src"))

from textup.adapters.base import BaseAdapter
from textup.adapters.zhihu import ZhihuAdapter
from textup.adapters.weibo import WeiboAdapter
from textup.models import (
    Content,
    ContentFormat,
    Platform,
    PublishResult,
    AuthResult,
    TransformedContent,
)
from textup.utils.exceptions import (
    AuthenticationError,
    PublishError,
    ContentError,
    NetworkError,
    ConfigurationError,
)


class TestBaseAdapter:
    """基础适配器测试"""

    @pytest.fixture
    def base_adapter(self):
        """创建基础适配器实例"""
        adapter = BaseAdapter()
        adapter.platform = Platform.ZHIHU
        adapter.config = {
            "client_id": "test_client_id",
            "client_secret": "test_client_secret",
            "redirect_uri": "http://localhost:8080/callback",
        }
        return adapter

    def test_adapter_initialization(self, base_adapter):
        """测试适配器初始化"""
        assert base_adapter.platform == Platform.ZHIHU
        assert base_adapter.config["client_id"] == "test_client_id"
        assert base_adapter.authenticated is False

    @pytest.mark.asyncio
    async def test_validate_config_success(self, base_adapter):
        """测试配置验证成功"""
        # 覆盖验证方法
        base_adapter._validate_required_config = Mock(return_value=True)

        is_valid = await base_adapter.validate_config()
        assert is_valid is True

    @pytest.mark.asyncio
    async def test_validate_config_failure(self, base_adapter):
        """测试配置验证失败"""
        base_adapter.config = {}  # 空配置

        with pytest.raises(ConfigurationError):
            await base_adapter.validate_config()

    @pytest.mark.asyncio
    async def test_authenticate_not_implemented(self, base_adapter):
        """测试认证方法未实现"""
        with pytest.raises(NotImplementedError):
            await base_adapter.authenticate()

    @pytest.mark.asyncio
    async def test_publish_not_implemented(self, base_adapter):
        """测试发布方法未实现"""
        content = Content(title="测试", content="测试内容")

        with pytest.raises(NotImplementedError):
            await base_adapter.publish(content)

    def test_transform_content_default(self, base_adapter):
        """测试内容转换默认实现"""
        content = Content(title="测试文章", content="这是测试内容", tags=["test", "article"])

        transformed = base_adapter.transform_content(content)

        assert isinstance(transformed, TransformedContent)
        assert transformed.title == content.title
        assert transformed.content == content.content

    def test_format_tags(self, base_adapter):
        """测试标签格式化"""
        tags = ["Python", "AI", "机器学习"]

        formatted = base_adapter._format_tags(tags)

        assert isinstance(formatted, list)
        assert len(formatted) == 3

    def test_truncate_content(self, base_adapter):
        """测试内容截断"""
        long_content = "这是一段很长的内容" * 100

        truncated = base_adapter._truncate_content(long_content, 50)

        assert len(truncated) <= 50
        assert "..." in truncated or len(truncated) == len(long_content)

    def test_extract_summary(self, base_adapter):
        """测试摘要提取"""
        content = """# 标题

        这是第一段内容，包含重要信息。

        这是第二段内容，包含更多详细信息。

        这是第三段内容。
        """

        summary = base_adapter._extract_summary(content, 50)

        assert len(summary) <= 50
        assert "这是第一段" in summary

    @pytest.mark.asyncio
    async def test_handle_rate_limit(self, base_adapter):
        """测试速率限制处理"""
        # 模拟速率限制响应
        mock_response = Mock()
        mock_response.status = 429
        mock_response.headers = {"Retry-After": "60"}

        with patch("asyncio.sleep") as mock_sleep:
            await base_adapter._handle_rate_limit(mock_response)
            mock_sleep.assert_called_once_with(60)

    @pytest.mark.asyncio
    async def test_make_request_success(self, base_adapter):
        """测试成功的HTTP请求"""
        mock_response = Mock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value={"success": True})

        with patch.object(base_adapter, "_session") as mock_session:
            mock_session.get = AsyncMock(return_value=mock_response)

            result = await base_adapter._make_request("GET", "http://example.com")

            assert result["success"] is True

    @pytest.mark.asyncio
    async def test_make_request_network_error(self, base_adapter):
        """测试网络错误"""
        with patch.object(base_adapter, "_session") as mock_session:
            mock_session.get = AsyncMock(side_effect=aiohttp.ClientError("连接失败"))

            with pytest.raises(NetworkError):
                await base_adapter._make_request("GET", "http://example.com")


class TestZhihuAdapter:
    """知乎适配器测试"""

    @pytest.fixture
    def zhihu_adapter(self):
        """创建知乎适配器实例"""
        config = {
            "client_id": "zhihu_client_id",
            "client_secret": "zhihu_client_secret",
            "redirect_uri": "http://localhost:8080/callback",
        }
        return ZhihuAdapter(config)

    def test_zhihu_initialization(self, zhihu_adapter):
        """测试知乎适配器初始化"""
        assert zhihu_adapter.platform == Platform.ZHIHU
        assert zhihu_adapter.base_url == "https://api.zhihu.com"

    @pytest.mark.asyncio
    async def test_get_auth_url(self, zhihu_adapter):
        """测试获取授权URL"""
        auth_url = await zhihu_adapter.get_auth_url()

        assert "oauth" in auth_url
        assert zhihu_adapter.config["client_id"] in auth_url
        assert zhihu_adapter.config["redirect_uri"] in auth_url

    @pytest.mark.asyncio
    async def test_authenticate_with_code_success(self, zhihu_adapter):
        """测试使用授权码认证成功"""
        mock_token_response = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "expires_in": 3600,
        }

        mock_user_response = {"id": "test_user_id", "name": "测试用户", "url_token": "test_user"}

        with patch.object(zhihu_adapter, "_make_request") as mock_request:
            mock_request.side_effect = [mock_token_response, mock_user_response]

            result = await zhihu_adapter.authenticate_with_code("test_code")

            assert result.success is True
            assert zhihu_adapter.access_token == "test_access_token"
            assert zhihu_adapter.authenticated is True

    @pytest.mark.asyncio
    async def test_authenticate_with_code_failure(self, zhihu_adapter):
        """测试使用授权码认证失败"""
        with patch.object(zhihu_adapter, "_make_request") as mock_request:
            mock_request.side_effect = Exception("认证失败")

            result = await zhihu_adapter.authenticate_with_code("invalid_code")

            assert result.success is False
            assert "认证失败" in result.error_message

    @pytest.mark.asyncio
    async def test_refresh_token_success(self, zhihu_adapter):
        """测试刷新token成功"""
        zhihu_adapter.refresh_token = "test_refresh_token"

        mock_response = {
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
            "expires_in": 3600,
        }

        with patch.object(zhihu_adapter, "_make_request") as mock_request:
            mock_request.return_value = mock_response

            success = await zhihu_adapter.refresh_access_token()

            assert success is True
            assert zhihu_adapter.access_token == "new_access_token"

    @pytest.mark.asyncio
    async def test_get_user_info(self, zhihu_adapter):
        """测试获取用户信息"""
        zhihu_adapter.authenticated = True
        zhihu_adapter.access_token = "test_token"

        mock_response = {
            "id": "user123",
            "name": "知乎用户",
            "headline": "程序员",
            "follower_count": 1000,
        }

        with patch.object(zhihu_adapter, "_make_request") as mock_request:
            mock_request.return_value = mock_response

            user_info = await zhihu_adapter.get_user_info()

            assert user_info["name"] == "知乎用户"
            assert user_info["follower_count"] == 1000

    def test_transform_content_for_zhihu(self, zhihu_adapter):
        """测试知乎内容转换"""
        content = Content(
            title="Python编程技巧",
            content="# 介绍\n\n这是一篇关于Python的文章。\n\n```python\nprint('Hello World')\n```",
            tags=["Python", "编程", "技巧"],
        )

        transformed = zhihu_adapter.transform_content(content)

        assert transformed.title == content.title
        assert "Python" in transformed.tags
        assert len(transformed.content) > 0

    @pytest.mark.asyncio
    async def test_publish_article_success(self, zhihu_adapter):
        """测试发布文章成功"""
        zhihu_adapter.authenticated = True
        zhihu_adapter.access_token = "test_token"

        content = Content(title="测试文章", content="这是测试内容", tags=["测试"])

        mock_response = {
            "id": "article_123",
            "url": "https://zhuanlan.zhihu.com/p/123456789",
            "title": "测试文章",
        }

        with patch.object(zhihu_adapter, "_make_request") as mock_request:
            mock_request.return_value = mock_response

            result = await zhihu_adapter.publish(content)

            assert result.success is True
            assert result.platform_post_id == "article_123"
            assert "zhihu.com" in result.post_url

    @pytest.mark.asyncio
    async def test_publish_without_authentication(self, zhihu_adapter):
        """测试未认证状态下发布"""
        content = Content(title="测试", content="测试内容")

        with pytest.raises(AuthenticationError):
            await zhihu_adapter.publish(content)

    @pytest.mark.asyncio
    async def test_validate_content_for_zhihu(self, zhihu_adapter):
        """测试知乎内容验证"""
        # 有效内容
        valid_content = Content(
            title="有效标题", content="这是足够长的有效内容" * 20, tags=["有效标签"]
        )

        is_valid, errors = await zhihu_adapter.validate_content(valid_content)
        assert is_valid is True
        assert len(errors) == 0

        # 无效内容 - 标题过长
        invalid_content = Content(title="这是一个非常长的标题" * 10, content="内容", tags=["标签"])

        is_valid, errors = await zhihu_adapter.validate_content(invalid_content)
        assert is_valid is False
        assert len(errors) > 0

    @pytest.mark.asyncio
    async def test_get_publish_status(self, zhihu_adapter):
        """测试获取发布状态"""
        zhihu_adapter.authenticated = True
        zhihu_adapter.access_token = "test_token"

        mock_response = {
            "state": "published",
            "title": "测试文章",
            "created": "2024-01-01T00:00:00Z",
        }

        with patch.object(zhihu_adapter, "_make_request") as mock_request:
            mock_request.return_value = mock_response

            status = await zhihu_adapter.get_publish_status("article_123")

            assert status["state"] == "published"

    @pytest.mark.asyncio
    async def test_delete_post(self, zhihu_adapter):
        """测试删除文章"""
        zhihu_adapter.authenticated = True
        zhihu_adapter.access_token = "test_token"

        with patch.object(zhihu_adapter, "_make_request") as mock_request:
            mock_request.return_value = {"success": True}

            success = await zhihu_adapter.delete_post("article_123")

            assert success is True


class TestWeiboAdapter:
    """微博适配器测试"""

    @pytest.fixture
    def weibo_adapter(self):
        """创建微博适配器实例"""
        config = {
            "app_key": "weibo_app_key",
            "app_secret": "weibo_app_secret",
            "redirect_uri": "http://localhost:8080/callback",
        }
        return WeiboAdapter(config)

    def test_weibo_initialization(self, weibo_adapter):
        """测试微博适配器初始化"""
        assert weibo_adapter.platform == Platform.WEIBO
        assert weibo_adapter.base_url == "https://api.weibo.com"

    @pytest.mark.asyncio
    async def test_get_auth_url(self, weibo_adapter):
        """测试获取授权URL"""
        auth_url = await weibo_adapter.get_auth_url()

        assert "oauth2/authorize" in auth_url
        assert weibo_adapter.config["app_key"] in auth_url

    def test_transform_content_for_weibo(self, weibo_adapter):
        """测试微博内容转换"""
        long_content = Content(
            title="长文章标题",
            content="这是一篇很长的文章内容" * 50,  # 超过微博字数限制
            tags=["长文章", "测试"],
        )

        transformed = weibo_adapter.transform_content(long_content)

        # 微博内容应该被截断
        assert len(transformed.content) <= 280  # 微博字数限制
        assert transformed.title in transformed.content

    @pytest.mark.asyncio
    async def test_authenticate_with_code_success(self, weibo_adapter):
        """测试微博认证成功"""
        mock_response = {
            "access_token": "weibo_access_token",
            "uid": "test_uid",
            "expires_in": 7200,
        }

        with patch.object(weibo_adapter, "_make_request") as mock_request:
            mock_request.return_value = mock_response

            result = await weibo_adapter.authenticate_with_code("test_code")

            assert result.success is True
            assert weibo_adapter.access_token == "weibo_access_token"

    @pytest.mark.asyncio
    async def test_publish_weibo_success(self, weibo_adapter):
        """测试发布微博成功"""
        weibo_adapter.authenticated = True
        weibo_adapter.access_token = "test_token"

        content = Content(title="测试微博", content="这是一条测试微博", tags=["测试"])

        mock_response = {
            "id": "weibo_123456",
            "text": "这是一条测试微博",
            "created_at": "Mon Jan 01 00:00:00 +0000 2024",
        }

        with patch.object(weibo_adapter, "_make_request") as mock_request:
            mock_request.return_value = mock_response

            result = await weibo_adapter.publish(content)

            assert result.success is True
            assert result.platform_post_id == "weibo_123456"

    @pytest.mark.asyncio
    async def test_publish_with_images(self, weibo_adapter):
        """测试发布带图片的微博"""
        weibo_adapter.authenticated = True
        weibo_adapter.access_token = "test_token"

        content = Content(
            title="带图微博",
            content="这是带图的微博 ![图片](http://example.com/image.jpg)",
            tags=["图片"],
        )

        mock_upload_response = {"pic_id": "pic123"}
        mock_post_response = {
            "id": "weibo_with_pic",
            "text": "这是带图的微博",
            "pic_ids": ["pic123"],
        }

        with patch.object(weibo_adapter, "_make_request") as mock_request:
            mock_request.side_effect = [mock_upload_response, mock_post_response]

            with patch.object(weibo_adapter, "_upload_image") as mock_upload:
                mock_upload.return_value = "pic123"

                result = await weibo_adapter.publish(content)

                assert result.success is True

    @pytest.mark.asyncio
    async def test_validate_content_for_weibo(self, weibo_adapter):
        """测试微博内容验证"""
        # 有效内容
        valid_content = Content(title="微博标题", content="这是有效的微博内容", tags=["微博"])

        is_valid, errors = await weibo_adapter.validate_content(valid_content)
        assert is_valid is True

        # 空内容
        empty_content = Content(title="", content="", tags=[])

        is_valid, errors = await weibo_adapter.validate_content(empty_content)
        assert is_valid is False
        assert len(errors) > 0

    @pytest.mark.asyncio
    async def test_get_user_timeline(self, weibo_adapter):
        """测试获取用户时间线"""
        weibo_adapter.authenticated = True
        weibo_adapter.access_token = "test_token"

        mock_response = {"statuses": [{"id": "1", "text": "微博1"}, {"id": "2", "text": "微博2"}]}

        with patch.object(weibo_adapter, "_make_request") as mock_request:
            mock_request.return_value = mock_response

            timeline = await weibo_adapter.get_user_timeline()

            assert len(timeline["statuses"]) == 2

    @pytest.mark.asyncio
    async def test_repost_weibo(self, weibo_adapter):
        """测试转发微博"""
        weibo_adapter.authenticated = True
        weibo_adapter.access_token = "test_token"

        mock_response = {
            "id": "repost_123",
            "text": "转发理由",
            "retweeted_status": {"id": "original_123"},
        }

        with patch.object(weibo_adapter, "_make_request") as mock_request:
            mock_request.return_value = mock_response

            result = await weibo_adapter.repost("original_123", "转发理由")

            assert result["id"] == "repost_123"

    @pytest.mark.asyncio
    async def test_upload_image_failure(self, weibo_adapter):
        """测试图片上传失败"""
        weibo_adapter.authenticated = True

        with patch.object(weibo_adapter, "_make_request") as mock_request:
            mock_request.side_effect = Exception("上传失败")

            with pytest.raises(Exception):
                await weibo_adapter._upload_image(b"fake_image_data")

    @pytest.mark.asyncio
    async def test_handle_api_error(self, weibo_adapter):
        """测试API错误处理"""
        # 测试限流错误
        rate_limit_response = Mock()
        rate_limit_response.status = 429
        rate_limit_response.json = AsyncMock(
            return_value={"error_code": 10023, "error": "接口调用超限"}
        )

        with pytest.raises(PublishError) as exc_info:
            await weibo_adapter._handle_api_error(rate_limit_response)

        assert "接口调用超限" in str(exc_info.value)


class TestAdapterIntegration:
    """适配器集成测试"""

    @pytest.mark.asyncio
    async def test_multi_platform_authentication(self):
        """测试多平台认证"""
        zhihu_config = {
            "client_id": "zhihu_id",
            "client_secret": "zhihu_secret",
            "redirect_uri": "http://localhost:8080/callback",
        }

        weibo_config = {
            "app_key": "weibo_key",
            "app_secret": "weibo_secret",
            "redirect_uri": "http://localhost:8080/callback",
        }

        zhihu_adapter = ZhihuAdapter(zhihu_config)
        weibo_adapter = WeiboAdapter(weibo_config)

        adapters = [zhihu_adapter, weibo_adapter]

        # 模拟认证流程
        for adapter in adapters:
            auth_url = await adapter.get_auth_url()
            assert "oauth" in auth_url or "authorize" in auth_url

    @pytest.mark.asyncio
    async def test_cross_platform_content_compatibility(self):
        """测试跨平台内容兼容性"""
        content = Content(
            title="跨平台测试文章",
            content="这是一篇测试文章，需要在多个平台发布。包含**粗体**和*斜体*文本。",
            tags=["跨平台", "测试", "Python"],
        )

        zhihu_config = {"client_id": "test", "client_secret": "test", "redirect_uri": "test"}
        weibo_config = {"app_key": "test", "app_secret": "test", "redirect_uri": "test"}

        zhihu_adapter = ZhihuAdapter(zhihu_config)
        weibo_adapter = WeiboAdapter(weibo_config)

        # 转换内容格式
        zhihu_transformed = zhihu_adapter.transform_content(content)
        weibo_transformed = weibo_adapter.transform_content(content)

        # 知乎应该保持完整内容
        assert len(zhihu_transformed.content) >= len(content.content)

        # 微博应该截断长内容
        if len(content.content) > 280:
            assert len(weibo_transformed.content) <= 280

    @pytest.mark.asyncio
    async def test_error_handling_consistency(self):
        """测试错误处理一致性"""
        zhihu_config = {"client_id": "test", "client_secret": "test", "redirect_uri": "test"}
        weibo_config = {"app_key": "test", "app_secret": "test", "redirect_uri": "test"}

        zhihu_adapter = ZhihuAdapter(zhihu_config)
        weibo_adapter = WeiboAdapter(weibo_config)

        # 测试未认证错误
        content = Content(title="测试", content="测试")

        with pytest.raises(AuthenticationError):
            await zhihu_adapter.publish(content)

        with pytest.raises(AuthenticationError):
            await weibo_adapter.publish(content)
