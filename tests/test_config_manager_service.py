"""
TextUp ConfigManager 服务层专项测试

本模块专门测试配置管理器服务的各项功能，包括配置加载、保存、验证、
环境变量处理、平台配置管理等核心功能。
"""

import pytest
import tempfile
import shutil
import yaml
import json
import os
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock, mock_open
from datetime import datetime
import asyncio

import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent / "src"))

from textup.services.config_manager import ConfigManager
from textup.models import Platform
from textup.utils.exceptions import ConfigurationError, ConfigNotFoundError, ConfigValidationError


class TestConfigManagerInitialization:
    """配置管理器初始化测试"""

    def test_init_default_paths(self):
        """测试默认路径初始化"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_manager = ConfigManager(temp_dir)

            assert config_manager.config_dir == Path(temp_dir)
            assert config_manager.default_config_file == Path(temp_dir) / "config.yaml"
            assert config_manager.platform_config_file == Path(temp_dir) / "platforms.yaml"

    def test_init_custom_paths(self):
        """测试自定义路径初始化"""
        with tempfile.TemporaryDirectory() as temp_dir:
            custom_config = "custom_config.yml"
            custom_platform = "custom_platforms.yml"

            config_manager = ConfigManager(
                config_dir=temp_dir,
                default_config_file=custom_config,
                platform_config_file=custom_platform,
            )

            assert config_manager.default_config_file == Path(temp_dir) / custom_config
            assert config_manager.platform_config_file == Path(temp_dir) / custom_platform

    def test_init_creates_config_directory(self):
        """测试初始化时创建配置目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir) / "new_config_dir"

            config_manager = ConfigManager(str(config_dir))

            assert config_dir.exists()
            assert config_dir.is_dir()

    def test_init_caches_initialized_empty(self):
        """测试初始化时缓存为空"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_manager = ConfigManager(temp_dir)

            assert config_manager._app_config is None
            assert config_manager._platform_configs == {}


class TestConfigManagerDefaultConfig:
    """默认配置生成和管理测试"""

    @pytest.fixture
    def config_manager(self):
        """创建配置管理器实例"""
        temp_dir = tempfile.mkdtemp()
        manager = ConfigManager(temp_dir)
        yield manager
        shutil.rmtree(temp_dir)

    def test_get_default_app_config(self, config_manager):
        """测试获取默认应用配置"""
        default_config = config_manager._get_default_app_config()

        assert isinstance(default_config, dict)
        assert "app" in default_config
        assert "database" in default_config
        assert "logging" in default_config

        app_config = default_config["app"]
        assert app_config["name"] == "TextUp"
        assert app_config["version"] is not None
        assert app_config["debug"] is False

    def test_get_default_platform_configs(self, config_manager):
        """测试获取默认平台配置"""
        default_configs = config_manager._get_default_platform_configs()

        assert isinstance(default_configs, dict)
        assert "platforms" in default_configs

        platforms = default_configs["platforms"]
        assert "zhihu" in platforms
        assert "weibo" in platforms

        # 检查知乎配置结构
        zhihu_config = platforms["zhihu"]
        assert "enabled" in zhihu_config
        assert "client_id" in zhihu_config
        assert "client_secret" in zhihu_config

    @pytest.mark.asyncio
    async def test_initialize_default_configs(self, config_manager):
        """测试初始化默认配置"""
        success = await config_manager.initialize()

        assert success is True
        assert config_manager.default_config_file.exists()
        assert config_manager.platform_config_file.exists()

        # 验证配置文件内容
        with open(config_manager.default_config_file) as f:
            app_config = yaml.safe_load(f)
            assert "app" in app_config
            assert app_config["app"]["name"] == "TextUp"


class TestConfigManagerLoadSave:
    """配置加载和保存测试"""

    @pytest.fixture
    def config_manager(self):
        temp_dir = tempfile.mkdtemp()
        manager = ConfigManager(temp_dir)
        yield manager
        shutil.rmtree(temp_dir)

    @pytest.mark.asyncio
    async def test_load_config_creates_default_when_missing(self, config_manager):
        """测试配置文件不存在时创建默认配置"""
        config = await config_manager.load_config()

        assert isinstance(config, dict)
        assert "app" in config
        assert config_manager.default_config_file.exists()

    @pytest.mark.asyncio
    async def test_load_existing_config(self, config_manager):
        """测试加载已存在的配置文件"""
        # 创建测试配置
        test_config = {
            "app": {"name": "Test App", "version": "1.0.0"},
            "database": {"url": "sqlite:///test.db"},
            "logging": {"level": "DEBUG"},
        }

        with open(config_manager.default_config_file, "w") as f:
            yaml.dump(test_config, f)

        config = await config_manager.load_config()

        assert config["app"]["name"] == "Test App"
        assert config["app"]["version"] == "1.0.0"
        assert config["database"]["url"] == "sqlite:///test.db"

    @pytest.mark.asyncio
    async def test_save_config_creates_file(self, config_manager):
        """测试保存配置创建文件"""
        test_config = {"app": {"name": "Saved App", "version": "2.0.0"}}

        success = await config_manager.save_config(test_config)

        assert success is True
        assert config_manager.default_config_file.exists()

        # 验证保存的内容
        with open(config_manager.default_config_file) as f:
            saved_config = yaml.safe_load(f)
            assert saved_config["app"]["name"] == "Saved App"

    @pytest.mark.asyncio
    async def test_save_config_overwrites_existing(self, config_manager):
        """测试保存配置覆盖已存在文件"""
        # 先创建一个配置
        original_config = {"app": {"name": "Original"}}
        await config_manager.save_config(original_config)

        # 再保存新配置
        new_config = {"app": {"name": "Updated"}}
        success = await config_manager.save_config(new_config)

        assert success is True

        # 验证配置被更新
        config = await config_manager.load_config()
        assert config["app"]["name"] == "Updated"

    @pytest.mark.asyncio
    async def test_load_config_invalid_yaml(self, config_manager):
        """测试加载无效的YAML配置"""
        # 创建无效的YAML文件
        with open(config_manager.default_config_file, "w") as f:
            f.write("invalid: yaml: content: [")

        with pytest.raises(ConfigurationError):
            await config_manager.load_config()

    @pytest.mark.asyncio
    async def test_save_config_permission_error(self, config_manager):
        """测试保存配置时权限错误"""
        with patch("aiofiles.open", side_effect=PermissionError("Permission denied")):
            test_config = {"app": {"name": "Test"}}

            with pytest.raises(ConfigurationError):
                await config_manager.save_config(test_config)


class TestConfigManagerKeyValueOperations:
    """配置键值操作测试"""

    @pytest.fixture
    def config_manager_with_config(self):
        temp_dir = tempfile.mkdtemp()
        manager = ConfigManager(temp_dir)

        # 预先加载配置
        asyncio.run(manager.initialize())

        yield manager
        shutil.rmtree(temp_dir)

    @pytest.mark.asyncio
    async def test_set_config_value_simple_key(self, config_manager_with_config):
        """测试设置简单键值"""
        manager = config_manager_with_config

        success = await manager.set_config_value("app.debug", True)
        assert success is True

        value = await manager.get_config_value("app.debug")
        assert value is True

    @pytest.mark.asyncio
    async def test_set_config_value_nested_key(self, config_manager_with_config):
        """测试设置嵌套键值"""
        manager = config_manager_with_config

        success = await manager.set_config_value("database.pool.max_connections", 20)
        assert success is True

        value = await manager.get_config_value("database.pool.max_connections")
        assert value == 20

    @pytest.mark.asyncio
    async def test_get_config_value_existing_key(self, config_manager_with_config):
        """测试获取已存在的键值"""
        manager = config_manager_with_config

        # 先设置一个值
        await manager.set_config_value("test.key", "test_value")

        value = await manager.get_config_value("test.key")
        assert value == "test_value"

    @pytest.mark.asyncio
    async def test_get_config_value_nonexistent_key(self, config_manager_with_config):
        """测试获取不存在的键值"""
        manager = config_manager_with_config

        value = await manager.get_config_value("nonexistent.key")
        assert value is None

    @pytest.mark.asyncio
    async def test_get_config_value_with_default(self, config_manager_with_config):
        """测试获取键值时使用默认值"""
        manager = config_manager_with_config

        value = await manager.get_config_value("nonexistent.key", "default_value")
        assert value == "default_value"

    @pytest.mark.asyncio
    async def test_set_config_value_creates_nested_structure(self, config_manager_with_config):
        """测试设置键值时创建嵌套结构"""
        manager = config_manager_with_config

        success = await manager.set_config_value("new.nested.deep.key", "deep_value")
        assert success is True

        # 验证嵌套结构被创建
        config = await manager.load_config()
        assert config["new"]["nested"]["deep"]["key"] == "deep_value"

    @pytest.mark.asyncio
    async def test_delete_config_value(self, config_manager_with_config):
        """测试删除配置值"""
        manager = config_manager_with_config

        # 先设置一个值
        await manager.set_config_value("temp.key", "temp_value")
        assert await manager.get_config_value("temp.key") == "temp_value"

        # 删除值
        success = await manager.delete_config_value("temp.key")
        assert success is True

        # 验证值已被删除
        value = await manager.get_config_value("temp.key")
        assert value is None


class TestConfigManagerPlatformConfig:
    """平台配置管理测试"""

    @pytest.fixture
    def config_manager(self):
        temp_dir = tempfile.mkdtemp()
        manager = ConfigManager(temp_dir)
        asyncio.run(manager.initialize())
        yield manager
        shutil.rmtree(temp_dir)

    @pytest.mark.asyncio
    async def test_get_platform_config_existing(self, config_manager):
        """测试获取已存在的平台配置"""
        # 设置知乎平台配置
        await config_manager.set_config_value("platforms.zhihu.client_id", "test_client_id")
        await config_manager.set_config_value("platforms.zhihu.enabled", True)

        platform_config = await config_manager.get_platform_config(Platform.ZHIHU)

        assert platform_config is not None
        assert platform_config["client_id"] == "test_client_id"
        assert platform_config["enabled"] is True

    @pytest.mark.asyncio
    async def test_get_platform_config_nonexistent(self, config_manager):
        """测试获取不存在的平台配置"""
        platform_config = await config_manager.get_platform_config(Platform.XIAOHONGSHU)

        # 应该返回默认配置或None
        assert platform_config is None or isinstance(platform_config, dict)

    @pytest.mark.asyncio
    async def test_set_platform_config(self, config_manager):
        """测试设置平台配置"""
        platform_config = {
            "client_id": "new_client_id",
            "client_secret": "new_client_secret",
            "enabled": True,
        }

        success = await config_manager.set_platform_config(Platform.WEIBO, platform_config)
        assert success is True

        # 验证配置被保存
        retrieved_config = await config_manager.get_platform_config(Platform.WEIBO)
        assert retrieved_config["client_id"] == "new_client_id"
        assert retrieved_config["enabled"] is True

    @pytest.mark.asyncio
    async def test_update_platform_config(self, config_manager):
        """测试更新平台配置"""
        # 先设置初始配置
        initial_config = {"client_id": "initial_id", "enabled": False}
        await config_manager.set_platform_config(Platform.ZHIHU, initial_config)

        # 更新配置
        update_config = {"client_id": "updated_id", "enabled": True, "new_field": "new_value"}
        success = await config_manager.update_platform_config(Platform.ZHIHU, update_config)
        assert success is True

        # 验证配置被更新
        final_config = await config_manager.get_platform_config(Platform.ZHIHU)
        assert final_config["client_id"] == "updated_id"
        assert final_config["enabled"] is True
        assert final_config["new_field"] == "new_value"

    @pytest.mark.asyncio
    async def test_get_all_platform_configs(self, config_manager):
        """测试获取所有平台配置"""
        # 设置多个平台配置
        await config_manager.set_platform_config(Platform.ZHIHU, {"enabled": True})
        await config_manager.set_platform_config(Platform.WEIBO, {"enabled": False})

        all_configs = await config_manager.get_all_platform_configs()

        assert isinstance(all_configs, dict)
        assert Platform.ZHIHU in all_configs or "zhihu" in all_configs
        assert Platform.WEIBO in all_configs or "weibo" in all_configs


class TestConfigManagerValidation:
    """配置验证测试"""

    @pytest.fixture
    def config_manager(self):
        temp_dir = tempfile.mkdtemp()
        manager = ConfigManager(temp_dir)
        yield manager
        shutil.rmtree(temp_dir)

    @pytest.mark.asyncio
    async def test_validate_config_valid(self, config_manager):
        """测试验证有效配置"""
        valid_config = {
            "app": {"name": "TextUp", "version": "1.0.0", "debug": False},
            "database": {"url": "sqlite:///textup.db"},
        }

        await config_manager.save_config(valid_config)
        is_valid, errors = await config_manager.validate_config()

        assert is_valid is True
        assert len(errors) == 0

    @pytest.mark.asyncio
    async def test_validate_config_missing_required_fields(self, config_manager):
        """测试验证缺少必需字段的配置"""
        invalid_config = {
            "app": {
                # 缺少 'name' 字段
                "version": "1.0.0"
            }
        }

        await config_manager.save_config(invalid_config)
        is_valid, errors = await config_manager.validate_config()

        assert is_valid is False
        assert len(errors) > 0
        assert any("name" in error.lower() for error in errors)

    @pytest.mark.asyncio
    async def test_validate_config_invalid_types(self, config_manager):
        """测试验证类型错误的配置"""
        invalid_config = {"app": {"name": "TextUp", "debug": "not_a_boolean"}}  # 应该是布尔值

        await config_manager.save_config(invalid_config)
        is_valid, errors = await config_manager.validate_config()

        assert is_valid is False
        assert len(errors) > 0

    @pytest.mark.asyncio
    async def test_validate_platform_config_valid(self, config_manager):
        """测试验证有效的平台配置"""
        valid_platform_config = {
            "client_id": "valid_client_id",
            "client_secret": "valid_client_secret",
            "enabled": True,
        }

        is_valid, errors = await config_manager.validate_platform_config(
            Platform.ZHIHU, valid_platform_config
        )

        assert is_valid is True
        assert len(errors) == 0

    @pytest.mark.asyncio
    async def test_validate_platform_config_invalid(self, config_manager):
        """测试验证无效的平台配置"""
        invalid_platform_config = {
            "client_id": "",  # 空字符串
            "enabled": "not_boolean",  # 错误类型
        }

        is_valid, errors = await config_manager.validate_platform_config(
            Platform.ZHIHU, invalid_platform_config
        )

        assert is_valid is False
        assert len(errors) > 0


class TestConfigManagerEnvironmentVariables:
    """环境变量处理测试"""

    @pytest.fixture
    def config_manager(self):
        temp_dir = tempfile.mkdtemp()
        manager = ConfigManager(temp_dir)
        yield manager
        shutil.rmtree(temp_dir)

    @pytest.mark.asyncio
    async def test_resolve_environment_variables(self, config_manager):
        """测试解析环境变量"""
        # 设置测试环境变量
        os.environ["TEST_DB_URL"] = "sqlite:///test_from_env.db"

        config_with_env = {"database": {"url": "${TEST_DB_URL}"}}

        resolved_config = await config_manager._resolve_environment_variables(config_with_env)

        assert resolved_config["database"]["url"] == "sqlite:///test_from_env.db"

        # 清理环境变量
        del os.environ["TEST_DB_URL"]

    @pytest.mark.asyncio
    async def test_resolve_environment_variables_with_default(self, config_manager):
        """测试解析环境变量使用默认值"""
        config_with_env = {"app": {"name": "${NONEXISTENT_VAR:DefaultAppName}"}}

        resolved_config = await config_manager._resolve_environment_variables(config_with_env)

        assert resolved_config["app"]["name"] == "DefaultAppName"

    @pytest.mark.asyncio
    async def test_resolve_environment_variables_missing_no_default(self, config_manager):
        """测试解析不存在的环境变量且无默认值"""
        config_with_env = {"app": {"secret": "${NONEXISTENT_SECRET}"}}

        with pytest.raises(ConfigurationError, match="Environment variable .* not found"):
            await config_manager._resolve_environment_variables(config_with_env)


class TestConfigManagerBackupRestore:
    """配置备份和恢复测试"""

    @pytest.fixture
    def config_manager(self):
        temp_dir = tempfile.mkdtemp()
        manager = ConfigManager(temp_dir)
        asyncio.run(manager.initialize())
        yield manager
        shutil.rmtree(temp_dir)

    @pytest.mark.asyncio
    async def test_backup_config(self, config_manager):
        """测试配置备份"""
        # 设置一些配置
        await config_manager.set_config_value("app.name", "BackupTest")

        backup_path = await config_manager.backup_config()

        assert backup_path is not None
        assert Path(backup_path).exists()
        assert backup_path.endswith(".bak")

    @pytest.mark.asyncio
    async def test_backup_config_custom_suffix(self, config_manager):
        """测试自定义后缀的配置备份"""
        backup_path = await config_manager.backup_config(suffix="custom")

        assert backup_path is not None
        assert backup_path.endswith(".custom")

    @pytest.mark.asyncio
    async def test_restore_config(self, config_manager):
        """测试配置恢复"""
        # 设置初始配置
        await config_manager.set_config_value("app.name", "Original")

        # 创建备份
        backup_path = await config_manager.backup_config()

        # 修改配置
        await config_manager.set_config_value("app.name", "Modified")
        assert await config_manager.get_config_value("app.name") == "Modified"

        # 恢复配置
        success = await config_manager.restore_config(backup_path)
        assert success is True

        # 验证恢复
        restored_value = await config_manager.get_config_value("app.name")
        assert restored_value == "Original"

    @pytest.mark.asyncio
    async def test_restore_config_nonexistent_backup(self, config_manager):
        """测试恢复不存在的备份文件"""
        nonexistent_backup = "/nonexistent/backup.bak"

        with pytest.raises(ConfigNotFoundError):
            await config_manager.restore_config(nonexistent_backup)

    @pytest.mark.asyncio
    async def test_list_backups(self, config_manager):
        """测试列出备份文件"""
        # 创建几个备份
        backup1 = await config_manager.backup_config(suffix="backup1")
        backup2 = await config_manager.backup_config(suffix="backup2")

        backups = await config_manager.list_backups()

        assert len(backups) >= 2
        assert any(backup1 in backup for backup in backups)
        assert any(backup2 in backup for backup in backups)

    @pytest.mark.asyncio
    async def test_cleanup_old_backups(self, config_manager):
        """测试清理旧备份"""
        # 创建多个备份
        backups = []
        for i in range(5):
            backup = await config_manager.backup_config(suffix=f"test{i}")
            backups.append(backup)

        # 清理，只保留最新的2个
        cleaned = await config_manager.cleanup_backups(keep_count=2)

        assert cleaned >= 3  # 至少删除了3个

        remaining_backups = await config_manager.list_backups()
        assert len(remaining_backups) <= 2


class TestConfigManagerCaching:
    """配置缓存测试"""

    @pytest.fixture
    def config_manager(self):
        temp_dir = tempfile.mkdtemp()
        manager = ConfigManager(temp_dir)
        yield manager
        shutil.rmtree(temp_dir)

    @pytest.mark.asyncio
    async def test_config_caching(self, config_manager):
        """测试配置缓存"""
        # 第一次加载配置
        config1 = await config_manager.load_config()

        # 第二次加载配置（应该从缓存获取）
        with patch("aiofiles.open", side_effect=Exception("Should not be called")):
            config2 = await config_manager.load_config(use_cache=True)

        assert config1 == config2

    @pytest.mark.asyncio
    async def test_cache_invalidation(self, config_manager):
        """测试缓存失效"""
        # 加载配置到缓存
        await config_manager.load_config()

        # 修改配置文件
        test_config = {"app": {"name": "CacheTest"}}
        await config_manager.save_config(test_config)

        # 强制重新加载
        config = await config_manager.load_config(use_cache=False)

        assert config["app"]["name"] == "CacheTest"

    @pytest.mark.asyncio
    async def test_clear_cache(self, config_manager):
        """测试清空缓存"""
        # 加载配置到缓存
        await config_manager.load_config()
        assert config_manager._app_config is not None

        # 清空缓存
        config_manager.clear_cache()

        assert config_manager._app_config is None
        assert config_manager._platform_configs == {}


class TestConfigManagerErrorHandling:
    """配置管理器错误处理测试"""

    @pytest.fixture
    def config_manager(self):
        temp_dir = tempfile.mkdtemp()
        manager = ConfigManager(temp_dir)
        yield manager
        shutil.rmtree(temp_dir)

    @pytest.mark.asyncio
    async def test_handle_file_not_found(self, config_manager):
        """测试处理文件未找到错误"""
        # 删除配置目录
        shutil.rmtree(config_manager.config_dir)

        # 尝试加载配置时应该重新创建
        config = await config_manager.load_config()

        assert isinstance(config, dict)
        assert config_manager.config_dir.exists()

    @pytest.mark.asyncio
    async def test_handle_yaml_parse_error(self, config_manager):
        """测试处理YAML解析错误"""
        # 创建无效的YAML文件
        with open(config_manager.default_config_file, "w") as f:
            f.write("invalid: yaml: [unclosed")

        with pytest.raises(ConfigurationError):
            await config_manager.load_config()

    @pytest.mark.asyncio
    async def test_handle_permission_error(self, config_manager):
        """测试处理权限错误"""
        with patch("pathlib.Path.mkdir", side_effect=PermissionError("Permission denied")):
            with pytest.raises(ConfigurationError):
                ConfigManager("/root/forbidden")

    @pytest.mark.asyncio
    async def test_handle_disk_full_error(self, config_manager):
        """测试处理磁盘空间不足错误"""
        with patch("aiofiles.open", side_effect=OSError("No space left on device")):
            test_config = {"app": {"name": "Test"}}

            with pytest.raises(ConfigurationError):
                await config_manager.save_config(test_config)


class TestConfigManagerIntegration:
    """配置管理器集成测试"""

    @pytest.fixture
    def config_manager(self):
        temp_dir = tempfile.mkdtemp()
        manager = ConfigManager(temp_dir)
        yield manager
        shutil.rmtree(temp_dir)

    @pytest.mark.asyncio
    async def test_full_workflow(self, config_manager):
        """测试完整工作流程"""
        # 1. 初始化
        success = await config_manager.initialize()
        assert success is True

        # 2. 设置应用配置
        await config_manager.set_config_value("app.name", "IntegrationTest")
        await config_manager.set_config_value("app.debug", True)

        # 3. 设置平台配置
        zhihu_config = {"client_id": "test_client", "client_secret": "test_secret", "enabled": True}
        await config_manager.set_platform_config(Platform.ZHIHU, zhihu_config)

        # 4. 验证配置
        is_valid, errors = await config_manager.validate_config()
        assert is_valid is True or len(errors) == 0

        # 5. 备份配置
        backup_path = await config_manager.backup_config()
        assert Path(backup_path).exists()

        # 6. 修改配置
        await config_manager.set_config_value("app.name", "Modified")

        # 7. 恢复配置
        await config_manager.restore_config(backup_path)

        # 8. 验证恢复结果
        app_name = await config_manager.get_config_value("app.name")
        assert app_name == "IntegrationTest"

    @pytest.mark.asyncio
    async def test_concurrent_access(self, config_manager):
        """测试并发访问"""

        async def set_config(key, value):
            return await config_manager.set_config_value(f"test_{key}", value)

        # 创建多个并发任务
        tasks = []
        for i in range(10):
            task = asyncio.create_task(set_config(f"concurrent_{i}", f"value_{i}"))
            tasks.append(task)

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

        # 验证所有设置都成功
        assert all(results)

        # 验证值都正确设置
        for i in range(10):
            value = await config_manager.get_config_value(f"test_concurrent_{i}")
            assert value == f"value_{i}"
