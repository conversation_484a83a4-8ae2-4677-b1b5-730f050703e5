"""
知乎适配器Phase 3覆盖率提升测试

本测试文件专门用于提升ZhihuAdapter的测试覆盖率
从当前21%目标提升至60%覆盖率

测试范围：
1. 基础属性和初始化
2. 认证凭证验证
3. OAuth流程测试
4. 内容转换和验证
5. 发布功能测试
6. 错误处理测试
7. 速率限制测试
8. API请求测试
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any

from textup.adapters.zhihu import ZhihuAdapter
from textup.models import (
    Platform,
    TransformedContent,
    PublishResult,
    AuthResult,
    ValidationResult,
    ValidationError,
    ContentFormat,
)
from textup.utils import PlatformAPIError, InvalidCredentialsError, RateLimitError


class TestZhihuAdapterBasics:
    """知乎适配器基础测试"""

    def test_initialization_default(self):
        """测试默认初始化"""
        adapter = ZhihuAdapter()
        assert adapter.platform == Platform.ZHIHU
        assert adapter.base_url == "https://www.zhihu.com/api/v4"
        assert adapter.oauth_base_url == "https://www.zhihu.com/oauth"
        assert adapter.required_credentials == ["client_id", "client_secret", "redirect_uri"]
        assert adapter._client_id is None
        assert adapter._client_secret is None
        assert adapter._redirect_uri is None

    def test_initialization_with_params(self):
        """测试带参数初始化"""
        adapter = ZhihuAdapter(
            timeout=60, max_retries=4, retry_delay=3.0, rate_limit_calls=100, rate_limit_period=3600
        )
        assert adapter.timeout == 60
        assert adapter.max_retries == 4
        assert adapter.retry_delay == 3.0
        assert adapter.rate_limit_calls == 100
        assert adapter.rate_limit_period == 3600

    def test_platform_property(self):
        """测试平台属性"""
        adapter = ZhihuAdapter()
        assert adapter.platform == Platform.ZHIHU
        assert isinstance(adapter.platform, Platform)

    def test_base_url_property(self):
        """测试基础URL属性"""
        adapter = ZhihuAdapter()
        assert adapter.base_url == "https://www.zhihu.com/api/v4"

    def test_oauth_base_url_property(self):
        """测试OAuth基础URL属性"""
        adapter = ZhihuAdapter()
        assert adapter.oauth_base_url == "https://www.zhihu.com/oauth"

    def test_required_credentials_property(self):
        """测试必需认证字段属性"""
        adapter = ZhihuAdapter()
        required = adapter.required_credentials
        assert isinstance(required, list)
        assert "client_id" in required
        assert "client_secret" in required
        assert "redirect_uri" in required
        assert len(required) == 3


class TestZhihuAdapterCredentials:
    """知乎适配器认证凭证测试"""

    def test_validate_credentials_success(self):
        """测试有效凭证验证"""
        adapter = ZhihuAdapter()
        credentials = {
            "client_id": "valid_client_id_123",
            "client_secret": "valid_client_secret_456",
            "redirect_uri": "https://example.com/auth/callback",
        }

        result = adapter._validate_credentials(credentials)
        assert isinstance(result, ValidationResult)
        assert result.is_valid is True
        assert len(result.errors) == 0

    def test_validate_credentials_missing_fields(self):
        """测试缺少必需字段"""
        adapter = ZhihuAdapter()
        credentials = {}

        result = adapter._validate_credentials(credentials)
        assert result.is_valid is False
        assert len(result.errors) == 3

        error_fields = [error.field for error in result.errors]
        assert "client_id" in error_fields
        assert "client_secret" in error_fields
        assert "redirect_uri" in error_fields

    def test_validate_credentials_empty_fields(self):
        """测试空字段"""
        adapter = ZhihuAdapter()
        credentials = {"client_id": "", "client_secret": "", "redirect_uri": ""}

        result = adapter._validate_credentials(credentials)
        assert result.is_valid is False
        assert len(result.errors) == 3

        for error in result.errors:
            assert error.error_code == "EMPTY_FIELD"

    def test_validate_credentials_invalid_client_id_format(self):
        """测试client_id格式错误"""
        adapter = ZhihuAdapter()
        credentials = {
            "client_id": "abc",  # 太短
            "client_secret": "valid_secret",
            "redirect_uri": "https://example.com/callback",
        }

        result = adapter._validate_credentials(credentials)
        assert result.is_valid is False
        assert len(result.errors) == 1
        assert result.errors[0].field == "client_id"
        assert result.errors[0].error_code == "INVALID_LENGTH"
        assert "至少8个字符" in result.errors[0].message

    def test_validate_credentials_invalid_redirect_uri_format(self):
        """测试redirect_uri格式错误"""
        adapter = ZhihuAdapter()
        credentials = {
            "client_id": "valid_client_id",
            "client_secret": "valid_secret",
            "redirect_uri": "invalid_uri_format",
        }

        result = adapter._validate_credentials(credentials)
        assert result.is_valid is False
        assert len(result.errors) == 1
        assert result.errors[0].field == "redirect_uri"
        assert result.errors[0].error_code == "INVALID_URL"
        assert "HTTP URL" in result.errors[0].message

    def test_validate_credentials_mixed_errors(self):
        """测试混合错误"""
        adapter = ZhihuAdapter()
        credentials = {
            "client_id": "short",  # 太短
            "client_secret": "valid_secret",
            "redirect_uri": "not_a_url",  # 无效URL
        }

        result = adapter._validate_credentials(credentials)
        assert result.is_valid is False
        assert len(result.errors) == 2

        error_codes = [error.error_code for error in result.errors]
        assert "INVALID_LENGTH" in error_codes
        assert "INVALID_URL" in error_codes


class TestZhihuAdapterAuth:
    """知乎适配器认证测试"""

    def test_generate_auth_url_success(self):
        """测试生成认证URL"""
        adapter = ZhihuAdapter()
        credentials = {
            "client_id": "zhihu_client_123",
            "redirect_uri": "https://example.com/callback",
        }

        auth_url = adapter.generate_auth_url(credentials)

        assert auth_url.startswith("https://www.zhihu.com/oauth/authorize?")
        assert "client_id=zhihu_client_123" in auth_url
        assert "redirect_uri=https%3A//example.com/callback" in auth_url
        assert "response_type=code" in auth_url
        assert "scope=write_article" in auth_url

    def test_get_auth_headers_without_token(self):
        """测试无访问令牌时的认证头"""
        adapter = ZhihuAdapter()
        headers = adapter._get_auth_headers()
        assert headers == {}

    def test_get_auth_headers_with_token(self):
        """测试有访问令牌时的认证头"""
        adapter = ZhihuAdapter()
        adapter._auth_data = {"access_token": "zhihu_token_123"}

        headers = adapter._get_auth_headers()
        assert headers["Authorization"] == "Bearer zhihu_token_123"

    @pytest.mark.asyncio
    async def test_exchange_code_for_token_success(self):
        """测试成功交换访问令牌"""
        adapter = ZhihuAdapter()

        mock_response = {
            "access_token": "zhihu_access_token_123",
            "expires_in": 7200,
            "refresh_token": "zhihu_refresh_token_123",
            "token_type": "Bearer",
        }

        with patch.object(adapter, "_make_request", new_callable=AsyncMock) as mock_request:
            mock_request.return_value = mock_response

            credentials = {
                "client_id": "zhihu_client_123",
                "client_secret": "zhihu_secret_456",
                "redirect_uri": "https://example.com/callback",
            }

            result = await adapter.exchange_code_for_token(credentials, "auth_code_789")

            assert result == mock_response
            assert "access_token" in result

            mock_request.assert_called_once()
            call_args = mock_request.call_args
            assert call_args[1]["method"] == "POST"
            assert "token" in call_args[1]["url"]

    @pytest.mark.asyncio
    async def test_exchange_code_for_token_failure(self):
        """测试交换访问令牌失败"""
        adapter = ZhihuAdapter()

        mock_response = {"error": "invalid_grant", "error_description": "授权码无效"}

        with patch.object(adapter, "_make_request", new_callable=AsyncMock) as mock_request:
            mock_request.return_value = mock_response

            credentials = {
                "client_id": "zhihu_client_123",
                "client_secret": "zhihu_secret_456",
                "redirect_uri": "https://example.com/callback",
            }

            with pytest.raises(PlatformAPIError) as exc_info:
                await adapter.exchange_code_for_token(credentials, "invalid_code")

            assert "获取访问令牌失败" in str(exc_info.value)
            assert exc_info.value.platform == Platform.ZHIHU.value

    @pytest.mark.asyncio
    async def test_refresh_token_success(self):
        """测试成功刷新访问令牌"""
        adapter = ZhihuAdapter()

        mock_response = {
            "access_token": "new_access_token_123",
            "expires_in": 7200,
            "refresh_token": "new_refresh_token_456",
            "token_type": "Bearer",
        }

        with patch.object(adapter, "_make_request", new_callable=AsyncMock) as mock_request:
            mock_request.return_value = mock_response

            credentials = {"client_id": "zhihu_client_123", "client_secret": "zhihu_secret_456"}
            refresh_token = "current_refresh_token"

            result = await adapter.refresh_access_token(credentials, refresh_token)

            assert result == mock_response
            assert result["access_token"] == "new_access_token_123"

            mock_request.assert_called_once()
            call_args = mock_request.call_args
            assert "refresh_token" in str(call_args[1]["data"])


class TestZhihuAdapterAuthImplementation:
    """知乎适配器认证实现测试"""

    @pytest.mark.asyncio
    async def test_authenticate_impl_with_existing_token(self):
        """测试使用现有访问令牌认证"""
        adapter = ZhihuAdapter()

        mock_user_info = {"id": "zhihu_user_123", "name": "测试用户", "url_token": "test_user"}

        with patch.object(adapter, "_verify_access_token", new_callable=AsyncMock) as mock_verify:
            mock_verify.return_value = mock_user_info

            credentials = {
                "client_id": "zhihu_client_123",
                "client_secret": "zhihu_secret_456",
                "redirect_uri": "https://example.com/callback",
                "access_token": "existing_token_123",
            }

            result = await adapter._authenticate_impl(credentials)

            assert isinstance(result, AuthResult)
            assert result.is_success is True
            assert adapter._client_id == "zhihu_client_123"
            assert adapter._client_secret == "zhihu_secret_456"
            assert adapter._redirect_uri == "https://example.com/callback"

    @pytest.mark.asyncio
    async def test_authenticate_impl_token_verification_failed(self):
        """测试访问令牌验证失败"""
        adapter = ZhihuAdapter()

        with patch.object(adapter, "_verify_access_token", new_callable=AsyncMock) as mock_verify:
            mock_verify.side_effect = InvalidCredentialsError(
                message="访问令牌已过期", error_code="TOKEN_EXPIRED"
            )

            credentials = {
                "client_id": "zhihu_client_123",
                "client_secret": "zhihu_secret_456",
                "redirect_uri": "https://example.com/callback",
                "access_token": "expired_token",
            }

            result = await adapter._authenticate_impl(credentials)

            assert isinstance(result, AuthResult)
            assert result.is_success is False
            assert "访问令牌已过期" in result.error_message

    @pytest.mark.asyncio
    async def test_authenticate_impl_without_token(self):
        """测试无访问令牌的认证"""
        adapter = ZhihuAdapter()

        credentials = {
            "client_id": "zhihu_client_123",
            "client_secret": "zhihu_secret_456",
            "redirect_uri": "https://example.com/callback",
        }

        result = await adapter._authenticate_impl(credentials)

        assert isinstance(result, AuthResult)
        assert result.is_success is False
        assert "需要通过OAuth授权" in result.error_message
        assert "auth_url" in result.context


class TestZhihuAdapterContentHandling:
    """知乎适配器内容处理测试"""

    def test_validate_format_impl_markdown_valid(self):
        """测试有效Markdown内容验证"""
        adapter = ZhihuAdapter()

        markdown_content = """
# 标题
这是一段测试内容

## 二级标题
- 列表项1
- 列表项2

**粗体文字** 和 *斜体文字*
        """

        result = adapter._validate_format_impl(markdown_content.strip(), ContentFormat.MARKDOWN)

        assert isinstance(result, ValidationResult)
        assert result.is_valid is True
        assert len(result.errors) == 0

    def test_validate_format_impl_text_too_short(self):
        """测试文本过短验证"""
        adapter = ZhihuAdapter()

        short_content = "太短"

        result = adapter._validate_format_impl(short_content, ContentFormat.TEXT)

        assert isinstance(result, ValidationResult)
        assert result.is_valid is False
        assert len(result.errors) > 0
        assert "内容太短" in result.errors[0].message

    def test_validate_format_impl_unsupported_format(self):
        """测试不支持的格式验证"""
        adapter = ZhihuAdapter()

        result = adapter._validate_format_impl("content", ContentFormat.HTML)

        assert isinstance(result, ValidationResult)
        assert result.is_valid is False
        assert len(result.errors) > 0
        assert "不支持的内容格式" in result.errors[0].message

    def test_transform_content_markdown(self):
        """测试Markdown内容转换"""
        adapter = ZhihuAdapter()

        markdown_content = """
# 知乎文章标题

这是一段正文内容，包含**粗体**和*斜体*文字。

## 二级标题

- 列表项1
- 列表项2

[链接示例](https://example.com)
        """

        result = adapter.transform_content(markdown_content.strip())

        assert isinstance(result, TransformedContent)
        assert result.title == "知乎文章标题"
        assert result.format == ContentFormat.MARKDOWN
        assert "**粗体**" in result.content
        assert "- 列表项" in result.content

    def test_transform_content_text_with_title_extraction(self):
        """测试文本内容标题提取"""
        adapter = ZhihuAdapter()

        content_with_title = """我的技术分享

今天想和大家分享一些技术心得，
包括编程技巧和最佳实践。

希望对大家有帮助。"""

        result = adapter.transform_content(content_with_title)

        assert isinstance(result, TransformedContent)
        assert result.title == "我的技术分享"
        assert result.format == ContentFormat.TEXT
        assert "今天想和大家分享" in result.content

    def test_transform_content_with_tags(self):
        """测试带标签的内容转换"""
        adapter = ZhihuAdapter()

        content_with_tags = """
# 编程技巧分享

分享一些实用的编程技巧和经验。

标签：#编程 #技术分享 #Python
        """

        result = adapter.transform_content(content_with_tags.strip())

        assert isinstance(result, TransformedContent)
        assert result.title == "编程技巧分享"
        assert len(result.tags) > 0
        assert "编程" in result.tags
        assert "技术分享" in result.tags


class TestZhihuAdapterPublishing:
    """知乎适配器发布测试"""

    @pytest.mark.asyncio
    async def test_publish_impl_article_success(self):
        """测试文章发布成功"""
        adapter = ZhihuAdapter()
        adapter._auth_data = {"access_token": "valid_token"}

        mock_publish_response = {
            "id": 123456789,
            "title": "测试文章标题",
            "url": "https://zhuanlan.zhihu.com/p/123456789",
            "created": "2023-10-10T12:00:00+08:00",
        }

        with patch.object(adapter, "_make_request", new_callable=AsyncMock) as mock_request:
            mock_request.return_value = mock_publish_response

            content = """
# 测试文章标题

这是一篇测试文章的内容。
            """

            result = await adapter._publish_impl(content.strip(), {"type": "article"})

            assert isinstance(result, PublishResult)
            assert result.is_success is True
            assert result.platform_id == str(mock_publish_response["id"])
            assert result.platform_url == mock_publish_response["url"]

    @pytest.mark.asyncio
    async def test_publish_impl_answer_success(self):
        """测试回答发布成功"""
        adapter = ZhihuAdapter()
        adapter._auth_data = {"access_token": "valid_token"}

        mock_publish_response = {
            "id": 987654321,
            "content": "这是一个测试回答",
            "created_time": 1697000000,
        }

        with patch.object(adapter, "_make_request", new_callable=AsyncMock) as mock_request:
            mock_request.return_value = mock_publish_response

            content = "这是一个测试回答的内容。"
            options = {"type": "answer", "question_id": "12345"}

            result = await adapter._publish_impl(content, options)

            assert isinstance(result, PublishResult)
            assert result.is_success is True
            assert result.platform_id == str(mock_publish_response["id"])

    @pytest.mark.asyncio
    async def test_publish_impl_failure(self):
        """测试发布失败"""
        adapter = ZhihuAdapter()
        adapter._auth_data = {"access_token": "invalid_token"}

        with patch.object(adapter, "_make_request", new_callable=AsyncMock) as mock_request:
            mock_request.side_effect = PlatformAPIError(
                platform=Platform.ZHIHU.value, api_error="发布权限不足", status_code=403
            )

            content = "测试发布失败的内容"
            result = await adapter._publish_impl(content, {})

            assert isinstance(result, PublishResult)
            assert result.is_success is False
            assert "发布权限不足" in result.error_message


class TestZhihuAdapterErrorHandling:
    """知乎适配器错误处理测试"""

    @pytest.mark.asyncio
    async def test_handle_api_rate_limit_error(self):
        """测试API速率限制错误处理"""
        adapter = ZhihuAdapter()

        with patch.object(adapter, "_make_request", new_callable=AsyncMock) as mock_request:
            mock_request.side_effect = RateLimitError(message="API调用频率超限", retry_after=1800)

            with pytest.raises(RateLimitError) as exc_info:
                await adapter._make_request("POST", "test_url", {})

            assert "API调用频率超限" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_handle_content_policy_violation(self):
        """测试内容政策违规错误处理"""
        adapter = ZhihuAdapter()

        with patch.object(adapter, "_make_request", new_callable=AsyncMock) as mock_request:
            mock_request.side_effect = PlatformAPIError(
                platform=Platform.ZHIHU.value, api_error="内容违反社区规范", status_code=400
            )

            with pytest.raises(PlatformAPIError) as exc_info:
                await adapter._make_request("POST", "articles", {})

            assert "内容违反社区规范" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_handle_authentication_expired(self):
        """测试认证过期错误处理"""
        adapter = ZhihuAdapter()

        with patch.object(adapter, "_make_request", new_callable=AsyncMock) as mock_request:
            mock_request.side_effect = InvalidCredentialsError(
                message="访问令牌已过期，请重新授权", error_code="TOKEN_EXPIRED"
            )

            with pytest.raises(InvalidCredentialsError) as exc_info:
                await adapter._make_request("GET", "me", {})

            assert "访问令牌已过期" in str(exc_info.value)


class TestZhihuAdapterIntegration:
    """知乎适配器集成测试"""

    @pytest.mark.asyncio
    async def test_full_article_publish_workflow(self):
        """测试完整文章发布工作流"""
        adapter = ZhihuAdapter()
        adapter._auth_data = {"access_token": "valid_token"}

        # 模拟文章发布成功
        mock_response = {
            "id": 123456789,
            "title": "技术分享文章",
            "url": "https://zhuanlan.zhihu.com/p/123456789",
            "state": "published",
        }

        with patch.object(adapter, "_make_request", new_callable=AsyncMock) as mock_request:
            mock_request.return_value = mock_response

            # 准备文章内容
            article_content = """
# 技术分享文章

这是一篇关于编程技巧的文章。

## 主要内容
- 编程最佳实践
- 代码优化技巧
- 性能调优方法

希望对大家有帮助！
            """

            result = await adapter._publish_impl(article_content.strip(), {"type": "article"})

            assert result.is_success is True
            assert result.platform_id == "123456789"
            assert "zhuanlan.zhihu.com" in result.platform_url

    @pytest.mark.asyncio
    async def test_full_answer_publish_workflow(self):
        """测试完整回答发布工作流"""
        adapter = ZhihuAdapter()
        adapter._auth_data = {"access_token": "valid_token"}

        mock_response = {
            "id": 987654321,
            "content": "详细的回答内容",
            "question": {"id": 12345, "title": "技术问题"},
            "created_time": 1697000000,
        }

        with patch.object(adapter, "_make_request", new_callable=AsyncMock) as mock_request:
            mock_request.return_value = mock_response

            answer_content = """
根据我的经验，这个问题可以这样解决：

1. 首先分析问题的根本原因
2. 然后制定解决方案
3. 最后实施并验证效果

希望这个回答对你有帮助。
            """

            options = {"type": "answer", "question_id": "12345"}

            result = await adapter._publish_impl(answer_content.strip(), options)

            assert result.is_success is True
            assert result.platform_id == "987654321"

    def test_content_preprocessing_pipeline(self):
        """测试内容预处理流水线"""
        adapter = ZhihuAdapter()

        # 测试复杂内容的预处理
        complex_content = """
# 复杂内容测试

这是一篇包含多种元素的内容：

## 代码示例
```python
def hello_world():
    print("Hello, World!")
```

## 数学公式
$$E = mc^2$$

## 引用
> 这是一段引用文字

## 链接和图片
[知乎链接](https://www.zhihu.com)
![示例图片](https://example.com/image.jpg)

标签：#编程 #Python #技术
        """

        result = adapter.transform_content(complex_content.strip())

        assert isinstance(result, TransformedContent)
        assert result.title == "复杂内容测试"
        assert result.format == ContentFormat.MARKDOWN
        assert "```python" in result.content
        assert len(result.tags) > 0


class TestZhihuAdapterEdgeCases:
    """知乎适配器边界情况测试"""

    def test_empty_content_handling(self):
        """测试空内容处理"""
        adapter = ZhihuAdapter()

        result = adapter.transform_content("")

        assert isinstance(result, TransformedContent)
        assert result.content == ""
        assert result.format == ContentFormat.TEXT

    def test_content_with_only_title(self):
        """测试只有标题的内容"""
        adapter = ZhihuAdapter()

        content = "# 只有标题"
        result = adapter.transform_content(content)

        assert isinstance(result, TransformedContent)
        assert result.title == "只有标题"
        assert len(result.content.strip()) == 0

    def test_very_long_content_handling(self):
        """测试超长内容处理"""
        adapter = ZhihuAdapter()

        # 创建一个很长的内容
        long_content = "# 超长内容测试\n\n" + "这是一段很长的内容。" * 1000

        result = adapter.transform_content(long_content)

        assert isinstance(result, TransformedContent)
        assert result.title == "超长内容测试"
        # 知乎支持长文，所以内容不应该被截断
        assert len(result.content) > 10000

    def test_special_markdown_elements(self):
        """测试特殊Markdown元素处理"""
        adapter = ZhihuAdapter()

        content_with_special_elements = """
# 特殊元素测试

## 表格
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |

## 任务列表
- [x] 已完成任务
- [ ] 未完成任务

## 脚注
这是带脚注的文字[^1]

[^1]: 这是脚注内容

## 高亮
==高亮文字==

## 下划线
<u>下划线文字</u>
        """

        result = adapter.transform_content(content_with_special_elements.strip())

        assert isinstance(result, TransformedContent)
        assert result.title == "特殊元素测试"
        assert "|" in result.content  # 表格
        assert "- [x]" in result.content  # 任务列表
        assert "[^1]" in result.content  # 脚注

    @pytest.mark.asyncio
    async def test_concurrent_publish_operations(self):
        """测试并发发布操作"""
        adapter = ZhihuAdapter()
        adapter._auth_data = {"access_token": "valid_token"}

        mock_responses = [
            {"id": f"article_{i}", "title": f"文章{i}", "url": f"https://zhuanlan.zhihu.com/p/{i}"}
            for i in range(3)
        ]

        with patch.object(adapter, "_make_request", new_callable=AsyncMock) as mock_request:
            mock_request.side_effect = mock_responses

            # 模拟并发发布多篇文章
            tasks = [adapter._publish_impl(f"# 文章{i}\n\n文章内容{i}") for i in range(3)]

            # 并发执行所有任务
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 验证所有请求都成功
            assert len(results) == 3
            for i, result in enumerate(results):
                assert not isinstance(result, Exception)
                assert result["id"] == f"article_{i}"
                assert result["title"] == f"文章{i}"
                assert result["url"] == f"https://zhuanlan.zhihu.com/p/{i}"

            # 验证请求次数正确
            assert mock_request.call_count == 3
