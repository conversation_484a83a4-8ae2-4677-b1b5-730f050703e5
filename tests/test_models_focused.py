"""
TextUp 数据模型专项测试

本模块专门测试数据模型的各种场景，旨在提升测试覆盖率。
"""

import pytest
from datetime import datetime
from typing import List, Dict, Any
import uuid
import json

import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent / "src"))

from textup.models import (
    Content,
    ContentFormat,
    Platform,
    TaskStatus,
    PublishStatus,
    PublishStrategy,
    ContentMetrics,
    TransformedContent,
    PublishTask,
    PublishRecord,
    ZhihuCredentials,
    WeiboCredentials,
)


class TestContentModel:
    """内容模型测试"""

    def test_content_creation_with_defaults(self):
        """测试使用默认值创建内容"""
        content = Content(title="测试文章", content="这是测试内容")

        assert content.title == "测试文章"
        assert content.content == "这是测试内容"
        assert content.content_format == ContentFormat.MARKDOWN
        assert content.tags == []
        assert content.metadata == {}
        assert isinstance(content.id, str)
        assert len(content.id) > 0
        assert isinstance(content.created_at, datetime)
        assert isinstance(content.updated_at, datetime)

    def test_content_creation_with_all_fields(self):
        """测试创建包含所有字段的内容"""
        metadata = {"author": "test", "category": "tech"}
        tags = ["Python", "测试", "技术"]

        content = Content(
            title="完整测试文章",
            content="这是完整的测试内容，包含多个段落。\n\n第二段内容。",
            content_format=ContentFormat.HTML,
            source_file_path="/path/to/article.md",
            tags=tags,
            metadata=metadata,
        )

        assert content.title == "完整测试文章"
        assert content.content_format == ContentFormat.HTML
        assert content.source_file_path == "/path/to/article.md"
        assert content.tags == tags
        assert content.metadata == metadata

    def test_content_validation_empty_title(self):
        """测试空标题验证"""
        with pytest.raises(ValueError, match="标题不能为空"):
            Content(title="", content="内容")

    def test_content_validation_whitespace_title(self):
        """测试空白标题验证"""
        with pytest.raises(ValueError, match="标题不能为空"):
            Content(title="   ", content="内容")

    def test_content_validation_long_title(self):
        """测试超长标题验证"""
        long_title = "a" * 201  # 超过200字符
        with pytest.raises(ValueError, match="标题长度不能超过200字符"):
            Content(title=long_title, content="内容")

    def test_content_validation_empty_content(self):
        """测试空内容验证"""
        with pytest.raises(ValueError, match="内容不能为空"):
            Content(title="标题", content="")

    def test_content_validation_whitespace_content(self):
        """测试空白内容验证"""
        with pytest.raises(ValueError, match="内容不能为空"):
            Content(title="标题", content="   ")

    def test_content_validation_too_many_tags(self):
        """测试标签过多验证"""
        too_many_tags = [f"标签{i}" for i in range(25)]  # 超过20个
        with pytest.raises(ValueError, match="标签数量不能超过20个"):
            Content(title="标题", content="内容", tags=too_many_tags)

    def test_content_tag_cleaning(self):
        """测试标签清理"""
        tags_with_whitespace = ["  Python  ", "", "  测试", "   ", "AI  "]
        content = Content(title="标题", content="内容", tags=tags_with_whitespace)

        # 应该自动清理空标签和首尾空白
        expected_tags = ["Python", "测试", "AI"]
        assert content.tags == expected_tags

    def test_content_utility_methods(self):
        """测试内容工具方法"""
        content = Content(
            title="测试文章", content="这是测试内容，包含**粗体**和![图片](image.jpg)链接。"
        )

        # 测试字数统计
        word_count = content.get_word_count()
        assert word_count > 0

        # 测试字符数统计
        char_count = content.get_char_count()
        assert char_count > 0
        assert char_count >= word_count

        # 测试图片检测
        assert content.has_images() is True

        # 测试图片提取
        images = content.extract_images()
        assert "image.jpg" in images[0] if images else False

    def test_content_json_serialization(self):
        """测试JSON序列化"""
        content = Content(title="JSON测试", content="JSON测试内容", tags=["json", "test"])

        json_str = content.to_json()
        assert isinstance(json_str, str)

        # 解析JSON验证
        data = json.loads(json_str)
        assert data["title"] == "JSON测试"
        assert data["tags"] == ["json", "test"]

    def test_content_dict_conversion(self):
        """测试字典转换"""
        content = Content(title="字典测试", content="字典测试内容")

        content_dict = content.to_dict()
        assert isinstance(content_dict, dict)
        assert content_dict["title"] == "字典测试"
        assert "id" in content_dict
        assert "created_at" in content_dict


class TestContentMetrics:
    """内容指标模型测试"""

    def test_content_metrics_creation(self):
        """测试内容指标创建"""
        # 不直接创建，因为estimated_read_time会被自动计算
        metrics = ContentMetrics(
            word_count=200,  # 使用会触发计算的值
            char_count=500,
            paragraph_count=3,
            image_count=2,
            link_count=1,
        )

        assert metrics.word_count == 200
        assert metrics.char_count == 500
        assert metrics.paragraph_count == 3
        assert metrics.image_count == 2
        assert metrics.link_count == 1
        assert metrics.estimated_read_time == 60  # 200字 -> 60秒

    def test_read_time_calculation(self):
        """测试阅读时间计算"""
        # 200字应该大约需要60秒阅读时间（每分钟200字）
        metrics = ContentMetrics(word_count=200)
        assert metrics.estimated_read_time == 60

        # 少量文字的最小阅读时间
        metrics = ContentMetrics(word_count=100)  # 使用足够触发计算的值
        assert metrics.estimated_read_time == 30


class TestTransformedContent:
    """转换内容模型测试"""

    def test_transformed_content_creation(self):
        """测试转换内容创建"""
        transformed = TransformedContent(
            title="转换标题",
            content="# 原始内容",
            html="<h1>原始内容</h1>",
            text="原始内容",
            tags=["转换", "测试"],
        )

        assert transformed.title == "转换标题"
        assert transformed.content == "# 原始内容"
        assert transformed.html == "<h1>原始内容</h1>"
        assert transformed.text == "原始内容"
        assert transformed.tags == ["转换", "测试"]

    def test_transformed_content_metrics_auto_calculation(self):
        """测试转换内容指标自动计算"""
        # 创建时不包含metrics，让它自动计算
        transformed = TransformedContent(
            title="指标测试",
            content="原始内容",
            html="<p>HTML内容</p>",
            text="这是测试文本内容包含多个单词",  # 确保有足够内容进行计算
        )

        # 检查基础字段
        assert transformed.title == "指标测试"
        assert transformed.content == "原始内容"
        assert transformed.html == "<p>HTML内容</p>"
        assert transformed.text == "这是测试文本内容包含多个单词"


class TestPublishTask:
    """发布任务模型测试"""

    def test_publish_task_creation(self):
        """测试发布任务创建"""
        content = Content(title="任务测试", content="任务内容")
        platforms = [Platform.ZHIHU, Platform.WEIBO]

        task = PublishTask(content_id=content.id, platforms=platforms)

        assert task.content_id == content.id
        assert task.platforms == platforms
        assert task.status == TaskStatus.PENDING
        assert task.scheduled_time is None
        assert isinstance(task.id, str)
        assert isinstance(task.created_at, datetime)

    def test_publish_task_platform_validation(self):
        """测试发布任务平台验证"""
        content = Content(title="验证测试", content="验证内容")

        # 空平台列表应该失败
        with pytest.raises(ValueError, match="至少需要选择一个发布平台"):
            PublishTask(content_id=content.id, platforms=[])

    def test_publish_task_platform_deduplication(self):
        """测试平台去重"""
        content = Content(title="去重测试", content="去重内容")
        platforms = [Platform.ZHIHU, Platform.ZHIHU, Platform.WEIBO]  # 重复的知乎

        task = PublishTask(content_id=content.id, platforms=platforms)

        # 应该自动去重
        assert len(task.platforms) == 2
        assert Platform.ZHIHU in task.platforms
        assert Platform.WEIBO in task.platforms

    def test_publish_task_with_schedule(self):
        """测试定时发布任务"""
        content = Content(title="定时测试", content="定时内容")
        scheduled_time = datetime.now()  # 使用datetime对象而不是timestamp

        task = PublishTask(
            content_id=content.id,
            platforms=[Platform.ZHIHU],
            scheduled_time=scheduled_time,
            publish_strategy=PublishStrategy.SCHEDULED,
        )

        assert task.scheduled_time == scheduled_time
        assert task.publish_strategy == PublishStrategy.SCHEDULED


class TestPublishRecord:
    """发布记录模型测试"""

    def test_publish_record_creation(self):
        """测试发布记录创建"""
        record = PublishRecord(
            task_id="task_123",
            platform=Platform.ZHIHU,
            platform_post_id="zhihu_456",
            status=PublishStatus.PUBLISHED,
        )

        assert record.task_id == "task_123"
        assert record.platform == Platform.ZHIHU
        assert record.platform_post_id == "zhihu_456"
        assert record.status == PublishStatus.PUBLISHED
        assert isinstance(record.id, str)
        assert isinstance(record.created_at, datetime)


class TestCredentialModels:
    """凭证模型测试"""

    def test_zhihu_credentials(self):
        """测试知乎凭证"""
        creds = ZhihuCredentials(
            access_token="zhihu_access_789", refresh_token="zhihu_refresh_000", user_id="user123"
        )

        assert creds.access_token == "zhihu_access_789"
        assert creds.refresh_token == "zhihu_refresh_000"
        assert creds.user_id == "user123"

    def test_weibo_credentials(self):
        """测试微博凭证"""
        creds = WeiboCredentials(access_token="weibo_access_789", uid="weibo_uid_123")

        assert creds.access_token == "weibo_access_789"
        assert creds.uid == "weibo_uid_123"


class TestEnums:
    """枚举测试"""

    def test_content_format_enum(self):
        """测试内容格式枚举"""
        assert ContentFormat.MARKDOWN == "markdown"
        assert ContentFormat.HTML == "html"
        assert ContentFormat.TEXT == "text"
        assert ContentFormat.PDF == "pdf"
        assert ContentFormat.DOCX == "docx"

    def test_platform_enum(self):
        """测试平台枚举"""
        assert Platform.ZHIHU == "zhihu"
        assert Platform.WEIBO == "weibo"
        assert Platform.XIAOHONGSHU == "xiaohongshu"
        assert Platform.TOUTIAO == "toutiao"

    def test_task_status_enum(self):
        """测试任务状态枚举"""
        assert TaskStatus.PENDING == "pending"
        assert TaskStatus.RUNNING == "running"
        assert TaskStatus.COMPLETED == "completed"
        assert TaskStatus.FAILED == "failed"
        assert TaskStatus.CANCELLED == "cancelled"

    def test_publish_status_enum(self):
        """测试发布状态枚举"""
        assert PublishStatus.PENDING == "pending"
        assert PublishStatus.PUBLISHING == "publishing"
        assert PublishStatus.PUBLISHED == "published"
        assert PublishStatus.FAILED == "failed"


if __name__ == "__main__":
    pytest.main([__file__])
