"""
TextUp 工具模块专项测试

本模块专门测试工具模块和异常处理，旨在提升测试覆盖率。
"""

import pytest
import asyncio
from datetime import datetime
from unittest.mock import Mock, patch

import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent / "src"))

from textup.utils.exceptions import (
    TextUpError,
    ConfigurationError,
    ContentError,
    AuthenticationError,
    NetworkError,
    RateLimitError,
    PlatformError,
    ContentValidationError,
    ContentFormatError,
    PlatformAPIError,
    InvalidCredentialsError,
    TokenExpiredError,
    PermissionDeniedError,
    ContentSizeError,
)

from textup.utils.interfaces import (
    ConfigManagerProtocol,
    ContentManagerProtocol,
    PlatformAdapterProtocol,
    PublishEngineProtocol,
    DatabaseManagerProtocol,
    LoggerProtocol,
)


class TestExceptionHierarchy:
    """测试异常层次结构"""

    def test_base_exception_creation(self):
        """测试基础异常创建"""
        error = TextUpError("测试错误")
        assert str(error) == "测试错误"
        assert error.error_code is None
        assert error.context == {}

    def test_base_exception_with_context(self):
        """测试带上下文的异常"""
        context = {"user": "test", "action": "publish"}
        error = TextUpError("操作失败", error_code="OP001", context=context)

        assert error.error_code == "OP001"
        assert error.context == context
        assert "测试" not in str(error.context)  # 上下文不直接显示在字符串中

    def test_configuration_error_inheritance(self):
        """测试配置错误继承"""
        error = ConfigurationError("配置文件无效")
        assert isinstance(error, TextUpError)
        assert str(error) == "配置文件无效"

    def test_content_error_inheritance(self):
        """测试内容错误继承"""
        error = ContentError("内容格式错误")
        assert isinstance(error, TextUpError)
        assert str(error) == "内容格式错误"

    def test_platform_api_error_inheritance(self):
        """测试平台API错误继承"""
        error = PlatformAPIError("zhihu", "API调用失败")
        assert isinstance(error, TextUpError)
        assert "API调用失败" in str(error)

    def test_authentication_error_inheritance(self):
        """测试认证错误继承"""
        error = AuthenticationError("认证失败")
        assert isinstance(error, TextUpError)
        assert str(error) == "认证失败"

    def test_network_error_inheritance(self):
        """测试网络错误继承"""
        error = NetworkError("网络连接失败")
        assert isinstance(error, TextUpError)
        assert str(error) == "网络连接失败"

    def test_content_validation_error_inheritance(self):
        """测试内容验证错误继承"""
        error = ContentValidationError("markdown", ["标题为空"])
        assert isinstance(error, TextUpError)
        assert "内容验证失败" in str(error)

    def test_platform_error_inheritance(self):
        """测试平台错误继承"""
        error = PlatformError("平台API错误", "zhihu")
        assert isinstance(error, TextUpError)
        assert str(error) == "平台API错误"

    def test_content_format_error_inheritance(self):
        """测试内容格式错误继承"""
        error = ContentFormatError("markdown", "html")
        assert isinstance(error, TextUpError)
        assert "格式转换失败" in str(error)

    def test_invalid_credentials_error_inheritance(self):
        """测试无效凭证错误继承"""
        error = InvalidCredentialsError("zhihu")
        assert isinstance(error, TextUpError)
        assert "无效的认证凭证" in str(error)

    def test_token_expired_error_inheritance(self):
        """测试令牌过期错误继承"""
        error = TokenExpiredError("weibo")
        assert isinstance(error, TextUpError)
        assert "访问令牌已过期" in str(error)

    def test_rate_limit_error_inheritance(self):
        """测试限流错误继承"""
        error = RateLimitError("zhihu", 60)
        assert isinstance(error, TextUpError)
        assert "请求频率过高" in str(error)

    def test_content_size_error_inheritance(self):
        """测试内容大小错误继承"""
        error = ContentSizeError(1000000, 500000)
        assert isinstance(error, TextUpError)
        assert "内容大小超出限制" in str(error)

    def test_permission_denied_error_inheritance(self):
        """测试权限拒绝错误继承"""
        error = PermissionDeniedError("zhihu", "publish")
        assert isinstance(error, TextUpError)
        assert "权限不足" in str(error)

    def test_exception_chaining(self):
        """测试异常链"""
        original_error = ValueError("原始错误")
        try:
            raise TextUpError("包装错误") from original_error
        except TextUpError as e:
            wrapped_error = e

        assert wrapped_error.__cause__ == original_error
        assert "原始错误" in str(original_error)

    def test_exception_context_serialization(self):
        """测试异常上下文序列化"""
        context = {
            "timestamp": datetime.now().isoformat(),
            "user_id": "user123",
            "operation": "publish",
            "platform": "zhihu",
        }
        error = TextUpError("操作失败", context=context)

        assert error.context["user_id"] == "user123"
        assert error.context["operation"] == "publish"
        assert "timestamp" in error.context

    def test_error_code_patterns(self):
        """测试错误代码模式"""
        config_error = ConfigurationError("配置错误", error_code="CFG001")
        content_error = ContentError("内容错误", error_code="CNT001")
        platform_error = PlatformError("平台错误", "zhihu", error_code="PLT001")

        assert config_error.error_code == "CFG001"
        assert content_error.error_code == "CNT001"
        assert platform_error.error_code == "PLT001"


class TestProtocolInterfaces:
    """测试协议接口"""

    def test_config_manager_protocol(self):
        """测试配置管理器协议"""
        # 检查协议是否定义了必需的方法
        assert hasattr(ConfigManagerProtocol, "__annotations__")

        # 创建一个符合协议的模拟对象
        mock_config_manager = Mock(spec=ConfigManagerProtocol)
        mock_config_manager.load_config = Mock()
        mock_config_manager.save_config = Mock()
        mock_config_manager.get_config_value = Mock()
        mock_config_manager.set_config_value = Mock()

        # 验证方法存在
        assert hasattr(mock_config_manager, "load_config")
        assert hasattr(mock_config_manager, "save_config")
        assert hasattr(mock_config_manager, "get_config_value")
        assert hasattr(mock_config_manager, "set_config_value")

    def test_content_manager_protocol(self):
        """测试内容管理器协议"""
        mock_content_manager = Mock(spec=ContentManagerProtocol)
        mock_content_manager.parse_content = Mock()
        mock_content_manager.validate_content = Mock()
        mock_content_manager.transform_content = Mock()

        assert hasattr(mock_content_manager, "parse_content")
        assert hasattr(mock_content_manager, "validate_content")
        assert hasattr(mock_content_manager, "transform_content")

    def test_adapter_protocol(self):
        """测试适配器协议"""
        mock_adapter = Mock(spec=PlatformAdapterProtocol)
        mock_adapter.authenticate = Mock()
        mock_adapter.publish = Mock()
        mock_adapter.validate_content = Mock()

        assert hasattr(mock_adapter, "authenticate")
        assert hasattr(mock_adapter, "publish")
        assert hasattr(mock_adapter, "validate_content")

    def test_publish_engine_protocol(self):
        """测试发布引擎协议"""
        mock_engine = Mock(spec=PublishEngineProtocol)
        mock_engine.execute_task = Mock()
        mock_engine.execute_parallel = Mock()
        mock_engine.get_task_status = Mock()

        assert hasattr(mock_engine, "execute_task")
        assert hasattr(mock_engine, "execute_parallel")
        assert hasattr(mock_engine, "get_task_status")

    def test_database_manager_protocol(self):
        """测试数据库管理器协议"""
        mock_db = Mock(spec=DatabaseManagerProtocol)
        mock_db.save_content = Mock()
        mock_db.get_content = Mock()
        mock_db.delete_content = Mock()

        assert hasattr(mock_db, "save_content")
        assert hasattr(mock_db, "get_content")
        assert hasattr(mock_db, "delete_content")

    def test_logger_protocol(self):
        """测试日志记录器协议"""
        mock_logger = Mock(spec=LoggerProtocol)
        mock_logger.info = Mock()
        mock_logger.error = Mock()
        mock_logger.debug = Mock()

        assert hasattr(mock_logger, "info")
        assert hasattr(mock_logger, "error")
        assert hasattr(mock_logger, "debug")


class TestExceptionHandling:
    """测试异常处理功能"""

    def test_error_message_formatting(self):
        """测试错误消息格式化"""
        error = TextUpError("基础错误消息")
        assert "基础错误消息" in str(error)

        error_with_code = TextUpError("错误消息", error_code="ERR001")
        error_str = str(error_with_code)
        assert "错误消息" in error_str

    def test_error_context_access(self):
        """测试错误上下文访问"""
        context = {"module": "test", "function": "test_function"}
        error = TextUpError("测试错误", context=context)

        assert error.context["module"] == "test"
        assert error.context["function"] == "test_function"

    def test_nested_exception_handling(self):
        """测试嵌套异常处理"""
        try:
            try:
                raise ValueError("内部错误")
            except ValueError as e:
                raise ConfigurationError("配置处理失败") from e
        except ConfigurationError as config_error:
            assert isinstance(config_error.__cause__, ValueError)
            assert "内部错误" in str(config_error.__cause__)

    def test_exception_with_none_values(self):
        """测试空值异常参数"""
        error = TextUpError(None)
        assert str(error) == "None"

        error_with_none_code = TextUpError("消息", error_code=None)
        assert error_with_none_code.error_code is None

    def test_exception_repr(self):
        """测试异常repr表示"""
        error = TextUpError("测试", error_code="TEST001")
        repr_str = repr(error)
        assert "TextUpError" in repr_str
        assert "测试" in repr_str


class TestUtilityFunctions:
    """测试工具函数"""

    def test_exception_categorization(self):
        """测试异常分类"""
        # 网络相关异常
        network_errors = [NetworkError("网络错误")]
        for error in network_errors:
            assert "network" in str(type(error)).lower() or isinstance(error, NetworkError)

        # 认证相关异常
        auth_error = AuthenticationError("认证失败")
        assert isinstance(auth_error, AuthenticationError)

        # 配置相关异常
        config_error = ConfigurationError("配置错误")
        assert isinstance(config_error, ConfigurationError)

    def test_error_severity_levels(self):
        """测试错误严重性级别"""
        # 根据错误类型判断严重性
        critical_errors = [PlatformAPIError("zhihu", "服务崩溃"), ContentError("数据丢失")]
        warning_errors = [RateLimitError("zhihu", 60), ContentSizeError(1000, 500)]

        for error in critical_errors:
            assert isinstance(error, TextUpError)

        for error in warning_errors:
            assert isinstance(error, TextUpError)

    def test_error_recovery_strategies(self):
        """测试错误恢复策略"""
        # 可重试的错误
        retryable_errors = [NetworkError("网络超时"), RateLimitError("zhihu", 60)]

        # 不可重试的错误
        non_retryable_errors = [
            AuthenticationError("认证失败"),
            PermissionDeniedError("zhihu", "publish"),
            ContentValidationError("markdown", ["格式错误"]),
        ]

        for error in retryable_errors:
            assert isinstance(error, TextUpError)
            # 这些错误通常可以重试

        for error in non_retryable_errors:
            assert isinstance(error, TextUpError)
            # 这些错误通常不应该重试

    def test_contextual_error_information(self):
        """测试上下文错误信息"""
        context = {
            "timestamp": "2024-01-01T12:00:00",
            "user": "test_user",
            "platform": "zhihu",
            "content_id": "article_123",
            "retry_count": 3,
        }

        error = PlatformAPIError("zhihu", "发布失败", context=context)

        assert error.context["user"] == "test_user"
        assert error.context["platform"] == "zhihu"
        assert error.context["retry_count"] == 3

    def test_error_logging_compatibility(self):
        """测试错误日志兼容性"""
        error = TextUpError("测试错误", error_code="LOG001", context={"test": True})

        # 验证错误可以被正确转换为字符串用于日志记录
        log_message = str(error)
        assert isinstance(log_message, str)
        assert len(log_message) > 0

        # 验证错误码和上下文可以访问
        assert error.error_code == "LOG001"
        assert error.context["test"] is True


class TestAsyncExceptionHandling:
    """测试异步异常处理"""

    @pytest.mark.asyncio
    async def test_async_exception_propagation(self):
        """测试异步异常传播"""

        async def failing_async_function():
            raise ContentError("异步内容错误")

        with pytest.raises(ContentError) as exc_info:
            await failing_async_function()

        assert "异步内容错误" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_async_exception_context(self):
        """测试异步异常上下文"""
        context = {"async_task": "content_processing", "step": 1}

        async def async_task_with_context():
            raise ContentFormatError("markdown", "html", context=context)

        with pytest.raises(ContentFormatError) as exc_info:
            await async_task_with_context()

        error = exc_info.value
        assert error.context["async_task"] == "content_processing"
        assert error.context["step"] == 1

    @pytest.mark.asyncio
    async def test_async_exception_chaining(self):
        """测试异步异常链"""

        async def inner_async_function():
            raise ValueError("异步内部错误")

        async def outer_async_function():
            try:
                await inner_async_function()
            except ValueError as e:
                raise PlatformAPIError("zhihu", "异步服务错误") from e

        with pytest.raises(PlatformAPIError) as exc_info:
            await outer_async_function()

        error = exc_info.value
        assert isinstance(error.__cause__, ValueError)
        assert "异步内部错误" in str(error.__cause__)


class TestProtocolCompliance:
    """测试协议遵循性"""

    def test_protocol_method_signatures(self):
        """测试协议方法签名"""
        # 这个测试确保协议定义是可用的
        assert ConfigManagerProtocol is not None
        assert ContentManagerProtocol is not None
        assert PlatformAdapterProtocol is not None
        assert PublishEngineProtocol is not None
        assert DatabaseManagerProtocol is not None
        assert LoggerProtocol is not None

    def test_mock_protocol_implementation(self):
        """测试模拟协议实现"""

        class MockConfigManager:
            async def load_config(self):
                return {}

            async def save_config(self, config):
                return True

            async def get_config_value(self, key):
                return None

            async def set_config_value(self, key, value):
                return True

        mock_manager = MockConfigManager()

        # 验证方法存在并可调用
        assert hasattr(mock_manager, "load_config")
        assert hasattr(mock_manager, "save_config")
        assert hasattr(mock_manager, "get_config_value")
        assert hasattr(mock_manager, "set_config_value")


if __name__ == "__main__":
    pytest.main([__file__])
