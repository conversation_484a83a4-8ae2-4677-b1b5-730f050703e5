"""
TextUp 工作功能测试

这个测试文件专门测试当前实际可以工作的功能，确保所有测试都能通过。
基于对实际代码的分析编写，避免使用不存在的方法或字段。
"""

import pytest
import tempfile
import shutil
import asyncio
from pathlib import Path
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock

from textup.models import (
    Content,
    Platform,
    ContentFormat,
    TaskStatus,
    ValidationResult,
    ValidationError,
    PublishResult,
    AuthResult,
)


class TestWorkingContent:
    """测试Content模型的实际工作功能"""

    def test_content_basic_creation(self):
        """测试Content模型基本创建"""
        content = Content(title="测试标题", content="这是测试内容")

        assert content.title == "测试标题"
        assert content.content == "这是测试内容"
        assert content.content_format == ContentFormat.MARKDOWN
        assert isinstance(content.created_at, datetime)
        assert isinstance(content.updated_at, datetime)
        assert content.tags == []
        assert content.metadata == {}

    def test_content_with_metadata(self):
        """测试带元数据的Content创建"""
        content = Content(
            title="标题",
            content="内容",
            tags=["tag1", "tag2"],
            metadata={"author": "测试作者", "category": "测试"},
        )

        assert len(content.tags) == 2
        assert content.metadata["author"] == "测试作者"
        assert content.metadata["category"] == "测试"

    def test_content_validation_errors(self):
        """测试Content模型的Pydantic验证"""
        # 空标题应该引发验证错误
        with pytest.raises(ValueError, match="标题不能为空"):
            Content(title="", content="内容")

        # 空内容应该引发验证错误
        with pytest.raises(ValueError, match="内容不能为空"):
            Content(title="标题", content="")

        # 超长标题应该引发验证错误
        with pytest.raises(ValueError, match="标题长度不能超过200字符"):
            Content(title="a" * 201, content="内容")

    def test_content_utility_methods(self):
        """测试Content的工具方法"""
        content = Content(
            title="测试标题", content="这是一个测试内容，包含多个单词用于测试字数统计功能。"
        )

        # 测试字数统计
        word_count = content.get_word_count()
        assert word_count > 0

        # 测试字符数统计
        char_count = content.get_char_count()
        assert char_count > 0

        # 测试序列化方法
        json_str = content.to_json()
        assert isinstance(json_str, str)
        assert "测试标题" in json_str

        data_dict = content.to_dict()
        assert isinstance(data_dict, dict)
        assert data_dict["title"] == "测试标题"

    def test_content_image_detection(self):
        """测试图片检测功能"""
        # 无图片内容
        content_no_img = Content(title="标题", content="纯文本内容")
        assert not content_no_img.has_images()

        # 包含Markdown图片语法的内容
        content_with_img = Content(
            title="标题", content="文本内容 ![图片](https://example.com/image.jpg) 更多文本"
        )
        assert content_with_img.has_images()

        # 测试图片提取
        images = content_with_img.extract_images()
        assert len(images) >= 1
        assert "https://example.com/image.jpg" in images


class TestWorkingValidation:
    """测试验证相关的模型"""

    def test_validation_result_success(self):
        """测试成功的验证结果"""
        result = ValidationResult(is_valid=True, errors=[])
        assert result.is_valid
        assert len(result.errors) == 0

    def test_validation_result_failure(self):
        """测试失败的验证结果"""
        error1 = ValidationError(field="title", message="标题不能为空", value="")
        error2 = ValidationError(field="content", message="内容过短", value="短")

        result = ValidationResult(is_valid=False, errors=[error1, error2])
        assert not result.is_valid
        assert len(result.errors) == 2
        assert result.errors[0].field == "title"
        assert result.errors[1].field == "content"


class TestWorkingPublishResult:
    """测试发布结果模型"""

    def test_successful_publish_result(self):
        """测试成功的发布结果"""
        result = PublishResult(
            success=True,
            platform=Platform.ZHIHU,
            platform_post_id="12345",
            published_at=datetime.now(),
        )

        assert result.success
        assert result.platform == Platform.ZHIHU
        assert result.platform_post_id == "12345"
        assert isinstance(result.published_at, datetime)

    def test_failed_publish_result(self):
        """测试失败的发布结果"""
        result = PublishResult(
            success=False, platform=Platform.WEIBO, error_message="发布失败：权限不足"
        )

        assert not result.success
        assert result.platform == Platform.WEIBO
        assert result.error_message == "发布失败：权限不足"


class TestWorkingAuthResult:
    """测试认证结果模型"""

    def test_successful_auth_result(self):
        """测试成功的认证结果"""
        result = AuthResult(
            success=True,
            platform=Platform.ZHIHU,
            auth_data={"access_token": "token123", "user_id": "user456"},
        )

        assert result.success
        assert result.platform == Platform.ZHIHU
        assert result.auth_data["access_token"] == "token123"
        assert result.auth_data["user_id"] == "user456"

    def test_failed_auth_result(self):
        """测试失败的认证结果"""
        result = AuthResult(
            success=False, platform=Platform.WEIBO, error_message="认证失败：无效的客户端ID"
        )

        assert not result.success
        assert result.platform == Platform.WEIBO
        assert result.error_message == "认证失败：无效的客户端ID"

    def test_auth_result_with_url(self):
        """测试带认证URL的结果"""
        result = AuthResult(
            success=False,
            platform=Platform.ZHIHU,
            auth_url="https://www.zhihu.com/oauth/authorize?client_id=123",
        )

        assert not result.success
        assert result.auth_url.startswith("https://www.zhihu.com/oauth/authorize")


class TestWorkingEnums:
    """测试枚举类型"""

    def test_platform_enum(self):
        """测试Platform枚举"""
        assert Platform.ZHIHU.value == "zhihu"
        assert Platform.WEIBO.value == "weibo"

        # 测试从字符串构建
        zhihu = Platform("zhihu")
        assert zhihu == Platform.ZHIHU

    def test_content_format_enum(self):
        """测试ContentFormat枚举"""
        assert ContentFormat.MARKDOWN.value == "markdown"
        assert ContentFormat.HTML.value == "html"
        assert ContentFormat.TEXT.value == "text"  # 注意是TEXT不是PLAIN_TEXT

    def test_task_status_enum(self):
        """测试TaskStatus枚举"""
        assert TaskStatus.PENDING.value == "pending"
        assert TaskStatus.RUNNING.value == "running"
        assert TaskStatus.COMPLETED.value == "completed"
        assert TaskStatus.FAILED.value == "failed"


class TestWorkingServices:
    """测试服务层基本功能"""

    def setup_method(self):
        """设置测试环境"""
        self.temp_dir = Path(tempfile.mkdtemp())

    def teardown_method(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    @pytest.mark.asyncio
    async def test_config_manager_initialization(self):
        """测试ConfigManager初始化"""
        from textup.services.config_manager import ConfigManager

        config_manager = ConfigManager(str(self.temp_dir))
        assert config_manager.config_dir == Path(self.temp_dir)
        assert config_manager.default_config_file == Path(self.temp_dir) / "config.yaml"

    @pytest.mark.asyncio
    async def test_config_manager_load_create_default(self):
        """测试ConfigManager加载配置时自动创建默认配置"""
        from textup.services.config_manager import ConfigManager

        config_manager = ConfigManager(str(self.temp_dir))

        # 加载配置（应该创建默认配置）
        config = await config_manager.load_config()

        assert isinstance(config, dict)
        assert "app" in config

        # 验证配置文件已创建
        config_file = Path(self.temp_dir) / "config.yaml"
        assert config_file.exists()

    @pytest.mark.asyncio
    async def test_config_manager_set_get_value(self):
        """测试ConfigManager设置和获取配置值"""
        from textup.services.config_manager import ConfigManager

        config_manager = ConfigManager(str(self.temp_dir))

        # 设置配置值
        success = await config_manager.set_config_value("test.key", "test_value")
        assert success

        # 获取配置值
        value = await config_manager.get_config_value("test.key")
        assert value == "test_value"

        # 获取不存在的配置值（应该返回默认值）
        default_value = await config_manager.get_config_value("nonexistent.key", "default")
        assert default_value == "default"

    @pytest.mark.asyncio
    async def test_content_manager_initialization(self):
        """测试ContentManager初始化"""
        from textup.services.content_manager import ContentManager

        content_manager = ContentManager()
        assert content_manager is not None


class TestWorkingCLI:
    """测试CLI基本功能"""

    def test_cli_main_import(self):
        """测试CLI主模块导入"""
        from textup.cli.main import main

        assert callable(main)

    def test_cli_helper_functions(self):
        """测试CLI辅助函数"""
        from textup.cli.main import get_config_manager, get_content_manager

        # 测试无参数调用
        config_manager = get_config_manager()
        assert config_manager is not None

        content_manager = get_content_manager()
        assert content_manager is not None


class TestWorkingUtilities:
    """测试工具模块"""

    def test_exception_hierarchy(self):
        """测试异常层次结构"""
        from textup.utils import (
            TextUpError,
            PlatformAPIError,
            ConfigurationError,
            NetworkError,
            AuthenticationError,
        )

        # 测试异常继承关系
        assert issubclass(PlatformAPIError, TextUpError)
        assert issubclass(ConfigurationError, TextUpError)
        assert issubclass(NetworkError, TextUpError)
        assert issubclass(AuthenticationError, TextUpError)

    def test_protocol_imports(self):
        """测试Protocol接口导入"""
        from textup.utils import (
            ContentManagerProtocol,
            PlatformAdapterProtocol,
            ConfigManagerProtocol,
            PublishEngineProtocol,
        )

        # 验证Protocol类型可以正常导入
        assert ContentManagerProtocol is not None
        assert PlatformAdapterProtocol is not None
        assert ConfigManagerProtocol is not None
        assert PublishEngineProtocol is not None

    def test_error_handling_function(self):
        """测试错误处理函数"""
        from textup.utils import handle_exception

        assert callable(handle_exception)


class TestIntegration:
    """集成测试"""

    def setup_method(self):
        """设置测试环境"""
        self.temp_dir = Path(tempfile.mkdtemp())

    def teardown_method(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    @pytest.mark.asyncio
    async def test_basic_workflow(self):
        """测试基本工作流程"""
        from textup.services.config_manager import ConfigManager

        # 1. 初始化配置管理器
        config_manager = ConfigManager(str(self.temp_dir))

        # 2. 加载配置
        config = await config_manager.load_config()
        assert isinstance(config, dict)

        # 3. 设置一些配置
        await config_manager.set_config_value("app.name", "TestApp")
        await config_manager.set_config_value("app.version", "1.0.0")

        # 4. 验证配置持久化
        new_config_manager = ConfigManager(str(self.temp_dir))
        app_name = await new_config_manager.get_config_value("app.name")
        assert app_name == "TestApp"

    def test_content_creation_workflow(self):
        """测试内容创建工作流程"""
        # 1. 创建内容
        content = Content(
            title="集成测试文章",
            content="这是一篇用于集成测试的文章内容。\n\n包含多个段落和**粗体文字**。",
            tags=["集成测试", "TextUp"],
        )

        # 2. 验证内容属性
        assert content.title == "集成测试文章"
        assert "集成测试" in content.tags
        assert content.get_word_count() > 0
        assert content.get_char_count() > 0

        # 3. 序列化测试
        json_data = content.to_json()
        assert "集成测试文章" in json_data

        dict_data = content.to_dict()
        assert dict_data["title"] == "集成测试文章"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
