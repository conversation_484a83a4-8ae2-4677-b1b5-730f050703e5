#!/bin/bash

# 知乎平台发布测试脚本
# TextUp Zhihu Publishing Test Script
# 用于测试通过命令行发布文章到知乎平台

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_ARTICLE="$SCRIPT_DIR/../../data/test-zhihu-article.md"
VENV_PATH="$SCRIPT_DIR/../../../.venv"

# 打印函数
print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════════════╗"
    echo "║                                                                      ║"
    echo "║                    🧪 知乎平台发布测试 🧪                            ║"
    echo "║                                                                      ║"
    echo "║                   TextUp - 多平台发布工具                             ║"
    echo "║                                                                      ║"
    echo "╚══════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo
}

print_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 检查Python环境
check_python() {
    print_step "检查Python环境..."

    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version 2>&1 | cut -d" " -f2)
        print_info "找到Python版本: $PYTHON_VERSION"

        # 检查版本是否满足要求 (>=3.9)
        if python3 -c "import sys; sys.exit(0 if sys.version_info >= (3, 9) else 1)"; then
            print_success "Python版本满足要求 (>=3.9)"
        else
            print_error "Python版本过低，需要3.9或更高版本"
            exit 1
        fi
    else
        print_error "未找到Python3，请先安装Python"
        exit 1
    fi
}

# 检查uv包管理器
check_uv() {
    print_step "检查uv包管理器..."

    if command -v uv &> /dev/null; then
        UV_VERSION=$(uv --version 2>&1)
        print_info "找到uv: $UV_VERSION"
    else
        print_warning "未找到uv，正在安装..."
        curl -LsSf https://astral.sh/uv/install.sh | sh
        export PATH="$HOME/.cargo/bin:$PATH"
        if command -v uv &> /dev/null; then
            print_success "uv安装成功"
        else
            print_error "uv安装失败"
            exit 1
        fi
    fi
}

# 设置虚拟环境
setup_venv() {
    print_step "设置虚拟环境..."

    if [ ! -d "$VENV_PATH" ]; then
        print_info "创建虚拟环境..."
        uv venv "$VENV_PATH"
    else
        print_info "虚拟环境已存在"
    fi

    # 激活虚拟环境
    source "$VENV_PATH/bin/activate"
    print_success "虚拟环境已激活"
}

# 安装依赖
install_dependencies() {
    print_step "安装项目依赖..."

    # 安装项目
    uv pip install -e .

    # 安装Playwright浏览器
    print_info "安装Playwright浏览器..."
    playwright install chromium

    print_success "依赖安装完成"
}

# 检查测试文章
check_test_article() {
    print_step "检查测试文章..."

    if [ ! -f "$TEST_ARTICLE" ]; then
        print_error "测试文章文件不存在: $TEST_ARTICLE"
        print_info "请确保../../../tests/data/test-zhihu-article.md文件存在"
        exit 1
    else
        print_info "找到测试文章: $(basename "$TEST_ARTICLE")"

        # 显示文章基本信息
        TITLE=$(head -10 "$TEST_ARTICLE" | grep -E "^title:" | cut -d'"' -f2 || echo "未知标题")
        print_info "文章标题: $TITLE"

        WORD_COUNT=$(wc -w < "$TEST_ARTICLE")
        print_info "文章字数: $WORD_COUNT 词"

        print_success "测试文章检查完成"
    fi
}

# 检查TextUp安装
check_textup() {
    print_step "检查TextUp安装..."

    if command -v textup &> /dev/null; then
        TEXTUP_VERSION=$(textup --version 2>&1 || echo "版本信息不可用")
        print_info "TextUp版本: $TEXTUP_VERSION"
        print_success "TextUp已正确安装"
    else
        print_error "TextUp命令不可用，请检查安装"
        exit 1
    fi
}

# 检查配置状态
check_configuration() {
    print_step "检查配置状态..."

    echo
    print_info "当前配置信息："
    textup config --list || {
        print_warning "配置信息不可用，可能需要初始化"
        return 1
    }

    echo
    print_info "平台认证状态："
    textup auth --status || {
        print_warning "认证状态不可用"
        return 1
    }
}

# 预览测试
preview_test() {
    print_step "预览测试 (不实际发布)..."

    echo
    print_info "执行预览命令："
    echo "textup publish \"$TEST_ARTICLE\" --platform zhihu --dry-run"
    echo

    textup publish "$TEST_ARTICLE" --platform zhihu --dry-run || {
        print_error "预览测试失败"
        return 1
    }

    print_success "预览测试完成"
}

# 实际发布测试
publish_test() {
    print_step "实际发布测试..."

    echo
    print_warning "⚠️  注意：这将实际发布文章到知乎平台！"
    echo
    read -p "确定要继续吗？(y/N): " confirm

    if [[ $confirm =~ ^[Yy]$ ]]; then
        print_info "执行发布命令："
        echo "textup publish \"$TEST_ARTICLE\" --platform zhihu"
        echo

        textup publish "$TEST_ARTICLE" --platform zhihu || {
            print_error "发布测试失败"
            return 1
        }

        print_success "🎉 发布测试完成！"
        print_info "请检查知乎平台确认文章是否成功发布"
    else
        print_info "跳过实际发布测试"
    fi
}

# 显示帮助信息
show_help() {
    echo
    echo -e "${PURPLE}使用说明：${NC}"
    echo "本脚本用于测试TextUp知乎平台发布功能"
    echo
    echo "测试流程："
    echo "1. 环境检查 (Python, uv)"
    echo "2. 依赖安装 (TextUp, Playwright)"
    echo "3. 配置检查"
    echo "4. 预览测试 (dry-run)"
    echo "5. 实际发布测试 (可选)"
    echo
    echo "测试前请确保："
    echo "- 已配置知乎平台认证信息"
    echo "- 网络连接正常"
    echo "- 有效的知乎账号"
    echo
    echo "命令行参数："
    echo "  --help, -h     显示帮助信息"
    echo "  --preview-only 仅执行预览测试"
    echo "  --skip-install 跳过依赖安装"
    echo
}

# 主函数
main() {
    # 解析命令行参数
    PREVIEW_ONLY=false
    SKIP_INSTALL=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_help
                exit 0
                ;;
            --preview-only)
                PREVIEW_ONLY=true
                shift
                ;;
            --skip-install)
                SKIP_INSTALL=true
                shift
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    print_header

    # 执行测试步骤
    check_python

    if [ "$SKIP_INSTALL" = false ]; then
        check_uv
        setup_venv
        install_dependencies
    else
        print_info "跳过依赖安装"
        if [ -d "$VENV_PATH" ]; then
            source "$VENV_PATH/bin/activate"
            print_info "激活现有虚拟环境"
        fi
    fi

    check_test_article
    check_textup

    echo
    print_step "开始测试流程..."

    # 配置检查
    if check_configuration; then
        print_success "配置检查通过"
    else
        print_warning "配置可能不完整，但继续测试..."
    fi

    echo
    # 预览测试
    if preview_test; then
        print_success "预览测试通过"
    else
        print_error "预览测试失败，请检查配置和网络"
        exit 1
    fi

    echo
    # 实际发布测试
    if [ "$PREVIEW_ONLY" = false ]; then
        publish_test
    else
        print_info "仅执行预览测试，跳过实际发布"
    fi

    echo
    print_success "🎊 测试流程完成！"

    echo
    print_info "其他有用的命令："
    echo "textup config --list          # 查看配置"
    echo "textup auth --status          # 查看认证状态"
    echo "textup publish --help         # 查看发布帮助"
    echo "textup --debug publish ...    # 调试模式发布"
}

# 错误处理
trap 'print_error "脚本执行中断"; exit 1' INT TERM

# 执行主函数
main "$@"
