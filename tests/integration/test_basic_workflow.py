"""
TextUp 基本工作流程集成测试

测试核心功能的端到端流程。
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from textup.models import (
    Content,
    Platform,
    TaskStatus,
    PublishStatus,
    ContentFormat,
    ValidationResult,
    PublishResult,
)
from textup.services.config_manager import ConfigManager
from textup.services.content_manager import ContentManager
from textup.services.publish_engine import PublishEngineManager


class TestBasicWorkflow:
    """测试基本工作流程"""

    def setup_method(self):
        """设置测试环境"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.config_dir = self.temp_dir / "config"
        self.content_dir = self.temp_dir / "content"
        self.config_dir.mkdir(parents=True)
        self.content_dir.mkdir(parents=True)

    def teardown_method(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    @pytest.mark.asyncio
    async def test_config_manager_workflow(self):
        """测试配置管理器工作流程"""
        config_manager = ConfigManager(str(self.config_dir))

        # 测试加载配置（应该创建默认配置）
        config = await config_manager.load_config()
        assert isinstance(config, dict)
        assert "app" in config

        # 测试设置配置值
        success = await config_manager.set_config_value("app.name", "Test App")
        assert success

        # 测试获取配置值
        app_name = await config_manager.get_config_value("app.name")
        assert app_name == "Test App"

        # 测试应用配置
        app_config = await config_manager.get_app_config()
        assert app_config is not None
        assert app_config.name == "Test App"

    @pytest.mark.asyncio
    async def test_content_manager_workflow(self):
        """测试内容管理器工作流程"""
        content_manager = ContentManager()

        # 创建测试Markdown文件
        test_md_file = self.content_dir / "test.md"
        test_md_content = """---
title: 测试文章标题
author: 测试作者
tags:
  - 测试
  - 单元测试
---

# 测试文章标题

这是一个测试文章的内容。

## 子标题

包含一些**粗体文字**和*斜体文字*。

- 列表项1
- 列表项2
- 列表项3

```python
# 代码块示例
def hello_world():
    print("Hello, World!")
```

结束内容。
"""
        test_md_file.write_text(test_md_content, encoding="utf-8")

        # 测试加载内容
        content = await content_manager.load_content(str(test_md_file))
        assert isinstance(content, Content)
        assert content.title == "测试文章标题"
        assert content.author == "测试作者"
        assert "测试" in content.tags
        assert content.format == ContentFormat.MARKDOWN

        # 测试验证内容
        validation = await content_manager.validate_content(content)
        assert isinstance(validation, ValidationResult)
        assert validation.is_valid

        # 测试转换内容为HTML
        transformed = await content_manager.transform_content(content, Platform.ZHIHU, {})
        assert transformed is not None
        assert transformed.title == content.title
        assert transformed.html is not None
        assert "<h1>" in transformed.html or "<h2>" in transformed.html

    @pytest.mark.asyncio
    async def test_markdown_parsing(self):
        """测试Markdown解析功能"""
        content_manager = ContentManager()

        # 测试基本Markdown
        basic_md = self.content_dir / "basic.md"
        basic_md.write_text(
            """# 标题

普通段落文本。

## 二级标题

包含**粗体**和*斜体*文本。
""",
            encoding="utf-8",
        )

        content = await content_manager.load_content(str(basic_md))
        assert content.title == "标题"
        assert "普通段落文本" in content.content

        # 测试带Front Matter的Markdown
        frontmatter_md = self.content_dir / "frontmatter.md"
        frontmatter_md.write_text(
            """---
title: 自定义标题
description: 文章描述
published: true
---

# Markdown标题

内容正文。
""",
            encoding="utf-8",
        )

        content = await content_manager.load_content(str(frontmatter_md))
        assert content.title == "自定义标题"
        assert content.description == "文章描述"

    @pytest.mark.asyncio
    async def test_content_validation_workflow(self):
        """测试内容验证工作流程"""
        content_manager = ContentManager()

        # 测试有效内容
        valid_content = Content(
            title="有效标题",
            content="这是有效的内容，长度合适。",
            format=ContentFormat.MARKDOWN,
            tags=["标签1", "标签2"],
        )

        validation = await content_manager.validate_content(valid_content)
        assert validation.is_valid
        assert len(validation.errors) == 0

        # 测试无效内容（标题过长）
        invalid_content = Content(
            title="a" * 101, content="内容", format=ContentFormat.MARKDOWN  # 超过100字符限制
        )

        validation = await content_manager.validate_content(invalid_content)
        assert not validation.is_valid
        assert len(validation.errors) > 0

    @pytest.mark.asyncio
    async def test_platform_transformation(self):
        """测试平台内容转换"""
        content_manager = ContentManager()

        test_content = Content(
            title="测试文章",
            content="""# 测试标题

这是一个测试内容。

## 子标题

包含**粗体**文字。

```python
def test():
    pass
```

结束。
""",
            format=ContentFormat.MARKDOWN,
        )

        # 测试知乎平台转换
        zhihu_transformed = await content_manager.transform_content(
            test_content, Platform.ZHIHU, {}
        )
        assert zhihu_transformed is not None
        assert zhihu_transformed.platform == Platform.ZHIHU
        assert zhihu_transformed.html is not None

        # 测试微博平台转换
        weibo_transformed = await content_manager.transform_content(
            test_content, Platform.WEIBO, {}
        )
        assert weibo_transformed is not None
        assert weibo_transformed.platform == Platform.WEIBO

    @pytest.mark.asyncio
    @patch("textup.adapters.zhihu.ZhihuAdapter")
    @patch("textup.adapters.weibo.WeiboAdapter")
    async def test_publish_engine_workflow(self, mock_weibo_adapter, mock_zhihu_adapter):
        """测试发布引擎工作流程"""
        # Mock适配器行为
        mock_zhihu_instance = Mock()
        mock_zhihu_adapter.return_value = mock_zhihu_instance
        mock_zhihu_instance.authenticate = AsyncMock(return_value=Mock(success=True))
        mock_zhihu_instance.publish = AsyncMock(
            return_value=PublishResult(
                success=True,
                platform=Platform.ZHIHU,
                platform_post_id="zhihu_123",
                platform_url="https://zhihu.com/answer/123",
                publish_time=datetime.now(),
                message="发布成功",
            )
        )

        mock_weibo_instance = Mock()
        mock_weibo_adapter.return_value = mock_weibo_instance
        mock_weibo_instance.authenticate = AsyncMock(return_value=Mock(success=True))
        mock_weibo_instance.publish = AsyncMock(
            return_value=PublishResult(
                success=True,
                platform=Platform.WEIBO,
                platform_post_id="weibo_456",
                platform_url="https://weibo.com/status/456",
                publish_time=datetime.now(),
                message="发布成功",
            )
        )

        # 创建发布引擎
        config_manager = ConfigManager(str(self.config_dir))
        content_manager = ContentManager()

        with patch("textup.services.publish_engine.ConfigManager", return_value=config_manager):
            with patch(
                "textup.services.publish_engine.ContentManager", return_value=content_manager
            ):
                publish_engine = PublishEngineManager(
                    config_manager=config_manager, content_manager=content_manager
                )

                # 创建测试内容
                test_content = Content(
                    title="测试发布",
                    content="这是一个测试发布的内容。",
                    format=ContentFormat.MARKDOWN,
                )

                # 测试发布到知乎
                result = await publish_engine.publish_to_platform(
                    content=test_content,
                    platform=Platform.ZHIHU,
                    platform_options={},
                    credentials={
                        "client_id": "test",
                        "client_secret": "test",
                        "redirect_uri": "test",
                    },
                )

                assert result.success
                assert result.platform == Platform.ZHIHU
                assert result.platform_post_id == "zhihu_123"

    @pytest.mark.asyncio
    async def test_error_handling_workflow(self):
        """测试错误处理工作流程"""
        content_manager = ContentManager()

        # 测试文件不存在的情况
        with pytest.raises(FileNotFoundError):
            await content_manager.load_content("/nonexistent/file.md")

        # 测试无效内容格式
        invalid_file = self.content_dir / "invalid.txt"
        invalid_file.write_text("Not markdown content", encoding="utf-8")

        with pytest.raises(ValueError):
            await content_manager.load_content(str(invalid_file))

    @pytest.mark.asyncio
    async def test_configuration_persistence(self):
        """测试配置持久化"""
        config_manager = ConfigManager(str(self.config_dir))

        # 设置一些配置
        await config_manager.set_config_value("app.name", "TestApp")
        await config_manager.set_config_value("app.version", "1.0.0")
        await config_manager.set_config_value("app.debug", True)

        # 创建新的配置管理器实例
        new_config_manager = ConfigManager(str(self.config_dir))

        # 验证配置被正确持久化
        app_name = await new_config_manager.get_config_value("app.name")
        app_version = await new_config_manager.get_config_value("app.version")
        debug_mode = await new_config_manager.get_config_value("app.debug")

        assert app_name == "TestApp"
        assert app_version == "1.0.0"
        assert debug_mode is True

    @pytest.mark.asyncio
    async def test_content_metrics_calculation(self):
        """测试内容指标计算"""
        content_manager = ContentManager()

        test_content = Content(
            title="测试文章",
            content="""这是一篇测试文章。

包含多个段落的内容，用于测试字数统计和其他指标的计算。

第二个段落包含更多文字，以便获得更准确的统计数据。

最后一个段落用于结束测试。""",
            format=ContentFormat.MARKDOWN,
        )

        transformed = await content_manager.transform_content(test_content, Platform.ZHIHU, {})

        # 验证指标计算
        assert transformed.metrics is not None
        assert transformed.metrics.word_count > 0
        assert transformed.metrics.char_count > 0
        assert transformed.metrics.paragraph_count > 0
        assert transformed.metrics.estimated_read_time > 0

    @pytest.mark.asyncio
    async def test_backup_and_recovery(self):
        """测试备份和恢复功能"""
        config_manager = ConfigManager(str(self.config_dir))

        # 设置初始配置
        await config_manager.set_config_value("app.name", "BackupTest")
        await config_manager.set_config_value("app.backup_enabled", True)

        # 创建备份
        backup_success = await config_manager.backup_config()
        assert backup_success

        # 验证备份文件存在
        backup_dir = Path(self.config_dir) / "backups"
        assert backup_dir.exists()
        backup_files = list(backup_dir.glob("config_*.yaml"))
        assert len(backup_files) > 0

        # 修改配置
        await config_manager.set_config_value("app.name", "Modified")

        # 验证配置已修改
        modified_name = await config_manager.get_config_value("app.name")
        assert modified_name == "Modified"


if __name__ == "__main__":
    pytest.main([__file__])
