"""
TextUp ContentManager 实际功能测试

基于ContentManager的实际实现编写的测试用例，确保测试与代码实现完全匹配。
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from textup.models import Content, Platform, ContentFormat, ValidationResult, TransformedContent
from textup.services.content_manager import ContentManager


class TestContentManagerReal:
    """测试ContentManager实际功能"""

    def setup_method(self):
        """设置测试环境"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.content_manager = ContentManager()

    def teardown_method(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_content_manager_initialization(self):
        """测试ContentManager初始化"""
        cm = ContentManager()
        assert cm is not None
        assert hasattr(cm, "max_file_size")
        assert hasattr(cm, "supported_extensions")
        assert hasattr(cm, "markdown_parser")

        # 检查实际方法
        assert hasattr(cm, "parse_content")
        assert hasattr(cm, "validate_content")
        assert hasattr(cm, "transform_content")

    def test_content_manager_custom_initialization(self):
        """测试ContentManager自定义初始化"""
        custom_extensions = [".md", ".txt"]
        cm = ContentManager(
            max_file_size=5 * 1024 * 1024, supported_extensions=custom_extensions  # 5MB
        )

        assert cm.max_file_size == 5 * 1024 * 1024
        assert cm.supported_extensions == custom_extensions

    def test_detect_content_format(self):
        """测试内容格式检测"""
        cm = ContentManager()

        assert cm._detect_content_format(".md") == ContentFormat.MARKDOWN
        assert cm._detect_content_format(".markdown") == ContentFormat.MARKDOWN
        assert cm._detect_content_format(".html") == ContentFormat.HTML
        assert cm._detect_content_format(".htm") == ContentFormat.HTML
        assert cm._detect_content_format(".txt") == ContentFormat.TEXT

    @pytest.mark.asyncio
    async def test_parse_content_markdown_file(self):
        """测试解析Markdown文件"""
        # 创建测试Markdown文件
        test_file = self.temp_dir / "test.md"
        markdown_content = """---
title: 测试标题
author: 测试作者
tags: [测试, 解析]
---

# 测试标题

这是一个测试内容。

## 子标题

包含**粗体**文字。

```python
print("代码块")
```

![图片](https://example.com/image.jpg)
"""
        test_file.write_text(markdown_content, encoding="utf-8")

        # 解析文件
        content = await self.content_manager.parse_content(str(test_file))

        assert isinstance(content, Content)
        assert content.title == "测试标题"
        assert content.content_format == ContentFormat.MARKDOWN
        assert "这是一个测试内容" in content.content

        # 检查元数据
        if content.metadata:
            assert content.metadata.get("author") == "测试作者"

    @pytest.mark.asyncio
    async def test_parse_content_simple_markdown(self):
        """测试解析简单Markdown文件"""
        test_file = self.temp_dir / "simple.md"
        simple_content = """# 简单标题

这是简单的内容。
"""
        test_file.write_text(simple_content, encoding="utf-8")

        content = await self.content_manager.parse_content(str(test_file))

        assert isinstance(content, Content)
        assert content.title == "简单标题"
        assert "这是简单的内容" in content.content

    @pytest.mark.asyncio
    async def test_parse_content_text_file(self):
        """测试解析文本文件"""
        test_file = self.temp_dir / "test.txt"
        text_content = """这是一个纯文本文件。

包含多行内容。
第二行内容。
"""
        test_file.write_text(text_content, encoding="utf-8")

        content = await self.content_manager.parse_content(str(test_file))

        assert isinstance(content, Content)
        assert content.content_format == ContentFormat.TEXT
        assert "这是一个纯文本文件" in content.content

    @pytest.mark.asyncio
    async def test_parse_content_file_not_found(self):
        """测试解析不存在的文件"""
        nonexistent_file = str(self.temp_dir / "nonexistent.md")

        with pytest.raises(FileNotFoundError):
            await self.content_manager.parse_content(nonexistent_file)

    @pytest.mark.asyncio
    async def test_parse_content_large_file(self):
        """测试解析超大文件"""
        large_file = self.temp_dir / "large.md"

        # 创建一个大文件（超过默认限制）
        large_content = "# 大文件\n\n" + "这是很长的内容。" * 500000  # 约10MB+
        large_file.write_text(large_content, encoding="utf-8")

        # 应该抛出ContentError
        from textup.utils import ContentError

        with pytest.raises(ContentError, match="文件大小超出限制"):
            await self.content_manager.parse_content(str(large_file))

    def test_extract_title_from_content(self):
        """测试从内容提取标题"""
        cm = ContentManager()

        # 测试有标题的内容
        content_with_title = "# 主标题\n\n这是内容。"
        title = cm._extract_title_from_content(content_with_title)
        assert title == "主标题"

        # 测试无标题的内容
        content_no_title = "这是没有标题的内容。"
        title = cm._extract_title_from_content(content_no_title)
        assert title == "无标题"

        # 测试多级标题
        content_multi_title = "## 二级标题\n\n# 一级标题\n\n内容"
        title = cm._extract_title_from_content(content_multi_title)
        assert title == "二级标题"  # 应该取第一个标题

    def test_extract_html_title(self):
        """测试从HTML提取标题"""
        cm = ContentManager()

        # 测试有title标签的HTML
        html_with_title = "<html><head><title>HTML标题</title></head><body>内容</body></html>"
        title = cm._extract_html_title(html_with_title)
        assert title == "HTML标题"

        # 测试有h1标签的HTML
        html_with_h1 = "<html><body><h1>H1标题</h1><p>内容</p></body></html>"
        title = cm._extract_html_title(html_with_h1)
        assert title == "H1标题"

        # 测试无标题的HTML
        html_no_title = "<html><body><p>没有标题的内容</p></body></html>"
        title = cm._extract_html_title(html_no_title)
        assert title == "无标题"

    @pytest.mark.asyncio
    async def test_validate_content_valid(self):
        """测试验证有效内容"""
        valid_content = Content(title="有效标题", content="这是有效的内容，长度合适，信息完整。")

        result = await self.content_manager.validate_content(valid_content)

        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert len(result.errors) == 0

    @pytest.mark.asyncio
    async def test_validate_content_with_issues(self):
        """测试验证有问题的内容"""
        # 创建一个可能有验证问题的内容
        content_with_issues = Content(title="测试", content="短内容")  # 可能被认为太短

        result = await self.content_manager.validate_content(content_with_issues)

        assert isinstance(result, ValidationResult)
        # 结果可能是有效的也可能无效，取决于实际验证规则
        assert isinstance(result.is_valid, bool)
        assert isinstance(result.errors, list)

    @pytest.mark.asyncio
    async def test_transform_content_basic(self):
        """测试基本内容转换"""
        test_content = Content(
            title="转换测试",
            content="# 标题\n\n这是内容。\n\n**粗体文字**",
            content_format=ContentFormat.MARKDOWN,
        )

        # 根据实际签名调用transform_content
        transformed = await self.content_manager.transform_content(
            test_content, "html"  # 转换为HTML格式
        )

        assert isinstance(transformed, TransformedContent)
        assert transformed.title == test_content.title
        assert transformed.html is not None
        assert len(transformed.html) > 0

    def test_extract_images(self):
        """测试提取图片URL"""
        cm = ContentManager()

        # 测试Markdown格式的图片
        markdown_content = "文本 ![图片](https://example.com/image.jpg) 更多文本"
        images = cm._extract_images(markdown_content, ContentFormat.MARKDOWN)
        assert "https://example.com/image.jpg" in images

        # 测试HTML格式的图片
        html_content = '文本 <img src="https://example.com/img.png" alt="图片"> 更多文本'
        images = cm._extract_images(html_content, ContentFormat.HTML)
        assert "https://example.com/img.png" in images

        # 测试无图片内容
        plain_content = "这是没有图片的纯文本"
        images = cm._extract_images(plain_content, ContentFormat.TEXT)
        assert len(images) == 0

    def test_extract_links(self):
        """测试提取链接URL"""
        cm = ContentManager()

        # 测试Markdown格式的链接
        markdown_content = "文本 [链接文字](https://example.com) 更多文本"
        links = cm._extract_links(markdown_content, ContentFormat.MARKDOWN)
        assert "https://example.com" in links

        # 测试HTML格式的链接
        html_content = '文本 <a href="https://test.com">链接</a> 更多文本'
        links = cm._extract_links(html_content, ContentFormat.HTML)
        assert "https://test.com" in links

    def test_html_to_text_conversion(self):
        """测试HTML到文本的转换"""
        cm = ContentManager()

        html_content = "<p>这是<strong>粗体</strong>文字。</p><p>第二段。</p>"
        text_result = cm._html_to_text(html_content)

        assert "这是" in text_result
        assert "粗体" in text_result
        assert "第二段" in text_result
        # HTML标签应该被移除
        assert "<p>" not in text_result
        assert "<strong>" not in text_result

    def test_text_to_html_conversion(self):
        """测试文本到HTML的转换"""
        cm = ContentManager()

        text_content = "第一行\n第二行\n\n第三段"
        html_result = cm._text_to_html(text_content)

        assert html_result.startswith("<p>")
        assert html_result.endswith("</p>")
        assert "第一行" in html_result
        assert "第二行" in html_result

    @pytest.mark.asyncio
    async def test_batch_parse_content(self):
        """测试批量解析内容"""
        # 创建多个测试文件
        file1 = self.temp_dir / "file1.md"
        file2 = self.temp_dir / "file2.md"

        file1.write_text("# 文件1\n内容1", encoding="utf-8")
        file2.write_text("# 文件2\n内容2", encoding="utf-8")

        file_paths = [str(file1), str(file2)]
        contents = await self.content_manager.batch_parse_content(file_paths)

        assert len(contents) == 2
        assert all(isinstance(content, Content) for content in contents)
        assert contents[0].title == "文件1"
        assert contents[1].title == "文件2"

    @pytest.mark.asyncio
    async def test_save_content(self):
        """测试保存内容到文件"""
        content = Content(title="保存测试", content="这是要保存的内容。")

        output_file = self.temp_dir / "output.md"
        success = await self.content_manager.save_content(content, str(output_file))

        assert success
        assert output_file.exists()

        # 验证文件内容
        saved_content = output_file.read_text(encoding="utf-8")
        assert "保存测试" in saved_content
        assert "这是要保存的内容" in saved_content


class TestContentManagerAdvanced:
    """测试ContentManager高级功能"""

    def setup_method(self):
        """设置测试环境"""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.content_manager = ContentManager()

    def teardown_method(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    @pytest.mark.asyncio
    async def test_parse_content_with_frontmatter(self):
        """测试解析带Front Matter的内容"""
        test_file = self.temp_dir / "frontmatter.md"
        content_with_fm = """---
title: Front Matter标题
author: 作者名
date: 2024-01-01
categories: [技术, 测试]
published: true
description: 这是描述
---

# Markdown标题

这是正文内容。

## 子标题

包含更多内容。
"""
        test_file.write_text(content_with_fm, encoding="utf-8")

        content = await self.content_manager.parse_content(str(test_file))

        assert content.title == "Front Matter标题"
        assert content.metadata is not None
        if content.metadata:
            assert content.metadata.get("author") == "作者名"
            assert content.metadata.get("published") is True

    @pytest.mark.asyncio
    async def test_error_handling_robustness(self):
        """测试错误处理的健壮性"""

        # 测试无效文件扩展名
        invalid_file = self.temp_dir / "test.xyz"
        invalid_file.write_text("内容", encoding="utf-8")

        try:
            content = await self.content_manager.parse_content(str(invalid_file))
            # 如果没有抛出异常，验证返回结果
            assert isinstance(content, Content)
        except Exception as e:
            # 预期可能的异常
            assert True

        # 测试空文件
        empty_file = self.temp_dir / "empty.md"
        empty_file.write_text("", encoding="utf-8")

        try:
            content = await self.content_manager.parse_content(str(empty_file))
            # 空文件可能被成功解析或抛出异常
            if content:
                assert isinstance(content, Content)
        except Exception:
            # 空文件处理异常是可接受的
            assert True

    @pytest.mark.asyncio
    async def test_complex_markdown_parsing(self):
        """测试复杂Markdown解析"""
        complex_md_file = self.temp_dir / "complex.md"
        complex_content = """# 复杂Markdown测试

这是一个包含各种Markdown元素的复杂文档。

## 文本格式

**粗体文字** 和 *斜体文字* 以及 `行内代码`。

### 列表

无序列表：
- 项目1
  - 子项目1.1
  - 子项目1.2
- 项目2

有序列表：
1. 第一项
2. 第二项
3. 第三项

### 引用

> 这是一个引用块
> 可以包含多行内容

### 代码块

```python
def hello_world():
    print("Hello, World!")
    return True
```

### 表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| A   | B   | C   |
| D   | E   | F   |

### 链接和图片

[外部链接](https://example.com)

![示例图片](https://example.com/image.jpg "图片标题")

### 分隔线

---

结束内容。
"""
        complex_md_file.write_text(complex_content, encoding="utf-8")

        content = await self.content_manager.parse_content(str(complex_md_file))

        assert isinstance(content, Content)
        assert content.title == "复杂Markdown测试"
        assert "这是一个包含各种Markdown元素" in content.content

        # 检查图片和链接提取
        assert content.has_images()
        images = content.extract_images()
        assert len(images) > 0
        assert "https://example.com/image.jpg" in images


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
