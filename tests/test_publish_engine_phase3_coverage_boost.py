"""
发布引擎Phase 3覆盖率提升测试

本测试文件专门用于提升PublishEngine的测试覆盖率
从当前31%目标提升至70%覆盖率

测试范围：
1. PublishEngine基础功能测试
2. 发布任务管理和执行
3. 批量发布操作测试
4. 发布状态检查和验证
5. 错误处理和重试机制
6. 适配器集成测试
7. 发布结果处理
8. 性能和并发测试
"""

import pytest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from src.textup.services.publish_engine import PublishEngine
from src.textup.models import (
    Platform,
    PublishResult,
    TransformedContent,
    ContentFormat,
    PublishTask,
    TaskStatus,
    TaskPriority,
)
from src.textup.utils import (
    PlatformAPIError,
    RateLimitError,
    InvalidCredentialsError,
    NetworkError,
    ValidationError,
)


class TestPublishEngineBasics:
    """发布引擎基础功能测试"""

    def test_publish_engine_initialization(self):
        """测试发布引擎初始化"""
        engine = PublishEngine()

        assert engine.max_concurrent_tasks == 5
        assert engine.task_timeout == 300.0
        assert engine.retry_attempts == 3
        assert engine.retry_delay == 2.0
        assert len(engine.active_tasks) == 0
        assert len(engine.completed_tasks) == 0
        assert len(engine.failed_tasks) == 0

    def test_publish_engine_custom_config(self):
        """测试自定义配置初始化"""
        config = {
            "max_concurrent_tasks": 10,
            "task_timeout": 600.0,
            "retry_attempts": 5,
            "retry_delay": 3.0,
            "enable_batch_mode": True,
        }

        engine = PublishEngine(config=config)

        assert engine.max_concurrent_tasks == 10
        assert engine.task_timeout == 600.0
        assert engine.retry_attempts == 5
        assert engine.retry_delay == 3.0
        assert engine.enable_batch_mode is True

    def test_adapter_registry_management(self):
        """测试适配器注册管理"""
        engine = PublishEngine()

        # 模拟适配器
        mock_weibo_adapter = Mock()
        mock_weibo_adapter.platform = Platform.WEIBO
        mock_zhihu_adapter = Mock()
        mock_zhihu_adapter.platform = Platform.ZHIHU

        # 注册适配器
        engine.register_adapter(Platform.WEIBO, mock_weibo_adapter)
        engine.register_adapter(Platform.ZHIHU, mock_zhihu_adapter)

        assert len(engine.adapters) == 2
        assert engine.adapters[Platform.WEIBO] == mock_weibo_adapter
        assert engine.adapters[Platform.ZHIHU] == mock_zhihu_adapter

    def test_get_adapter_success(self):
        """测试获取适配器成功"""
        engine = PublishEngine()
        mock_adapter = Mock()
        mock_adapter.platform = Platform.WEIBO

        engine.register_adapter(Platform.WEIBO, mock_adapter)

        adapter = engine.get_adapter(Platform.WEIBO)
        assert adapter == mock_adapter

    def test_get_adapter_not_found(self):
        """测试获取不存在的适配器"""
        engine = PublishEngine()

        with pytest.raises(ValueError) as exc_info:
            engine.get_adapter(Platform.WEIBO)

        assert "未注册的平台适配器" in str(exc_info.value)

    def test_is_platform_available(self):
        """测试平台可用性检查"""
        engine = PublishEngine()
        mock_adapter = Mock()

        engine.register_adapter(Platform.WEIBO, mock_adapter)

        assert engine.is_platform_available(Platform.WEIBO) is True
        assert engine.is_platform_available(Platform.ZHIHU) is False


class TestPublishEngineTaskManagement:
    """发布引擎任务管理测试"""

    def test_create_publish_task(self):
        """测试创建发布任务"""
        engine = PublishEngine()

        content = TransformedContent(
            title="测试标题", content="测试内容", format=ContentFormat.TEXT, tags=["测试", "任务"]
        )

        task = engine._create_publish_task(
            content=content,
            platform=Platform.WEIBO,
            options={"priority": "high"},
            task_id="test_task_001",
        )

        assert isinstance(task, PublishTask)
        assert task.task_id == "test_task_001"
        assert task.platform == Platform.WEIBO
        assert task.content == content
        assert task.status == TaskStatus.PENDING
        assert task.priority == TaskPriority.NORMAL  # 默认优先级

    def test_add_task_to_queue(self):
        """测试添加任务到队列"""
        engine = PublishEngine()

        task = PublishTask(
            task_id="queue_test_001",
            platform=Platform.WEIBO,
            content=TransformedContent("标题", "内容", ContentFormat.TEXT),
            status=TaskStatus.PENDING,
        )

        engine._add_task_to_queue(task)

        assert len(engine.pending_tasks) == 1
        assert engine.pending_tasks[0] == task

    def test_task_priority_sorting(self):
        """测试任务优先级排序"""
        engine = PublishEngine()

        # 创建不同优先级的任务
        low_task = PublishTask(
            task_id="low_001",
            platform=Platform.WEIBO,
            content=TransformedContent("低", "低优先级", ContentFormat.TEXT),
            priority=TaskPriority.LOW,
            status=TaskStatus.PENDING,
        )

        high_task = PublishTask(
            task_id="high_001",
            platform=Platform.WEIBO,
            content=TransformedContent("高", "高优先级", ContentFormat.TEXT),
            priority=TaskPriority.HIGH,
            status=TaskStatus.PENDING,
        )

        normal_task = PublishTask(
            task_id="normal_001",
            platform=Platform.WEIBO,
            content=TransformedContent("普通", "普通优先级", ContentFormat.TEXT),
            priority=TaskPriority.NORMAL,
            status=TaskStatus.PENDING,
        )

        # 按错误顺序添加
        engine._add_task_to_queue(low_task)
        engine._add_task_to_queue(high_task)
        engine._add_task_to_queue(normal_task)

        # 排序后应该是：HIGH -> NORMAL -> LOW
        engine._sort_pending_tasks()

        assert engine.pending_tasks[0] == high_task
        assert engine.pending_tasks[1] == normal_task
        assert engine.pending_tasks[2] == low_task

    def test_get_next_task(self):
        """测试获取下一个任务"""
        engine = PublishEngine()

        task1 = PublishTask(
            task_id="next_001",
            platform=Platform.WEIBO,
            content=TransformedContent("1", "任务1", ContentFormat.TEXT),
            status=TaskStatus.PENDING,
        )

        task2 = PublishTask(
            task_id="next_002",
            platform=Platform.ZHIHU,
            content=TransformedContent("2", "任务2", ContentFormat.TEXT),
            status=TaskStatus.PENDING,
        )

        engine._add_task_to_queue(task1)
        engine._add_task_to_queue(task2)

        next_task = engine._get_next_task()
        assert next_task == task1
        assert len(engine.pending_tasks) == 1
        assert len(engine.active_tasks) == 1

    def test_complete_task(self):
        """测试完成任务"""
        engine = PublishEngine()

        task = PublishTask(
            task_id="complete_001",
            platform=Platform.WEIBO,
            content=TransformedContent("完成", "完成任务", ContentFormat.TEXT),
            status=TaskStatus.RUNNING,
        )

        result = PublishResult(
            platform=Platform.WEIBO.value,
            is_success=True,
            platform_id="weibo_123",
            platform_url="https://weibo.com/123",
        )

        engine.active_tasks[task.task_id] = task
        engine._complete_task(task, result)

        assert task.status == TaskStatus.COMPLETED
        assert task.result == result
        assert task.task_id not in engine.active_tasks
        assert task.task_id in engine.completed_tasks

    def test_fail_task(self):
        """测试任务失败"""
        engine = PublishEngine()

        task = PublishTask(
            task_id="fail_001",
            platform=Platform.WEIBO,
            content=TransformedContent("失败", "失败任务", ContentFormat.TEXT),
            status=TaskStatus.RUNNING,
        )

        error = PlatformAPIError(platform=Platform.WEIBO.value, api_error="发布失败")

        result = PublishResult(
            platform=Platform.WEIBO.value, is_success=False, error_message="发布失败"
        )

        engine.active_tasks[task.task_id] = task
        engine._fail_task(task, error, result)

        assert task.status == TaskStatus.FAILED
        assert task.error == error
        assert task.result == result
        assert task.task_id not in engine.active_tasks
        assert task.task_id in engine.failed_tasks


class TestPublishEngineExecution:
    """发布引擎执行测试"""

    @pytest.mark.asyncio
    async def test_publish_single_success(self):
        """测试单个发布成功"""
        engine = PublishEngine()

        # 模拟适配器
        mock_adapter = AsyncMock()
        mock_adapter.platform = Platform.WEIBO
        mock_adapter.publish.return_value = PublishResult(
            platform=Platform.WEIBO.value,
            is_success=True,
            platform_id="weibo_123",
            platform_url="https://weibo.com/123",
        )

        engine.register_adapter(Platform.WEIBO, mock_adapter)

        content = TransformedContent(
            title="测试发布", content="单个发布测试内容", format=ContentFormat.TEXT
        )

        result = await engine.publish_single(content=content, platform=Platform.WEIBO, options={})

        assert isinstance(result, PublishResult)
        assert result.is_success is True
        assert result.platform_id == "weibo_123"
        mock_adapter.publish.assert_called_once()

    @pytest.mark.asyncio
    async def test_publish_single_failure(self):
        """测试单个发布失败"""
        engine = PublishEngine()

        mock_adapter = AsyncMock()
        mock_adapter.platform = Platform.WEIBO
        mock_adapter.publish.side_effect = PlatformAPIError(
            platform=Platform.WEIBO.value, api_error="API错误"
        )

        engine.register_adapter(Platform.WEIBO, mock_adapter)

        content = TransformedContent(
            title="失败测试", content="失败发布内容", format=ContentFormat.TEXT
        )

        result = await engine.publish_single(content=content, platform=Platform.WEIBO, options={})

        assert isinstance(result, PublishResult)
        assert result.is_success is False
        assert "API错误" in result.error_message

    @pytest.mark.asyncio
    async def test_publish_single_with_retry_success(self):
        """测试单个发布重试成功"""
        engine = PublishEngine(config={"retry_attempts": 2})

        mock_adapter = AsyncMock()
        mock_adapter.platform = Platform.WEIBO

        # 第一次失败，第二次成功
        call_count = 0

        async def mock_publish(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise RateLimitError("频率限制", retry_after=0.01)
            return PublishResult(
                platform=Platform.WEIBO.value, is_success=True, platform_id="weibo_retry_123"
            )

        mock_adapter.publish = mock_publish
        engine.register_adapter(Platform.WEIBO, mock_adapter)

        content = TransformedContent(
            title="重试测试", content="重试发布内容", format=ContentFormat.TEXT
        )

        result = await engine.publish_single(content=content, platform=Platform.WEIBO, options={})

        assert result.is_success is True
        assert result.platform_id == "weibo_retry_123"
        assert call_count == 2

    @pytest.mark.asyncio
    async def test_execute_task_success(self):
        """测试执行任务成功"""
        engine = PublishEngine()

        mock_adapter = AsyncMock()
        mock_adapter.platform = Platform.WEIBO
        mock_adapter.publish.return_value = PublishResult(
            platform=Platform.WEIBO.value, is_success=True, platform_id="task_123"
        )

        engine.register_adapter(Platform.WEIBO, mock_adapter)

        task = PublishTask(
            task_id="execute_001",
            platform=Platform.WEIBO,
            content=TransformedContent("执行", "执行任务", ContentFormat.TEXT),
            status=TaskStatus.PENDING,
        )

        await engine._execute_task(task)

        assert task.status == TaskStatus.COMPLETED
        assert task.result.is_success is True
        assert task.result.platform_id == "task_123"

    @pytest.mark.asyncio
    async def test_execute_task_timeout(self):
        """测试执行任务超时"""
        engine = PublishEngine(config={"task_timeout": 0.01})

        mock_adapter = AsyncMock()
        mock_adapter.platform = Platform.WEIBO

        # 模拟慢操作
        async def slow_publish(*args, **kwargs):
            await asyncio.sleep(0.1)  # 超过超时时间
            return PublishResult(platform=Platform.WEIBO.value, is_success=True)

        mock_adapter.publish = slow_publish
        engine.register_adapter(Platform.WEIBO, mock_adapter)

        task = PublishTask(
            task_id="timeout_001",
            platform=Platform.WEIBO,
            content=TransformedContent("超时", "超时任务", ContentFormat.TEXT),
            status=TaskStatus.PENDING,
        )

        await engine._execute_task(task)

        assert task.status == TaskStatus.FAILED
        assert "超时" in task.result.error_message.lower()

    @pytest.mark.asyncio
    async def test_batch_publish_success(self):
        """测试批量发布成功"""
        engine = PublishEngine()

        mock_adapter = AsyncMock()
        mock_adapter.platform = Platform.WEIBO

        # 模拟批量发布成功
        async def mock_batch_publish(contents, options):
            results = []
            for i, content in enumerate(contents):
                results.append(
                    PublishResult(
                        platform=Platform.WEIBO.value, is_success=True, platform_id=f"batch_{i}"
                    )
                )
            return results

        mock_adapter.batch_publish = mock_batch_publish
        engine.register_adapter(Platform.WEIBO, mock_adapter)

        contents = [
            TransformedContent(f"标题{i}", f"批量内容{i}", ContentFormat.TEXT) for i in range(3)
        ]

        results = await engine.publish_batch(contents=contents, platform=Platform.WEIBO, options={})

        assert len(results) == 3
        assert all(result.is_success for result in results)
        assert all(f"batch_{i}" in result.platform_id for i, result in enumerate(results))

    @pytest.mark.asyncio
    async def test_batch_publish_partial_failure(self):
        """测试批量发布部分失败"""
        engine = PublishEngine()

        mock_adapter = AsyncMock()
        mock_adapter.platform = Platform.WEIBO

        # 模拟部分成功，部分失败
        async def mock_batch_publish_partial(contents, options):
            results = []
            for i, content in enumerate(contents):
                if i % 2 == 0:  # 偶数索引成功
                    results.append(
                        PublishResult(
                            platform=Platform.WEIBO.value,
                            is_success=True,
                            platform_id=f"success_{i}",
                        )
                    )
                else:  # 奇数索引失败
                    results.append(
                        PublishResult(
                            platform=Platform.WEIBO.value,
                            is_success=False,
                            error_message=f"失败_{i}",
                        )
                    )
            return results

        mock_adapter.batch_publish = mock_batch_publish_partial
        engine.register_adapter(Platform.WEIBO, mock_adapter)

        contents = [
            TransformedContent(f"标题{i}", f"内容{i}", ContentFormat.TEXT) for i in range(4)
        ]

        results = await engine.publish_batch(contents=contents, platform=Platform.WEIBO, options={})

        assert len(results) == 4
        assert sum(1 for r in results if r.is_success) == 2
        assert sum(1 for r in results if not r.is_success) == 2


class TestPublishEngineStatusAndValidation:
    """发布引擎状态和验证测试"""

    @pytest.mark.asyncio
    async def test_get_publish_status_success(self):
        """测试获取发布状态成功"""
        engine = PublishEngine()

        mock_adapter = AsyncMock()
        mock_adapter.platform = Platform.WEIBO
        mock_adapter.get_publish_status.return_value = {
            "status": "published",
            "visibility": "public",
            "created_at": "2023-10-10T12:00:00Z",
        }

        engine.register_adapter(Platform.WEIBO, mock_adapter)

        status = await engine.get_publish_status(platform=Platform.WEIBO, publish_id="weibo_123")

        assert status["status"] == "published"
        assert status["visibility"] == "public"
        mock_adapter.get_publish_status.assert_called_once_with("weibo_123")

    @pytest.mark.asyncio
    async def test_get_publish_status_not_found(self):
        """测试获取发布状态未找到"""
        engine = PublishEngine()

        mock_adapter = AsyncMock()
        mock_adapter.platform = Platform.WEIBO
        mock_adapter.get_publish_status.side_effect = ValueError("发布内容未找到")

        engine.register_adapter(Platform.WEIBO, mock_adapter)

        with pytest.raises(ValueError) as exc_info:
            await engine.get_publish_status(platform=Platform.WEIBO, publish_id="nonexistent_123")

        assert "发布内容未找到" in str(exc_info.value)

    def test_validate_publish_request_success(self):
        """测试发布请求验证成功"""
        engine = PublishEngine()

        content = TransformedContent(
            title="有效标题",
            content="有效内容，长度足够进行发布测试",
            format=ContentFormat.TEXT,
            tags=["测试", "验证"],
        )

        result = engine._validate_publish_request(content, Platform.WEIBO, {})

        assert result.is_valid is True
        assert len(result.errors) == 0

    def test_validate_publish_request_empty_content(self):
        """测试发布请求验证空内容"""
        engine = PublishEngine()

        content = TransformedContent(title="", content="", format=ContentFormat.TEXT)

        result = engine._validate_publish_request(content, Platform.WEIBO, {})

        assert result.is_valid is False
        assert len(result.errors) > 0
        assert any("内容不能为空" in error.message for error in result.errors)

    def test_validate_publish_request_unsupported_platform(self):
        """测试不支持的平台验证"""
        engine = PublishEngine()

        content = TransformedContent(
            title="测试标题", content="测试内容", format=ContentFormat.TEXT
        )

        result = engine._validate_publish_request(content, Platform.WEIBO, {})

        assert result.is_valid is False
        assert len(result.errors) > 0
        assert any("平台不可用" in error.message for error in result.errors)

    def test_get_task_statistics(self):
        """测试获取任务统计"""
        engine = PublishEngine()

        # 添加不同状态的任务
        pending_task = PublishTask(
            "pending_1",
            Platform.WEIBO,
            TransformedContent("P", "待处理", ContentFormat.TEXT),
            status=TaskStatus.PENDING,
        )

        running_task = PublishTask(
            "running_1",
            Platform.ZHIHU,
            TransformedContent("R", "运行中", ContentFormat.TEXT),
            status=TaskStatus.RUNNING,
        )

        completed_task = PublishTask(
            "completed_1",
            Platform.WEIBO,
            TransformedContent("C", "已完成", ContentFormat.TEXT),
            status=TaskStatus.COMPLETED,
        )

        failed_task = PublishTask(
            "failed_1",
            Platform.ZHIHU,
            TransformedContent("F", "已失败", ContentFormat.TEXT),
            status=TaskStatus.FAILED,
        )

        engine.pending_tasks.append(pending_task)
        engine.active_tasks["running_1"] = running_task
        engine.completed_tasks["completed_1"] = completed_task
        engine.failed_tasks["failed_1"] = failed_task

        stats = engine.get_task_statistics()

        assert stats["pending_count"] == 1
        assert stats["active_count"] == 1
        assert stats["completed_count"] == 1
        assert stats["failed_count"] == 1
        assert stats["total_count"] == 4

    def test_get_task_by_id(self):
        """测试根据ID获取任务"""
        engine = PublishEngine()

        task = PublishTask(
            task_id="find_me_001",
            platform=Platform.WEIBO,
            content=TransformedContent("查找", "查找任务", ContentFormat.TEXT),
            status=TaskStatus.COMPLETED,
        )

        engine.completed_tasks["find_me_001"] = task

        found_task = engine.get_task_by_id("find_me_001")
        assert found_task == task

        not_found_task = engine.get_task_by_id("not_exist")
        assert not_found_task is None


class TestPublishEngineErrorHandling:
    """发布引擎错误处理测试"""

    @pytest.mark.asyncio
    async def test_handle_rate_limit_error(self):
        """测试处理速率限制错误"""
        engine = PublishEngine()

        mock_adapter = AsyncMock()
        mock_adapter.platform = Platform.WEIBO
        mock_adapter.publish.side_effect = RateLimitError("频率限制", retry_after=0.01)

        engine.register_adapter(Platform.WEIBO, mock_adapter)

        content = TransformedContent("限制", "速率限制测试", ContentFormat.TEXT)

        result = await engine.publish_single(content, Platform.WEIBO, {})

        assert result.is_success is False
        assert "频率限制" in result.error_message

    @pytest.mark.asyncio
    async def test_handle_authentication_error(self):
        """测试处理认证错误"""
        engine = PublishEngine()

        mock_adapter = AsyncMock()
        mock_adapter.platform = Platform.WEIBO
        mock_adapter.publish.side_effect = InvalidCredentialsError(
            "认证失败", error_code="INVALID_TOKEN"
        )

        engine.register_adapter(Platform.WEIBO, mock_adapter)

        content = TransformedContent("认证", "认证测试", ContentFormat.TEXT)

        result = await engine.publish_single(content, Platform.WEIBO, {})

        assert result.is_success is False
        assert "认证失败" in result.error_message

    @pytest.mark.asyncio
    async def test_handle_network_error_with_retry(self):
        """测试网络错误重试"""
        engine = PublishEngine(config={"retry_attempts": 3})

        mock_adapter = AsyncMock()
        mock_adapter.platform = Platform.WEIBO

        call_count = 0

        async def failing_publish(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise NetworkError("网络连接失败")
            return PublishResult(
                platform=Platform.WEIBO.value, is_success=True, platform_id="network_retry_success"
            )

        mock_adapter.publish = failing_publish
        engine.register_adapter(Platform.WEIBO, mock_adapter)

        content = TransformedContent("网络", "网络重试测试", ContentFormat.TEXT)

        result = await engine.publish_single(content, Platform.WEIBO, {})

        assert result.is_success is True
        assert result.platform_id == "network_retry_success"
        assert call_count == 3

    @pytest.mark.asyncio
    async def test_handle_validation_error_no_retry(self):
        """测试验证错误不重试"""
        engine = PublishEngine(config={"retry_attempts": 3})

        mock_adapter = AsyncMock()
        mock_adapter.platform = Platform.WEIBO
        mock_adapter.publish.side_effect = ValidationError(field="content", message="内容格式无效")

        engine.register_adapter(Platform.WEIBO, mock_adapter)

        content = TransformedContent("验证", "验证错误测试", ContentFormat.TEXT)

        result = await engine.publish_single(content, Platform.WEIBO, {})

        assert result.is_success is False
        assert "内容格式无效" in result.error_message
        # 验证错误不应该重试，所以只调用一次
        assert mock_adapter.publish.call_count == 1

    def test_error_classification(self):
        """测试错误分类"""
        engine = PublishEngine()

        # 测试不同错误的分类
        rate_limit = RateLimitError("频率限制")
        network = NetworkError("网络错误")
        auth = InvalidCredentialsError("认证错误")
        platform = PlatformAPIError("weibo", "平台错误")
        validation = ValidationError("field", "验证错误")

        assert engine._should_retry(rate_limit) is True
        assert engine._should_retry(network) is True
        assert engine._should_retry(auth) is False
        assert engine._should_retry(platform) is True
        assert engine._should_retry(validation) is False


class TestPublishEnginePerformanceAndConcurrency:
    """发布引擎性能和并发测试"""

    @pytest.mark.asyncio
    async def test_concurrent_publish_operations(self):
        """测试并发发布操作"""
        engine = PublishEngine(config={"max_concurrent_tasks": 5})

        mock_adapter = AsyncMock()
        mock_adapter.platform = Platform.WEIBO

        # 模拟并发发布
        async def concurrent_publish(content, options):
            await asyncio.sleep(0.01)  # 模拟网络延迟
            return PublishResult(
                platform=Platform.WEIBO.value,
                is_success=True,
                platform_id=f"concurrent_{content.title}",
            )

        mock_adapter.publish = concurrent_publish
        engine.register_adapter(Platform.WEIBO, mock_adapter)

        # 创建多个发布任务
        tasks = []
        for i in range(10):
            content = TransformedContent(f"并发{i}", f"并发内容{i}", ContentFormat.TEXT)
            task = engine.publish_single(content, Platform.WEIBO, {})
            tasks.append(task)

        # 并发执行
        results = await asyncio.gather(*tasks)

        assert len(results) == 10
        assert all(result.is_success for result in results)
        assert all("concurrent_" in result.platform_id for result in results)

    @pytest.mark.asyncio
    async def test_task_queue_management_under_load(self):
        """测试负载下的任务队列管理"""
        engine = PublishEngine(config={"max_concurrent_tasks": 2})

        mock_adapter = AsyncMock()
        mock_adapter.platform = Platform.WEIBO

        # 模拟慢操作
        async def slow_publish(content, options):
            await asyncio.sleep(0.05)
            return PublishResult(
                platform=Platform.WEIBO.value, is_success=True, platform_id=f"queue_{content.title}"
            )

        mock_adapter.publish = slow_publish
        engine.register_adapter(Platform.WEIBO, mock_adapter)

        # 创建多个任务
        contents = [
            TransformedContent(f"队列{i}", f"队列内容{i}", ContentFormat.TEXT) for i in range(6)
        ]

        # 提交任务到队列
        task_futures = []
        for content in contents:
            future = asyncio.create_task(engine.publish_async(content, "test_platform"))
            task_futures.append(future)

        # 等待所有任务完成
        results = await asyncio.gather(*task_futures, return_exceptions=True)

        # 验证所有任务都成功完成
        assert len(results) == 6
        for i, result in enumerate(results):
            assert not isinstance(result, Exception)
            assert result.title == f"队列{i}"

        # 验证队列状态
        assert engine.queue_size == 0  # 所有任务应该已完成

        # 验证发布统计
        stats = engine.get_publish_stats()
        assert stats.get("total_published", 0) >= 6
