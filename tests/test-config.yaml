# TextUp 测试配置 - 今日头条发布功能测试
# Test Configuration for Toutiao Publishing Feature

app:
  name: "TextUp"
  version: "1.0.0"
  log_level: "DEBUG"  # 测试时使用DEBUG级别
  max_concurrent: 3   # 测试时限制并发数
  timeout: 30
  enable_cache: true

# 平台配置
platforms:
  # 今日头条配置
  toutiao:
    enabled: true
    app_id: "your_toutiao_app_id"           # 需要替换为实际的App ID
    secret: "your_toutiao_app_secret"       # 需要替换为实际的Secret
    redirect_uri: "http://localhost:8080/auth/toutiao/callback"

    # 发布选项
    publish_options:
      category: "其他"                      # 默认分类
      original_type: 1                      # 1=原创, 0=转载
      auto_save_draft: true                 # 自动保存草稿

    # 内容限制
    content_limits:
      title_max_length: 100                 # 标题最大长度
      content_min_length: 100               # 内容最小长度
      content_max_length: 50000             # 内容最大长度

    # API配置
    api:
      base_url: "https://developer.toutiao.com/api"
      oauth_url: "https://developer.toutiao.com/oauth"
      timeout: 30
      max_retries: 3
      retry_delay: 2.0

  # 微博配置 (用于对比测试)
  weibo:
    enabled: true
    client_id: "test_weibo_client_id"
    client_secret: "test_weibo_client_secret"
    redirect_uri: "http://localhost:8080/auth/weibo/callback"

  # 知乎配置 (用于对比测试)
  zhihu:
    enabled: true
    client_id: "test_zhihu_client_id"
    client_secret: "test_zhihu_client_secret"
    redirect_uri: "http://localhost:8080/auth/zhihu/callback"

# 数据库配置
database:
  type: "sqlite"
  path: "./test-data/textup-test.db"
  echo: true  # 测试时显示SQL语句

# 日志配置
logging:
  level: "DEBUG"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  handlers:
    - type: "console"
      level: "INFO"
    - type: "file"
      level: "DEBUG"
      filename: "./logs/textup-test.log"
      max_bytes: 10485760  # 10MB
      backup_count: 5

# 内容处理配置
content:
  # 支持的格式
  supported_formats:
    - "markdown"
    - "html"
    - "text"

  # 转换选项
  conversion:
    markdown_to_html: true
    preserve_formatting: true
    auto_paragraph: true

  # 图片处理
  images:
    max_size: 5242880      # 5MB
    allowed_formats: ["jpg", "jpeg", "png", "gif"]
    upload_timeout: 60

# 错误处理配置
error_handling:
  max_retries: 3
  retry_delay: 2.0
  exponential_backoff: true

  # 重试的错误类型
  retry_on:
    - "ConnectionError"
    - "TimeoutError"
    - "RateLimitError"

  # 不重试的错误类型
  no_retry_on:
    - "AuthenticationError"
    - "ContentValidationError"
    - "InvalidCredentialsError"

# 测试专用配置
test:
  # 测试模式开关
  enabled: true

  # 模拟发布 (不实际发布到平台)
  mock_publish: false

  # 测试数据
  sample_content:
    title: "唐朝茶圣的秘密"
    content_file: "./test-story.md"
    cover_image: "./story-assets/tang-tea-master-cover.jpg"

  # 测试平台优先级
  test_platforms:
    - "toutiao"
    - "weibo"
    - "zhihu"

  # 测试结果保存
  save_results: true
  results_dir: "./test-results"

# 性能监控配置
monitoring:
  enabled: true
  metrics:
    - "publish_success_rate"
    - "publish_duration"
    - "api_response_time"
    - "error_count"

  # 输出格式
  output:
    format: "json"
    file: "./logs/metrics.json"

# 安全配置
security:
  # 凭据加密
  encrypt_credentials: true
  encryption_key_file: "./config/.encryption_key"

  # 令牌存储
  token_storage:
    type: "file"  # file 或 database
    location: "./config/tokens.enc"

# 开发配置
development:
  debug: true
  hot_reload: false
  profiling: false

  # API调试
  api_debug:
    log_requests: true
    log_responses: true
    save_payloads: true
    payloads_dir: "./debug/api-payloads"

# 环境变量映射
environment:
  # 可以通过环境变量覆盖的配置
  TEXTUP_LOG_LEVEL: "app.log_level"
  TEXTUP_DATABASE_PATH: "database.path"
  TEXTUP_TOUTIAO_APP_ID: "platforms.toutiao.app_id"
  TEXTUP_TOUTIAO_SECRET: "platforms.toutiao.secret"

# 配置版本和元数据
metadata:
  config_version: "1.0.0"
  created_at: "2024-12-19T18:00:00Z"
  created_by: "TextUp Test Suite"
  description: "今日头条发布功能测试配置"
  purpose: "端到端功能测试 - 历史故事发布流程"
