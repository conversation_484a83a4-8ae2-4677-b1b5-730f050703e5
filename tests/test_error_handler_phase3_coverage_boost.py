"""
错误处理器Phase 3覆盖率提升测试

本测试文件专门用于提升ErrorHandler的测试覆盖率
从当前29%目标提升至70%覆盖率

测试范围：
1. RetryConfig配置类测试
2. RetryPolicy重试策略测试
3. ErrorCollector错误收集器测试
4. ErrorHandler错误处理器核心功能
5. 错误分类和处理逻辑
6. 重试机制测试
7. 熔断器模式测试
8. 错误统计和报告
"""

import pytest
import asyncio
import time
import json
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from dataclasses import dataclass
from typing import Dict, Any, List, Optional

from textup.services.error_handler import (
    RetryConfig,
    RetryStrategy,
    RetryPolicy,
    ErrorCollector,
    ErrorHandler,
    CircuitBreakerState,
)
from textup.models import Platform, ErrorType, ErrorSeverity, PublishResult
from textup.utils import (
    TextUpError,
    PlatformAPIError,
    RateLimitError,
    InvalidCredentialsError,
    ValidationError,
    NetworkError,
)


class TestRetryConfig:
    """重试配置类测试"""

    def test_retry_config_default_initialization(self):
        """测试默认初始化"""
        config = RetryConfig()

        assert config.max_attempts == 3
        assert config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF
        assert config.base_delay == 1.0
        assert config.max_delay == 60.0
        assert config.exponential_base == 2.0
        assert config.jitter is True
        assert len(config.retry_on_exceptions) == 3
        assert len(config.no_retry_exceptions) == 2

    def test_retry_config_custom_initialization(self):
        """测试自定义初始化"""
        custom_retry_exceptions = [NetworkError, PlatformAPIError]
        custom_no_retry_exceptions = [ValidationError]

        config = RetryConfig(
            max_attempts=5,
            strategy=RetryStrategy.LINEAR_BACKOFF,
            base_delay=2.0,
            max_delay=120.0,
            exponential_base=3.0,
            jitter=False,
            retry_on_exceptions=custom_retry_exceptions,
            no_retry_exceptions=custom_no_retry_exceptions,
        )

        assert config.max_attempts == 5
        assert config.strategy == RetryStrategy.LINEAR_BACKOFF
        assert config.base_delay == 2.0
        assert config.max_delay == 120.0
        assert config.exponential_base == 3.0
        assert config.jitter is False
        assert config.retry_on_exceptions == custom_retry_exceptions
        assert config.no_retry_exceptions == custom_no_retry_exceptions

    def test_retry_config_exception_lists(self):
        """测试异常列表配置"""
        config = RetryConfig()

        # 检查默认重试异常
        retry_exceptions = config.retry_on_exceptions
        assert PlatformAPIError in retry_exceptions
        assert TimeoutError in retry_exceptions
        assert ConnectionError in retry_exceptions

        # 检查默认不重试异常
        no_retry_exceptions = config.no_retry_exceptions
        assert InvalidCredentialsError in no_retry_exceptions
        assert ValidationError in no_retry_exceptions

    def test_retry_config_strategy_enum(self):
        """测试重试策略枚举"""
        config_exp = RetryConfig(strategy=RetryStrategy.EXPONENTIAL_BACKOFF)
        config_linear = RetryConfig(strategy=RetryStrategy.LINEAR_BACKOFF)
        config_fixed = RetryConfig(strategy=RetryStrategy.FIXED_DELAY)

        assert config_exp.strategy == RetryStrategy.EXPONENTIAL_BACKOFF
        assert config_linear.strategy == RetryStrategy.LINEAR_BACKOFF
        assert config_fixed.strategy == RetryStrategy.FIXED_DELAY


class TestRetryPolicy:
    """重试策略类测试"""

    def test_retry_policy_initialization(self):
        """测试重试策略初始化"""
        policy = RetryPolicy(max_attempts=5, base_delay=2.0)

        assert policy.max_attempts == 5
        assert policy.base_delay == 2.0

    def test_retry_policy_default_values(self):
        """测试默认值"""
        policy = RetryPolicy()

        assert policy.max_attempts == 3
        assert policy.base_delay == 1.0

    def test_calculate_delay_exponential_backoff(self):
        """测试指数退避延迟计算"""
        config = RetryConfig(
            strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
            base_delay=1.0,
            exponential_base=2.0,
            max_delay=10.0,
            jitter=False,
        )

        # 模拟ErrorHandler来测试延迟计算
        handler = ErrorHandler()

        # 测试不同尝试次数的延迟
        delay_1 = handler._calculate_delay(config, 1)
        delay_2 = handler._calculate_delay(config, 2)
        delay_3 = handler._calculate_delay(config, 3)

        assert delay_1 == 1.0  # base_delay * (2^0)
        assert delay_2 == 2.0  # base_delay * (2^1)
        assert delay_3 == 4.0  # base_delay * (2^2)

    def test_calculate_delay_linear_backoff(self):
        """测试线性退避延迟计算"""
        config = RetryConfig(
            strategy=RetryStrategy.LINEAR_BACKOFF, base_delay=1.0, max_delay=10.0, jitter=False
        )

        handler = ErrorHandler()

        delay_1 = handler._calculate_delay(config, 1)
        delay_2 = handler._calculate_delay(config, 2)
        delay_3 = handler._calculate_delay(config, 3)

        assert delay_1 == 1.0  # base_delay * 1
        assert delay_2 == 2.0  # base_delay * 2
        assert delay_3 == 3.0  # base_delay * 3

    def test_calculate_delay_fixed_delay(self):
        """测试固定延迟"""
        config = RetryConfig(strategy=RetryStrategy.FIXED_DELAY, base_delay=2.5, jitter=False)

        handler = ErrorHandler()

        delay_1 = handler._calculate_delay(config, 1)
        delay_2 = handler._calculate_delay(config, 2)
        delay_3 = handler._calculate_delay(config, 3)

        assert delay_1 == 2.5
        assert delay_2 == 2.5
        assert delay_3 == 2.5

    def test_calculate_delay_max_delay_limit(self):
        """测试最大延迟限制"""
        config = RetryConfig(
            strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
            base_delay=10.0,
            exponential_base=2.0,
            max_delay=15.0,
            jitter=False,
        )

        handler = ErrorHandler()

        # 第4次尝试应该是 10.0 * (2^3) = 80.0，但应该被限制为15.0
        delay_4 = handler._calculate_delay(config, 4)
        assert delay_4 == 15.0


class TestErrorCollector:
    """错误收集器测试"""

    def test_error_collector_initialization(self):
        """测试错误收集器初始化"""
        collector = ErrorCollector()

        assert len(collector.errors) == 0
        assert len(collector.error_stats) == 0

    def test_collect_error_basic(self):
        """测试基本错误收集"""
        collector = ErrorCollector()

        error = PlatformAPIError(platform="weibo", api_error="测试错误", status_code=500)

        collector.collect_error(error, {"context": "test"})

        assert len(collector.errors) == 1
        assert collector.errors[0]["error"] == error
        assert collector.errors[0]["context"]["context"] == "test"
        assert "timestamp" in collector.errors[0]

    def test_collect_multiple_errors(self):
        """测试收集多个错误"""
        collector = ErrorCollector()

        errors = [
            PlatformAPIError("weibo", "错误1"),
            RateLimitError("速率限制"),
            InvalidCredentialsError("无效凭证"),
        ]

        for i, error in enumerate(errors):
            collector.collect_error(error, {"index": i})

        assert len(collector.errors) == 3

        # 验证错误分类统计
        stats = collector.get_error_stats()
        assert stats["total_errors"] == 3
        assert len(stats["by_type"]) > 0

    def test_classify_error_types(self):
        """测试错误分类"""
        collector = ErrorCollector()

        # 测试不同类型的错误分类
        network_error = ConnectionError("网络连接失败")
        auth_error = InvalidCredentialsError("认证失败")
        rate_limit_error = RateLimitError("频率限制")
        validation_error = ValidationError("field", "验证失败")
        platform_error = PlatformAPIError("zhihu", "平台错误")
        general_error = Exception("通用错误")

        assert collector._classify_error(network_error) == ErrorType.NETWORK
        assert collector._classify_error(auth_error) == ErrorType.AUTHENTICATION
        assert collector._classify_error(rate_limit_error) == ErrorType.RATE_LIMIT
        assert collector._classify_error(validation_error) == ErrorType.VALIDATION
        assert collector._classify_error(platform_error) == ErrorType.PLATFORM
        assert collector._classify_error(general_error) == ErrorType.UNKNOWN

    def test_error_stats_calculation(self):
        """测试错误统计计算"""
        collector = ErrorCollector()

        # 添加不同类型的错误
        errors = [
            PlatformAPIError("weibo", "错误1"),
            PlatformAPIError("zhihu", "错误2"),
            RateLimitError("限制1"),
            RateLimitError("限制2"),
            InvalidCredentialsError("认证错误"),
        ]

        for error in errors:
            collector.collect_error(error)

        stats = collector.get_error_stats()

        assert stats["total_errors"] == 5
        assert stats["by_type"][ErrorType.PLATFORM.value] == 2
        assert stats["by_type"][ErrorType.RATE_LIMIT.value] == 2
        assert stats["by_type"][ErrorType.AUTHENTICATION.value] == 1

    def test_clear_errors(self):
        """测试清空错误记录"""
        collector = ErrorCollector()

        # 添加一些错误
        for i in range(3):
            collector.collect_error(Exception(f"错误{i}"))

        assert len(collector.errors) == 3

        collector.clear_errors()

        assert len(collector.errors) == 0
        assert len(collector.error_stats) == 0


class TestErrorHandler:
    """错误处理器核心测试"""

    def test_error_handler_initialization(self):
        """测试错误处理器初始化"""
        handler = ErrorHandler()

        assert isinstance(handler.error_collector, ErrorCollector)
        assert len(handler.retry_configs) > 0
        assert len(handler.circuit_breakers) == 0
        assert handler.is_enabled is True

    def test_error_handler_with_custom_config(self):
        """测试自定义配置初始化"""
        custom_config = {
            "default_max_attempts": 5,
            "default_base_delay": 2.0,
            "enable_circuit_breaker": True,
            "circuit_breaker_threshold": 10,
        }

        handler = ErrorHandler(config=custom_config)

        assert handler.config["default_max_attempts"] == 5
        assert handler.config["default_base_delay"] == 2.0
        assert handler.config["enable_circuit_breaker"] is True

    def test_setup_default_configs(self):
        """测试默认配置设置"""
        handler = ErrorHandler()

        # 验证默认配置存在
        assert "platform_api" in handler.retry_configs
        assert "network_request" in handler.retry_configs
        assert "authentication" in handler.retry_configs

        # 验证平台API配置
        platform_config = handler.retry_configs["platform_api"]
        assert platform_config.max_attempts == 3
        assert platform_config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF

    def test_should_retry_decision(self):
        """测试重试决策逻辑"""
        handler = ErrorHandler()
        config = RetryConfig(
            max_attempts=3,
            retry_on_exceptions=[PlatformAPIError, NetworkError],
            no_retry_exceptions=[InvalidCredentialsError],
        )

        # 应该重试的错误
        retryable_error = PlatformAPIError("test", "可重试错误")
        assert handler._should_retry(retryable_error, 1, config) is True
        assert handler._should_retry(retryable_error, 3, config) is False  # 超过最大次数

        # 不应该重试的错误
        non_retryable_error = InvalidCredentialsError("不可重试错误")
        assert handler._should_retry(non_retryable_error, 1, config) is False

    @pytest.mark.asyncio
    async def test_handle_error_with_retry_success(self):
        """测试错误处理与重试成功"""
        handler = ErrorHandler()

        # 模拟一个会失败然后成功的操作
        call_count = 0

        async def failing_operation():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise PlatformAPIError("test", "临时错误")
            return "成功结果"

        config = RetryConfig(max_attempts=3, base_delay=0.01)  # 快速测试

        result = await handler.handle_error_with_retry(
            failing_operation, config, context={"test": "retry_test"}
        )

        assert result == "成功结果"
        assert call_count == 3

    @pytest.mark.asyncio
    async def test_handle_error_with_retry_final_failure(self):
        """测试重试后最终失败"""
        handler = ErrorHandler()

        async def always_failing_operation():
            raise PlatformAPIError("test", "持续错误")

        config = RetryConfig(max_attempts=2, base_delay=0.01)

        with pytest.raises(PlatformAPIError):
            await handler.handle_error_with_retry(
                always_failing_operation, config, context={"test": "final_failure"}
            )

    @pytest.mark.asyncio
    async def test_handle_error_with_retry_non_retryable(self):
        """测试不可重试错误处理"""
        handler = ErrorHandler()

        async def auth_failing_operation():
            raise InvalidCredentialsError("认证失败")

        config = RetryConfig(max_attempts=3, no_retry_exceptions=[InvalidCredentialsError])

        with pytest.raises(InvalidCredentialsError):
            await handler.handle_error_with_retry(auth_failing_operation, config)

        # 验证错误被收集
        assert len(handler.error_collector.errors) > 0

    def test_circuit_breaker_initialization(self):
        """测试熔断器初始化"""
        handler = ErrorHandler()

        circuit_breaker = handler._get_circuit_breaker("test_service")

        assert circuit_breaker is not None
        assert circuit_breaker["state"] == CircuitBreakerState.CLOSED
        assert circuit_breaker["failure_count"] == 0
        assert circuit_breaker["last_failure_time"] is None

    def test_circuit_breaker_state_transitions(self):
        """测试熔断器状态转换"""
        handler = ErrorHandler()

        service_name = "test_service"

        # 初始状态应该是CLOSED
        assert handler._is_circuit_open(service_name) is False

        # 模拟多次失败，触发熔断器打开
        for _ in range(handler.config["circuit_breaker_threshold"]):
            handler._record_circuit_breaker_failure(service_name)

        assert handler._is_circuit_open(service_name) is True

        circuit_breaker = handler.circuit_breakers[service_name]
        assert circuit_breaker["state"] == CircuitBreakerState.OPEN

    def test_circuit_breaker_recovery(self):
        """测试熔断器恢复"""
        handler = ErrorHandler()
        handler.config["circuit_breaker_recovery_time"] = 0.01  # 快速测试

        service_name = "test_service"

        # 触发熔断器打开
        for _ in range(handler.config["circuit_breaker_threshold"]):
            handler._record_circuit_breaker_failure(service_name)

        assert handler._is_circuit_open(service_name) is True

        # 等待恢复时间
        time.sleep(0.02)

        # 现在应该进入半开状态
        assert handler._is_circuit_open(service_name) is False
        circuit_breaker = handler.circuit_breakers[service_name]
        assert circuit_breaker["state"] == CircuitBreakerState.HALF_OPEN

    def test_circuit_breaker_success_reset(self):
        """测试熔断器成功重置"""
        handler = ErrorHandler()

        service_name = "test_service"

        # 记录一些失败
        for _ in range(2):
            handler._record_circuit_breaker_failure(service_name)

        circuit_breaker = handler.circuit_breakers[service_name]
        assert circuit_breaker["failure_count"] == 2

        # 记录成功，应该重置计数
        handler._record_circuit_breaker_success(service_name)

        assert circuit_breaker["failure_count"] == 0
        assert circuit_breaker["state"] == CircuitBreakerState.CLOSED

    def test_get_error_summary(self):
        """测试错误摘要生成"""
        handler = ErrorHandler()

        # 添加一些错误
        errors = [
            PlatformAPIError("weibo", "错误1"),
            PlatformAPIError("zhihu", "错误2"),
            RateLimitError("限制错误"),
            InvalidCredentialsError("认证错误"),
        ]

        for error in errors:
            handler.error_collector.collect_error(error)

        summary = handler.get_error_summary()

        assert "total_errors" in summary
        assert "by_type" in summary
        assert "by_severity" in summary
        assert "recent_errors" in summary

        assert summary["total_errors"] == 4

    def test_clear_errors(self):
        """测试清空错误记录"""
        handler = ErrorHandler()

        # 添加错误
        handler.error_collector.collect_error(Exception("测试错误"))

        assert len(handler.error_collector.errors) == 1

        handler.clear_errors()

        assert len(handler.error_collector.errors) == 0

    def test_is_enabled_functionality(self):
        """测试启用/禁用功能"""
        handler = ErrorHandler()

        assert handler.is_enabled is True

        handler.disable()
        assert handler.is_enabled is False

        handler.enable()
        assert handler.is_enabled is True

    @pytest.mark.asyncio
    async def test_disabled_handler_passthrough(self):
        """测试禁用状态下的直通行为"""
        handler = ErrorHandler()
        handler.disable()

        async def test_operation():
            return "直通结果"

        result = await handler.handle_error_with_retry(test_operation, RetryConfig())

        assert result == "直通结果"


class TestErrorHandlerIntegration:
    """错误处理器集成测试"""

    @pytest.mark.asyncio
    async def test_full_error_handling_workflow(self):
        """测试完整的错误处理工作流"""
        handler = ErrorHandler()

        # 模拟复杂的操作场景
        attempt_count = 0

        async def complex_operation():
            nonlocal attempt_count
            attempt_count += 1

            if attempt_count == 1:
                raise NetworkError("网络连接失败")
            elif attempt_count == 2:
                raise RateLimitError("频率限制", retry_after=0.01)
            else:
                return {"status": "success", "data": "操作完成"}

        config = RetryConfig(
            max_attempts=3, base_delay=0.01, retry_on_exceptions=[NetworkError, RateLimitError]
        )

        result = await handler.handle_error_with_retry(
            complex_operation,
            config,
            service_name="complex_service",
            context={"user_id": "test_user"},
        )

        assert result["status"] == "success"
        assert attempt_count == 3

        # 验证错误被正确收集和分类
        summary = handler.get_error_summary()
        assert summary["total_errors"] >= 2

    @pytest.mark.asyncio
    async def test_circuit_breaker_integration(self):
        """测试熔断器集成"""
        handler = ErrorHandler()
        handler.config["circuit_breaker_threshold"] = 2  # 降低阈值以便测试

        service_name = "unreliable_service"

        async def unreliable_operation():
            raise PlatformAPIError("test", "服务不可用")

        config = RetryConfig(max_attempts=1)  # 不重试，直接失败

        # 触发多次失败以打开熔断器
        for _ in range(2):
            with pytest.raises(PlatformAPIError):
                await handler.handle_error_with_retry(
                    unreliable_operation, config, service_name=service_name
                )

        # 现在熔断器应该是打开的
        assert handler._is_circuit_open(service_name) is True

        # 下次调用应该立即失败而不执行操作
        call_count = 0

        async def counting_operation():
            nonlocal call_count
            call_count += 1
            raise PlatformAPIError("test", "不应该被调用")

        with pytest.raises(Exception):  # 熔断器会抛出特定异常
            await handler.handle_error_with_retry(
                counting_operation, config, service_name=service_name
            )

        # 验证操作没有被执行
        assert call_count == 0

    def test_error_context_preservation(self):
        """测试错误上下文保持"""
        handler = ErrorHandler()

        test_context = {
            "user_id": "test_user",
            "operation": "publish",
            "platform": "weibo",
            "content_id": "123456",
        }

        error = PlatformAPIError("weibo", "发布失败")
        handler.error_collector.collect_error(error, test_context)

        collected_error = handler.error_collector.errors[0]

        assert collected_error["context"]["user_id"] == "test_user"
        assert collected_error["context"]["operation"] == "publish"
        assert collected_error["context"]["platform"] == "weibo"
        assert collected_error["context"]["content_id"] == "123456"

    def test_performance_metrics_collection(self):
        """测试性能指标收集"""
        handler = ErrorHandler()

        # 添加不同时间的错误
        import time

        base_time = time.time()

        for i in range(5):
            error = Exception(f"错误{i}")
            # 模拟不同时间的错误
            with patch("time.time", return_value=base_time + i):
                handler.error_collector.collect_error(error)

        summary = handler.get_error_summary()

        assert "recent_errors" in summary
        assert len(summary["recent_errors"]) <= 5

    @pytest.mark.asyncio
    async def test_concurrent_error_handling(self):
        """测试并发错误处理"""
        handler = ErrorHandler()

        async def concurrent_operation(operation_id: int):
            if operation_id % 2 == 0:
                raise PlatformAPIError("test", f"错误{operation_id}")
            return f"成功{operation_id}"

        config = RetryConfig(max_attempts=1)

        # 创建多个并发任务
        tasks = []
        for i in range(10):
            task = handler.handle_error_with_retry(
                lambda i=i: concurrent_operation(i),
                config,
                service_name=f"service_{i}",
                context={"operation_id": i},
            )
            tasks.append(task)

        # 执行并发任务
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 验证结果
        success_count = sum(1 for result in results if isinstance(result, str))
        error_count = sum(1 for result in results if isinstance(result, Exception))

        assert success_count == 5  # 奇数操作应该成功
        assert error_count == 5  # 偶数操作应该失败

        # 验证错误收集
        assert len(handler.error_collector.errors) >= error_count


class TestErrorHandlerEdgeCases:
    """错误处理器边界情况测试"""

    def test_empty_retry_config(self):
        """测试空重试配置"""
        handler = ErrorHandler()
        config = RetryConfig(max_attempts=0)  # 不重试

        assert handler._should_retry(Exception("test"), 1, config) is False

    def test_negative_delays(self):
        """测试负延迟处理"""
        handler = ErrorHandler()
        config = RetryConfig(base_delay=-1.0)

        delay = handler._calculate_delay(config, 1)
        assert delay >= 0  # 延迟不应该是负数

    def test_very_large_attempt_numbers(self):
        """测试非常大的尝试次数"""
        handler = ErrorHandler()
        config = RetryConfig(
            strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
            base_delay=1.0,
            exponential_base=2.0,
            max_delay=10.0,
        )

        # 测试大数字不会导致溢出
        delay = handler._calculate_delay(config, 100)
        assert delay == config.max_delay  # 应该被限制为最大延迟

    def test_circuit_breaker_edge_cases(self):
        """测试熔断器边界情况"""
        handler = ErrorHandler()

        # 测试不存在的服务
        assert handler._is_circuit_open("nonexistent_service") is False

        # 测试零阈值
        handler.config["circuit_breaker_threshold"] = 0
        handler._record_circuit_breaker_failure("zero_threshold_service")
        assert handler._is_circuit_open("zero_threshold_service") is True

    def test_malformed_error_objects(self):
        """测试格式错误的异常对象"""
        handler = ErrorHandler()

        # 测试没有消息的异常
        class EmptyException(Exception):
            def __str__(self):
                return ""

        empty_error = EmptyException()
        handler.error_collector.collect_error(empty_error)

        assert len(handler.error_collector.errors) == 1

    @pytest.mark.asyncio
    async def test_operation_timeout_during_retry(self):
        """测试重试过程中的超时"""
        handler = ErrorHandler()

        async def slow_operation():
            await asyncio.sleep(0.1)
            raise PlatformAPIError("test", "超时操作")

        config = RetryConfig(max_attempts=2, base_delay=0.01)

        start_time = time.time()

        with pytest.raises(PlatformAPIError):
            await handler.handle_error_with_retry(slow_operation, config)

        elapsed_time = time.time() - start_time
        # 验证确实进行了重试（总时间 > 单次操作时间）
        assert elapsed_time > 0.1

    def test_memory_usage_with_many_errors(self):
        """测试大量错误的内存使用"""
        handler = ErrorHandler()

        # 添加大量错误
        for i in range(1000):
            error = Exception(f"错误{i}")
            handler.error_collector.collect_error(error, {"index": i})

        assert len(handler.error_collector.errors) == 1000

        # 测试清空功能
        handler.clear_errors()
        assert len(handler.error_collector.errors) == 0


# 运行覆盖率检
