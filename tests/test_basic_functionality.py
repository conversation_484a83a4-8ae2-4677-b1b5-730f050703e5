"""
TextUp 基本功能验证测试

这个测试文件用于验证项目的基本功能是否正常工作。
专门针对当前实际的代码实现编写，确保测试能够通过。
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from textup.models import (
    Content,
    Platform,
    ContentFormat,
    TaskStatus,
    PublishStatus,
    ValidationResult,
    ValidationError,
    PublishResult,
    AuthResult,
)


class TestBasicModels:
    """测试基本模型功能"""

    def test_content_creation(self):
        """测试Content模型基本创建功能"""
        content = Content(title="测试标题", content="这是测试内容")

        assert content.title == "测试标题"
        assert content.content == "这是测试内容"
        assert content.content_format == ContentFormat.MARKDOWN  # 注意是content_format不是format
        assert isinstance(content.created_at, datetime)
        assert isinstance(content.updated_at, datetime)
        assert content.tags == []  # 默认为空列表
        assert content.metadata == {}  # 默认为空字典

    def test_content_with_tags(self):
        """测试带标签的Content创建"""
        content = Content(title="标题", content="内容", tags=["标签1", "标签2", "标签3"])

        assert len(content.tags) == 3
        assert "标签1" in content.tags

    def test_content_validation(self):
        """测试Content模型验证（使用Pydantic验证）"""
        # 测试空标题会引发验证错误
        with pytest.raises(ValueError, match="标题不能为空"):
            Content(title="", content="内容")

        # 测试空内容会引发验证错误
        with pytest.raises(ValueError, match="内容不能为空"):
            Content(title="标题", content="")

    def test_content_methods(self):
        """测试Content模型的方法"""
        content = Content(title="测试标题", content="这是一个测试内容，用于测试字数统计功能。")

        # 测试字数统计
        word_count = content.get_word_count()
        assert word_count > 0

        # 测试字符数统计
        char_count = content.get_char_count()
        assert char_count > 0

        # 测试JSON转换
        json_str = content.to_json()
        assert isinstance(json_str, str)
        assert "测试标题" in json_str

        # 测试字典转换
        data_dict = content.to_dict()
        assert isinstance(data_dict, dict)
        assert data_dict["title"] == "测试标题"

    def test_content_image_detection(self):
        """测试图片检测功能"""
        # 测试无图片内容
        content_no_img = Content(title="标题", content="这是纯文本内容，没有图片。")
        assert not content_no_img.has_images()

        # 测试带Markdown图片的内容
        content_with_img = Content(
            title="标题", content="内容 ![测试图片](https://example.com/image.jpg) 更多内容"
        )
        assert content_with_img.has_images()

    def test_validation_result(self):
        """测试ValidationResult模型"""
        # 测试有效结果
        valid_result = ValidationResult(is_valid=True, errors=[])
        assert valid_result.is_valid
        assert len(valid_result.errors) == 0

        # 测试无效结果
        error = ValidationError(field="title", message="标题不能为空", value="")
        invalid_result = ValidationResult(is_valid=False, errors=[error])
        assert not invalid_result.is_valid
        assert len(invalid_result.errors) == 1
        assert invalid_result.errors[0].field == "title"

    def test_publish_result(self):
        """测试PublishResult模型"""
        # 测试成功结果
        success_result = PublishResult(
            success=True,
            platform=Platform.ZHIHU,
            platform_post_id="12345",
            platform_url="https://zhihu.com/answer/12345",
            publish_time=datetime.now(),
            message="发布成功",
        )

        assert success_result.success
        assert success_result.platform == Platform.ZHIHU
        assert success_result.platform_post_id == "12345"
        assert success_result.message == "发布成功"

        # 测试失败结果
        failure_result = PublishResult(
            success=False, platform=Platform.WEIBO, error_message="发布失败：网络错误"
        )

        assert not failure_result.success
        assert failure_result.platform == Platform.WEIBO
        assert failure_result.error_message == "发布失败：网络错误"

    def test_auth_result(self):
        """测试AuthResult模型"""
        # 测试成功认证
        success_auth = AuthResult(
            success=True,
            platform=Platform.ZHIHU,
            auth_data={"access_token": "token123", "user_id": "user456"},
            message="认证成功",
        )

        assert success_auth.success
        assert success_auth.platform == Platform.ZHIHU
        assert success_auth.auth_data["access_token"] == "token123"

        # 测试失败认证
        failure_auth = AuthResult(
            success=False, platform=Platform.WEIBO, error_message="认证失败：无效凭证"
        )

        assert not failure_auth.success
        assert failure_auth.error_message == "认证失败：无效凭证"


class TestBasicServices:
    """测试基本服务功能"""

    def setup_method(self):
        """设置测试环境"""
        self.temp_dir = Path(tempfile.mkdtemp())

    def teardown_method(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    @pytest.mark.asyncio
    async def test_config_manager_basic(self):
        """测试ConfigManager基本功能"""
        from textup.services.config_manager import ConfigManager

        config_manager = ConfigManager(str(self.temp_dir))

        # 测试加载配置（会创建默认配置）
        config = await config_manager.load_config()
        assert isinstance(config, dict)
        assert "app" in config

        # 测试设置配置值
        success = await config_manager.set_config_value("test.key", "test_value")
        assert success

        # 测试获取配置值
        value = await config_manager.get_config_value("test.key")
        assert value == "test_value"

    @pytest.mark.asyncio
    async def test_content_manager_basic(self):
        """测试ContentManager基本功能"""
        from textup.services.content_manager import ContentManager

        content_manager = ContentManager()

        # 创建测试Markdown文件
        test_file = self.temp_dir / "test.md"
        test_file.write_text(
            """# 测试标题

这是测试内容。

## 子标题

包含**粗体**文字。
""",
            encoding="utf-8",
        )

        # 测试加载内容
        content = await content_manager.load_content(str(test_file))
        assert isinstance(content, Content)
        assert content.title == "测试标题"
        assert "这是测试内容" in content.content

        # 测试验证内容
        validation = await content_manager.validate_content(content)
        assert isinstance(validation, ValidationResult)
        assert validation.is_valid

    @pytest.mark.asyncio
    async def test_content_transformation(self):
        """测试内容转换功能"""
        from textup.services.content_manager import ContentManager

        content_manager = ContentManager()

        test_content = Content(title="测试转换", content="# 标题\n\n这是内容。\n\n**粗体文字**")

        # 测试转换为知乎格式
        transformed = await content_manager.transform_content(test_content, Platform.ZHIHU, {})

        assert transformed is not None
        assert transformed.title == "测试转换"
        assert transformed.platform == Platform.ZHIHU
        assert transformed.html is not None
        assert "<h1>" in transformed.html or "标题" in transformed.html


class TestCLIBasic:
    """测试CLI基本功能"""

    def test_cli_import(self):
        """测试CLI模块能够正常导入"""
        from textup.cli.main import main

        assert callable(main)

    def test_get_managers(self):
        """测试管理器获取函数"""
        from textup.cli.main import get_config_manager, get_content_manager

        config_manager = get_config_manager("test_config")
        assert config_manager is not None

        content_manager = get_content_manager()
        assert content_manager is not None


class TestUtilities:
    """测试工具函数"""

    def test_exception_imports(self):
        """测试异常类能够正常导入"""
        from textup.utils import TextUpError, PlatformAPIError, ConfigurationError, handle_exception

        assert issubclass(TextUpError, Exception)
        assert issubclass(PlatformAPIError, TextUpError)
        assert issubclass(ConfigurationError, TextUpError)
        assert callable(handle_exception)

    def test_interface_imports(self):
        """测试接口协议能够正常导入"""
        from textup.utils import (
            ContentManagerProtocol,
            PlatformAdapterProtocol,
            ConfigManagerProtocol,
        )

        # 这些是Protocol类型，主要测试能否正常导入
        assert ContentManagerProtocol is not None
        assert PlatformAdapterProtocol is not None
        assert ConfigManagerProtocol is not None


class TestPlatformEnums:
    """测试平台枚举"""

    def test_platform_enum(self):
        """测试Platform枚举"""
        assert Platform.ZHIHU.value == "zhihu"
        assert Platform.WEIBO.value == "weibo"

        # 测试可以从字符串获取枚举
        zhihu_platform = Platform("zhihu")
        assert zhihu_platform == Platform.ZHIHU

    def test_content_format_enum(self):
        """测试ContentFormat枚举"""
        assert ContentFormat.MARKDOWN.value == "markdown"
        assert ContentFormat.HTML.value == "html"
        assert ContentFormat.PLAIN_TEXT.value == "plain_text"

    def test_task_status_enum(self):
        """测试TaskStatus枚举"""
        assert TaskStatus.PENDING.value == "pending"
        assert TaskStatus.RUNNING.value == "running"
        assert TaskStatus.COMPLETED.value == "completed"
        assert TaskStatus.FAILED.value == "failed"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
