"""
TextUp CLI综合测试

本模块包含对CLI组件的全面测试，旨在提升测试覆盖率并验证命令行功能。
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from typer.testing import CliRunner
import json
import yaml

import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent / "src"))

from textup.cli.main import app, get_config_manager, get_content_manager, parse_config_value
from textup.services.config_manager import ConfigManager
from textup.services.content_manager import ContentManager
from textup.models import Content, Platform, ContentFormat
from textup.utils.exceptions import ConfigurationError, ContentError


class TestCLIComprehensive:
    """CLI综合测试"""

    @pytest.fixture
    def runner(self):
        """CLI测试运行器"""
        return CliRunner()

    @pytest.fixture
    def temp_dir(self):
        """临时目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir

    def test_cli_main_help(self, runner):
        """测试主命令帮助"""
        result = runner.invoke(app, ["--help"])
        assert result.exit_code == 0
        assert "textup" in result.output.lower()

    def test_cli_version_flag(self, runner):
        """测试版本标志"""
        result = runner.invoke(app, ["--version"])
        # 版本命令可能会有不同的退出码，但不应该崩溃
        assert "error" not in result.output.lower() or result.exit_code in [0, 1]

    def test_config_command_help(self, runner):
        """测试config命令帮助"""
        result = runner.invoke(app, ["config", "--help"])
        assert result.exit_code == 0
        assert "配置" in result.output

    def test_auth_command_help(self, runner):
        """测试auth命令帮助"""
        result = runner.invoke(app, ["auth", "--help"])
        assert result.exit_code == 0
        assert "认证" in result.output

    def test_publish_command_help(self, runner):
        """测试publish命令帮助"""
        result = runner.invoke(app, ["publish", "--help"])
        assert result.exit_code == 0
        assert "发布" in result.output

    @patch("textup.cli.main.get_config_manager")
    def test_config_list_command(self, mock_get_config_manager, runner):
        """测试配置列表命令"""
        mock_config_manager = Mock()
        mock_config_manager.load_config = AsyncMock(
            return_value={"platforms": {"weibo": {"enabled": True}}, "general": {"timeout": 30}}
        )
        mock_get_config_manager.return_value = mock_config_manager

        result = runner.invoke(app, ["config", "--list"])
        assert result.exit_code == 0

    @patch("textup.cli.main.get_config_manager")
    def test_config_get_command(self, mock_get_config_manager, runner):
        """测试配置获取命令"""
        mock_config_manager = Mock()
        mock_config_manager.get_config_value = AsyncMock(return_value="test_value")
        mock_get_config_manager.return_value = mock_config_manager

        result = runner.invoke(app, ["config", "--get", "test.key"])
        assert result.exit_code == 0

    @patch("textup.cli.main.get_config_manager")
    def test_config_set_command(self, mock_get_config_manager, runner):
        """测试配置设置命令"""
        mock_config_manager = Mock()
        mock_config_manager.set_config_value = AsyncMock(return_value=True)
        mock_get_config_manager.return_value = mock_config_manager

        result = runner.invoke(app, ["config", "--set", "test.key", "--value", "test_value"])
        assert result.exit_code == 0

    @patch("textup.cli.main.get_config_manager")
    def test_config_backup_command(self, mock_get_config_manager, runner):
        """测试配置备份命令"""
        mock_config_manager = Mock()
        mock_config_manager.backup_config = AsyncMock(return_value=True)
        mock_get_config_manager.return_value = mock_config_manager

        result = runner.invoke(app, ["config", "--backup"])
        assert result.exit_code == 0

    @patch("textup.cli.main.get_config_manager")
    def test_auth_list_command(self, mock_get_config_manager, runner):
        """测试认证列表命令"""
        mock_config_manager = Mock()
        mock_config_manager.get_all_platform_configs = AsyncMock(return_value={})
        mock_get_config_manager.return_value = mock_config_manager

        result = runner.invoke(app, ["auth", "--list"])
        assert result.exit_code == 0

    def test_parse_config_value_boolean(self):
        """测试配置值解析 - 布尔值"""
        assert parse_config_value("true") is True
        assert parse_config_value("false") is False

    def test_parse_config_value_number(self):
        """测试配置值解析 - 数字"""
        assert parse_config_value("42") == 42
        assert parse_config_value("3.14") == 3.14

    def test_parse_config_value_string(self):
        """测试配置值解析 - 字符串"""
        assert parse_config_value("hello world") == "hello world"

    def test_parse_config_value_yaml(self):
        """测试配置值解析 - YAML"""
        yaml_value = "key: value"
        result = parse_config_value(yaml_value)
        assert isinstance(result, dict)
        assert result.get("key") == "value"

    @patch("textup.cli.main.get_content_manager")
    def test_publish_command_basic(self, mock_get_content_manager, runner, temp_dir):
        """测试基本发布命令"""
        # 创建测试文件
        test_file = Path(temp_dir) / "test.md"
        test_file.write_text("# Test Content\nThis is test content.")

        mock_content_manager = Mock()
        mock_content_manager.process_content_file = AsyncMock(return_value=Mock())
        mock_get_content_manager.return_value = mock_content_manager

        result = runner.invoke(app, ["publish", str(test_file)])
        # 发布可能会失败但不应该崩溃
        assert result.exit_code in [0, 1]

    @patch("textup.cli.main.get_content_manager")
    def test_publish_command_dry_run(self, mock_get_content_manager, runner, temp_dir):
        """测试发布预览命令"""
        # 创建测试文件
        test_file = Path(temp_dir) / "test.md"
        test_file.write_text("# Test Content\nThis is test content.")

        mock_content_manager = Mock()
        mock_content_manager.process_content_file = AsyncMock(return_value=Mock())
        mock_get_content_manager.return_value = mock_content_manager

        result = runner.invoke(app, ["publish", str(test_file), "--dry-run"])
        assert result.exit_code in [0, 1]

    def test_get_config_manager_creation(self):
        """测试配置管理器创建"""
        config_mgr = get_config_manager()
        assert isinstance(config_mgr, ConfigManager)

    def test_get_content_manager_creation(self):
        """测试内容管理器创建"""
        content_mgr = get_content_manager()
        assert isinstance(content_mgr, ContentManager)


class TestCLIErrorHandling:
    """CLI错误处理测试"""

    @pytest.fixture
    def runner(self):
        """CLI测试运行器"""
        return CliRunner()

    def test_config_command_error_handling(self, runner):
        """测试配置命令错误处理"""
        # 测试无效的配置操作
        result = runner.invoke(app, ["config", "--get", "nonexistent.key"])
        # 应该有合理的错误处理，不应该崩溃
        assert result.exit_code in [0, 1]

    @patch("textup.cli.main.get_config_manager")
    def test_config_command_exception_handling(self, mock_get_config_manager, runner):
        """测试配置命令异常处理"""
        mock_config_manager = Mock()
        mock_config_manager.load_config = AsyncMock(side_effect=Exception("Test error"))
        mock_get_config_manager.return_value = mock_config_manager

        result = runner.invoke(app, ["config", "--list"])
        assert result.exit_code == 1
        assert "失败" in result.output

    @patch("textup.cli.main.get_config_manager")
    def test_auth_command_exception_handling(self, mock_get_config_manager, runner):
        """测试认证命令异常处理"""
        mock_config_manager = Mock()
        mock_config_manager.get_all_platform_configs = AsyncMock(
            side_effect=Exception("Test error")
        )
        mock_get_config_manager.return_value = mock_config_manager

        result = runner.invoke(app, ["auth", "--list"])
        assert result.exit_code == 1
        assert "失败" in result.output


class TestCLIUtilities:
    """CLI实用工具测试"""

    def test_parse_config_value_edge_cases(self):
        """测试配置值解析边缘情况"""
        # 空字符串
        assert parse_config_value("") == ""

        # 特殊字符
        assert parse_config_value("key: value\nkey2: value2") == {"key": "value", "key2": "value2"}

        # 数字字符串
        assert parse_config_value("123abc") == "123abc"

        # 布尔字符串变体
        assert parse_config_value("TRUE") is True
        assert parse_config_value("False") is False

    def test_display_config_functionality(self):
        """测试配置显示功能"""
        # 这里测试_display_config函数的基本功能
        from textup.cli.main import _display_config

        test_config = {"platforms": {"weibo": {"enabled": True}, "zhihu": {"enabled": False}}}

        # 函数应该能正常处理配置字典而不抛出异常
        try:
            _display_config(test_config)
            success = True
        except Exception:
            success = False
        assert success

    @patch("textup.cli.main.Console")
    def test_console_usage(self, mock_console):
        """测试Console使用"""
        from textup.cli.main import console

        # 验证console对象存在
        assert console is not None

    def test_cli_app_structure(self):
        """测试CLI应用结构"""
        # 验证app对象存在并有基本属性
        assert hasattr(app, "commands") or hasattr(app, "registered_commands")

    @pytest.fixture
    def runner(self):
        """创建CLI测试运行器"""
        return CliRunner()

    @pytest.fixture
    def temp_config_dir(self):
        """创建临时配置目录"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def sample_content_file(self, temp_config_dir):
        """创建示例内容文件"""
        content_file = Path(temp_config_dir) / "test_article.md"
        content_file.write_text(
            """# 测试文章

这是一篇测试文章的内容。

## 子标题

包含一些**粗体**和*斜体*文本。

- 列表项1
- 列表项2

[链接](http://example.com)
""",
            encoding="utf-8",
        )
        return str(content_file)

    def test_app_help(self, runner):
        """测试应用帮助信息"""
        result = runner.invoke(app, ["--help"])
        assert result.exit_code == 0
        assert "TextUp" in result.stdout
        assert "init" in result.stdout
        assert "config" in result.stdout
        assert "publish" in result.stdout

    def test_version_command(self, runner):
        """测试版本命令"""
        result = runner.invoke(app, ["--version"])
        assert result.exit_code == 0
        assert "TextUp" in result.stdout

    def test_init_command_success(self, runner, temp_config_dir):
        """测试初始化命令成功"""
        with patch("textup.cli.main.get_config_manager") as mock_get_manager:
            mock_manager = Mock()
            mock_manager.initialize = AsyncMock(return_value=True)
            mock_get_manager.return_value = mock_manager

            result = runner.invoke(app, ["init", "--config-dir", temp_config_dir])
            assert result.exit_code == 0
            assert "初始化成功" in result.stdout or "成功" in result.stdout

    def test_init_command_with_force(self, runner, temp_config_dir):
        """测试强制初始化命令"""
        # 创建已存在的配置文件
        config_file = Path(temp_config_dir) / "config.yaml"
        config_file.write_text("existing: config")

        with patch("textup.cli.main.get_config_manager") as mock_get_manager:
            mock_manager = Mock()
            mock_manager.initialize = AsyncMock(return_value=True)
            mock_get_manager.return_value = mock_manager

            result = runner.invoke(app, ["init", "--config-dir", temp_config_dir, "--force"])
            assert result.exit_code == 0

    def test_config_show_command(self, runner, temp_config_dir):
        """测试显示配置命令"""
        with patch("textup.cli.main.get_config_manager") as mock_get_manager:
            mock_manager = Mock()
            mock_manager.load_config = AsyncMock(
                return_value={
                    "app": {"name": "TextUp", "version": "1.0"},
                    "platforms": {"zhihu": {"enabled": True}},
                }
            )
            mock_get_manager.return_value = mock_manager

            result = runner.invoke(app, ["config", "show", "--config-dir", temp_config_dir])
            assert result.exit_code == 0

    def test_config_set_command(self, runner, temp_config_dir):
        """测试设置配置命令"""
        with patch("textup.cli.main.get_config_manager") as mock_get_manager:
            mock_manager = Mock()
            mock_manager.set_config_value = AsyncMock(return_value=True)
            mock_get_manager.return_value = mock_manager

            result = runner.invoke(
                app, ["config", "set", "--config-dir", temp_config_dir, "app.name", "New App Name"]
            )
            assert result.exit_code == 0
            mock_manager.set_config_value.assert_called_once_with("app.name", "New App Name")

    def test_config_get_command(self, runner, temp_config_dir):
        """测试获取配置命令"""
        with patch("textup.cli.main.get_config_manager") as mock_get_manager:
            mock_manager = Mock()
            mock_manager.get_config_value = AsyncMock(return_value="TextUp")
            mock_get_manager.return_value = mock_manager

            result = runner.invoke(
                app, ["config", "get", "--config-dir", temp_config_dir, "app.name"]
            )
            assert result.exit_code == 0
            assert "TextUp" in result.stdout

    def test_config_get_nonexistent(self, runner, temp_config_dir):
        """测试获取不存在的配置"""
        with patch("textup.cli.main.get_config_manager") as mock_get_manager:
            mock_manager = Mock()
            mock_manager.get_config_value = AsyncMock(return_value=None)
            mock_get_manager.return_value = mock_manager

            result = runner.invoke(
                app, ["config", "get", "--config-dir", temp_config_dir, "nonexistent.key"]
            )
            assert result.exit_code == 0
            assert "not found" in result.stdout.lower() or "未找到" in result.stdout

    def test_auth_setup_command(self, runner, temp_config_dir):
        """测试认证设置命令"""
        with patch("textup.cli.main.get_config_manager") as mock_get_manager:
            mock_manager = Mock()
            mock_manager.get_platform_config = AsyncMock(return_value={})
            mock_manager.set_config_value = AsyncMock(return_value=True)
            mock_get_manager.return_value = mock_manager

            # 模拟用户输入
            with patch("typer.prompt", return_value="test_value"):
                result = runner.invoke(
                    app, ["auth", "setup", "zhihu", "--config-dir", temp_config_dir]
                )
                assert result.exit_code == 0

    def test_auth_status_command(self, runner, temp_config_dir):
        """测试认证状态命令"""
        with patch("textup.cli.main.get_config_manager") as mock_get_manager:
            mock_manager = Mock()
            mock_manager.get_platform_config = AsyncMock(
                return_value={"client_id": "test_id", "access_token": "test_token"}
            )
            mock_get_manager.return_value = mock_manager

            result = runner.invoke(
                app, ["auth", "status", "zhihu", "--config-dir", temp_config_dir]
            )
            assert result.exit_code == 0

    def test_auth_status_not_configured(self, runner, temp_config_dir):
        """测试未配置的认证状态"""
        with patch("textup.cli.main.get_config_manager") as mock_get_manager:
            mock_manager = Mock()
            mock_manager.get_platform_config = AsyncMock(return_value=None)
            mock_get_manager.return_value = mock_manager

            result = runner.invoke(
                app, ["auth", "status", "zhihu", "--config-dir", temp_config_dir]
            )
            assert result.exit_code == 0
            assert "未配置" in result.stdout or "not configured" in result.stdout.lower()

    def test_publish_command_with_file(self, runner, temp_config_dir, sample_content_file):
        """测试使用文件发布命令"""
        with (
            patch("src.textup.cli.main.get_config_manager") as mock_get_config,
            patch("src.textup.cli.main.get_content_manager") as mock_get_content,
            patch("src.textup.cli.main.get_publish_engine") as mock_get_engine,
        ):

            # 模拟配置管理器
            mock_config_manager = Mock()
            mock_config_manager.load_config = AsyncMock(return_value={"app": {"name": "TextUp"}})
            mock_get_config.return_value = mock_config_manager

            # 模拟内容管理器
            mock_content_manager = Mock()
            mock_content_manager.load_content_from_file = AsyncMock(
                return_value=Content(title="测试文章", content="测试内容")
            )
            mock_get_content.return_value = mock_content_manager

            # 模拟发布引擎
            mock_engine = Mock()
            mock_engine.publish = AsyncMock(return_value=[])
            mock_get_engine.return_value = mock_engine

            result = runner.invoke(
                app,
                [
                    "publish",
                    sample_content_file,
                    "--platform",
                    "zhihu",
                    "--config-dir",
                    temp_config_dir,
                ],
            )
            assert result.exit_code == 0

    def test_publish_command_with_preview(self, runner, temp_config_dir, sample_content_file):
        """测试预览发布命令"""
        with patch("textup.cli.main.get_content_manager") as mock_get_content:
            mock_content_manager = Mock()
            mock_content_manager.load_content_from_file = AsyncMock(
                return_value=Content(title="测试文章", content="测试内容")
            )
            mock_content_manager.validate_content = AsyncMock(
                return_value=Mock(is_valid=True, errors=[])
            )
            mock_get_content.return_value = mock_content_manager

            result = runner.invoke(
                app,
                [
                    "publish",
                    sample_content_file,
                    "--platform",
                    "zhihu",
                    "--preview",
                    "--config-dir",
                    temp_config_dir,
                ],
            )
            assert result.exit_code == 0
            assert "预览" in result.stdout or "Preview" in result.stdout

    def test_publish_command_with_invalid_file(self, runner, temp_config_dir):
        """测试发布不存在的文件"""
        result = runner.invoke(
            app,
            [
                "publish",
                "nonexistent_file.md",
                "--platform",
                "zhihu",
                "--config-dir",
                temp_config_dir,
            ],
        )
        assert result.exit_code != 0

    def test_publish_command_with_invalid_platform(
        self, runner, temp_config_dir, sample_content_file
    ):
        """测试使用无效平台发布"""
        result = runner.invoke(
            app,
            [
                "publish",
                sample_content_file,
                "--platform",
                "invalid_platform",
                "--config-dir",
                temp_config_dir,
            ],
        )
        assert result.exit_code != 0

    def test_status_command(self, runner, temp_config_dir):
        """测试状态命令"""
        with patch("textup.cli.main.get_publish_engine") as mock_get_engine:
            mock_engine = Mock()
            mock_engine.get_all_tasks = AsyncMock(return_value=[])
            mock_get_engine.return_value = mock_engine

            result = runner.invoke(app, ["status", "--config-dir", temp_config_dir])
            assert result.exit_code == 0

    def test_status_command_with_tasks(self, runner, temp_config_dir):
        """测试有任务的状态命令"""
        with patch("textup.cli.main.get_publish_engine") as mock_get_engine:
            mock_task = Mock()
            mock_task.id = "task_123"
            mock_task.status = "completed"
            mock_task.content.title = "测试文章"
            mock_task.created_at = "2024-01-01T00:00:00"

            mock_engine = Mock()
            mock_engine.get_all_tasks = AsyncMock(return_value=[mock_task])
            mock_get_engine.return_value = mock_engine

            result = runner.invoke(app, ["status", "--config-dir", temp_config_dir])
            assert result.exit_code == 0
            assert "task_123" in result.stdout

    def test_history_command(self, runner, temp_config_dir):
        """测试历史记录命令"""
        with patch("textup.cli.main.get_publish_engine") as mock_get_engine:
            mock_engine = Mock()
            mock_engine.get_publish_history = AsyncMock(return_value=[])
            mock_get_engine.return_value = mock_engine

            result = runner.invoke(app, ["history", "--config-dir", temp_config_dir])
            assert result.exit_code == 0

    def test_history_command_with_limit(self, runner, temp_config_dir):
        """测试限制数量的历史记录命令"""
        with patch("textup.cli.main.get_publish_engine") as mock_get_engine:
            mock_record = Mock()
            mock_record.id = "record_123"
            mock_record.title = "历史文章"
            mock_record.platform = "zhihu"
            mock_record.status = "published"
            mock_record.published_at = "2024-01-01T00:00:00"

            mock_engine = Mock()
            mock_engine.get_publish_history = AsyncMock(return_value=[mock_record])
            mock_get_engine.return_value = mock_engine

            result = runner.invoke(
                app, ["history", "--limit", "5", "--config-dir", temp_config_dir]
            )
            assert result.exit_code == 0

    def test_debug_option(self, runner, temp_config_dir):
        """测试调试选项"""
        result = runner.invoke(app, ["init", "--config-dir", temp_config_dir, "--debug"])
        # 调试模式应该显示更多信息
        assert result.exit_code == 0

    def test_config_dir_option_default(self, runner):
        """测试默认配置目录选项"""
        with patch("textup.cli.main.get_config_manager") as mock_get_manager:
            mock_manager = Mock()
            mock_manager.load_config = AsyncMock(return_value={"app": {"name": "TextUp"}})
            mock_get_manager.return_value = mock_manager

            result = runner.invoke(app, ["config", "show"])
            assert result.exit_code == 0

    def test_quiet_option(self, runner, temp_config_dir):
        """测试静默选项"""
        with patch("src.textup.cli.main.get_config_manager") as mock_get_manager:
            mock_manager = Mock()
            mock_manager.initialize = AsyncMock(return_value=True)
            mock_get_manager.return_value = mock_manager

            result = runner.invoke(app, ["init", "--config-dir", temp_config_dir, "--quiet"])
            assert result.exit_code == 0

    def test_invalid_command(self, runner):
        """测试无效命令"""
        result = runner.invoke(app, ["invalid_command"])
        assert result.exit_code != 0

    def test_publish_command_with_schedule(self, runner, temp_config_dir, sample_content_file):
        """测试定时发布命令"""
        with (
            patch("textup.cli.main.get_content_manager") as mock_get_content,
            patch("textup.cli.main.get_publish_engine") as mock_get_engine,
        ):

            mock_content_manager = Mock()
            mock_content_manager.load_content_from_file = AsyncMock(
                return_value=Content(title="定时发布文章", content="定时发布内容")
            )
            mock_get_content.return_value = mock_content_manager

            mock_engine = Mock()
            mock_engine.schedule_publish = AsyncMock(return_value="task_123")
            mock_get_engine.return_value = mock_engine

            result = runner.invoke(
                app,
                [
                    "publish",
                    sample_content_file,
                    "--platform",
                    "zhihu",
                    "--schedule",
                    "2024-12-31T23:59:59",
                    "--config-dir",
                    temp_config_dir,
                ],
            )
            assert result.exit_code == 0


class TestCLIHelpers:
    """CLI辅助函数测试"""

    def test_parse_config_value_string(self):
        """测试解析字符串配置值"""
        result = parse_config_value("test_string")
        assert result == "test_string"

    def test_parse_config_value_boolean(self):
        """测试解析布尔配置值"""
        assert parse_config_value("true") is True
        assert parse_config_value("false") is False
        assert parse_config_value("True") is True
        assert parse_config_value("False") is False

    def test_parse_config_value_integer(self):
        """测试解析整数配置值"""
        assert parse_config_value("123") == 123
        assert parse_config_value("-456") == -456

    def test_parse_config_value_float(self):
        """测试解析浮点数配置值"""
        assert parse_config_value("3.14") == 3.14
        assert parse_config_value("-2.5") == -2.5

    def test_parse_config_value_json(self):
        """测试解析JSON配置值"""
        json_str = '{"key": "value", "number": 42}'
        result = parse_config_value(json_str)
        assert isinstance(result, dict)
        assert result["key"] == "value"
        assert result["number"] == 42

    def test_parse_config_value_list(self):
        """测试解析列表配置值"""
        list_str = '["item1", "item2", "item3"]'
        result = parse_config_value(list_str)
        assert isinstance(result, list)
        assert len(result) == 3

    @patch("textup.cli.main.ConfigManager")
    def test_get_config_manager(self, mock_config_manager_class):
        """测试获取配置管理器"""
        mock_manager = Mock()
        mock_config_manager_class.return_value = mock_manager

        result = get_config_manager("/test/config")
        assert result == mock_manager
        mock_config_manager_class.assert_called_once_with("/test/config")

    @patch("textup.cli.main.ContentManager")
    def test_get_content_manager(self, mock_content_manager_class):
        """测试获取内容管理器"""
        mock_manager = Mock()
        mock_content_manager_class.return_value = mock_manager

        result = get_content_manager()
        assert result == mock_manager
        mock_content_manager_class.assert_called_once()


class TestCLIErrorHandling:
    """CLI错误处理测试"""

    @pytest.fixture
    def runner(self):
        return CliRunner()

    @pytest.fixture
    def temp_config_dir(self):
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)

    def test_config_error_handling(self, runner, temp_config_dir):
        """测试配置错误处理"""
        with patch("textup.cli.main.get_config_manager") as mock_get_manager:
            mock_manager = Mock()
            mock_manager.load_config = AsyncMock(side_effect=ConfigurationError("配置文件损坏"))
            mock_get_manager.return_value = mock_manager

            result = runner.invoke(app, ["config", "show", "--config-dir", temp_config_dir])
            assert result.exit_code != 0
            assert "配置" in result.stdout or "错误" in result.stdout

    def test_content_error_handling(self, runner, temp_config_dir):
        """测试内容错误处理"""
        with patch("textup.cli.main.get_content_manager") as mock_get_content:
            mock_content_manager = Mock()
            mock_content_manager.load_content_from_file = AsyncMock(
                side_effect=ContentError("文件格式不支持")
            )
            mock_get_content.return_value = mock_content_manager

            result = runner.invoke(
                app, ["publish", "test.txt", "--platform", "zhihu", "--config-dir", temp_config_dir]
            )
            assert result.exit_code != 0

    def test_permission_error_handling(self, runner):
        """测试权限错误处理"""
        result = runner.invoke(app, ["init", "--config-dir", "/root/forbidden"])
        # 应该优雅地处理权限错误
        assert result.exit_code != 0

    def test_keyboard_interrupt_handling(self, runner, temp_config_dir):
        """测试键盘中断处理"""
        with patch("textup.cli.main.get_config_manager") as mock_get_manager:
            mock_manager = Mock()
            mock_manager.initialize = AsyncMock(side_effect=KeyboardInterrupt())
            mock_get_manager.return_value = mock_manager

            result = runner.invoke(app, ["init", "--config-dir", temp_config_dir])
            # 应该优雅地处理键盘中断
            assert result.exit_code != 0


class TestCLIIntegration:
    """CLI集成测试"""

    @pytest.fixture
    def runner(self):
        return CliRunner()

    @pytest.fixture
    def temp_workspace(self):
        """创建临时工作空间"""
        temp_dir = tempfile.mkdtemp()

        # 创建测试文件结构
        content_dir = Path(temp_dir) / "content"
        content_dir.mkdir()

        config_dir = Path(temp_dir) / "config"
        config_dir.mkdir()

        # 创建测试文章
        test_article = content_dir / "test_article.md"
        test_article.write_text(
            """# 集成测试文章

这是一篇用于集成测试的文章。

## 主要内容

包含以下要素：
- **粗体文本**
- *斜体文本*
- [外部链接](https://example.com)
- 代码块

```python
print("Hello, TextUp!")
```

## 标签

Python, 测试, 集成
""",
            encoding="utf-8",
        )

        yield {
            "root": temp_dir,
            "content": str(content_dir),
            "config": str(config_dir),
            "article": str(test_article),
        }

        shutil.rmtree(temp_dir)

    def test_full_workflow_simulation(self, runner, temp_workspace):
        """测试完整工作流程模拟"""
        config_dir = temp_workspace["config"]
        article_file = temp_workspace["article"]

        # 1. 初始化项目
        with patch("textup.cli.main.get_config_manager") as mock_get_config:
            mock_config_manager = Mock()
            mock_config_manager.initialize = AsyncMock(return_value=True)
            mock_get_config.return_value = mock_config_manager

            result = runner.invoke(app, ["init", "--config-dir", config_dir])
            assert result.exit_code == 0

        # 2. 设置平台配置
        with patch("textup.cli.main.get_config_manager") as mock_get_config:
            mock_config_manager = Mock()
            mock_config_manager.set_config_value = AsyncMock(return_value=True)
            mock_get_config.return_value = mock_config_manager

            result = runner.invoke(
                app,
                ["config", "set", "--config-dir", config_dir, "platforms.zhihu.enabled", "true"],
            )
            assert result.exit_code == 0

        # 3. 预览发布
        with patch("textup.cli.main.get_content_manager") as mock_get_content:
            mock_content_manager = Mock()
            mock_content_manager.load_content_from_file = AsyncMock(
                return_value=Content(
                    title="集成测试文章",
                    content="这是一篇用于集成测试的文章。",
                    tags=["Python", "测试", "集成"],
                )
            )
            mock_content_manager.validate_content = AsyncMock(
                return_value=Mock(is_valid=True, errors=[])
            )
            mock_get_content.return_value = mock_content_manager

            result = runner.invoke(
                app,
                [
                    "publish",
                    article_file,
                    "--platform",
                    "zhihu",
                    "--preview",
                    "--config-dir",
                    config_dir,
                ],
            )
            assert result.exit_code == 0

        # 4. 检查状态
        with patch("textup.cli.main.get_publish_engine") as mock_get_engine:
            mock_engine = Mock()
            mock_engine.get_all_tasks = AsyncMock(return_value=[])
            mock_get_engine.return_value = mock_engine

            result = runner.invoke(app, ["status", "--config-dir", config_dir])
            assert result.exit_code == 0
