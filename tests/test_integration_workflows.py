"""
Integration test workflows for TextUp application.

This module contains integration tests that verify interactions between
different components using synchronous patterns.
"""

import pytest
import tempfile
import os
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import yaml

from textup.models import (
    Content,
    Platform,
    ContentFormat,
    TaskStatus,
    ValidationResult,
    PublishResult,
    AuthResult,
)
from textup.services.config_manager import ConfigManager
from textup.utils.exceptions import (
    ConfigurationError,
    PublishError,
    NetworkError,
    AuthenticationError,
)


class TestConfigurationIntegration:
    """Configuration system integration tests"""

    def test_config_manager_full_workflow(self):
        """Test complete configuration management workflow"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_manager = ConfigManager(temp_dir)

            # Test initial state
            assert str(config_manager.config_dir) == temp_dir

            # Test configuration file creation
            test_config = {
                "platforms": {
                    "weibo": {
                        "enabled": True,
                        "credentials": {"app_key": "test_key", "app_secret": "test_secret"},
                    },
                    "zhihu": {"enabled": False},
                },
                "general": {"timeout": 30, "retry_attempts": 3},
            }

            # Save configuration
            config_path = Path(temp_dir) / "config.yaml"
            with open(config_path, "w") as f:
                yaml.dump(test_config, f)

            # Verify file exists
            assert config_path.exists()

            # Test configuration loading
            with open(config_path, "r") as f:
                loaded_config = yaml.safe_load(f)

            assert loaded_config["platforms"]["weibo"]["enabled"] is True
            assert loaded_config["platforms"]["zhihu"]["enabled"] is False
            assert loaded_config["general"]["timeout"] == 30

    def test_config_validation_integration(self):
        """Test configuration validation integration"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_manager = ConfigManager(temp_dir)

            # Test invalid configuration handling
            invalid_config = {"platforms": {"weibo": {"enabled": "not_boolean"}}}  # Invalid type

            config_path = Path(temp_dir) / "invalid_config.yaml"
            with open(config_path, "w") as f:
                yaml.dump(invalid_config, f)

            # Configuration should exist but validation might fail
            assert config_path.exists()


class TestContentProcessingIntegration:
    """Content processing integration tests"""

    def test_content_lifecycle_integration(self):
        """Test complete content lifecycle from creation to validation"""
        # Create test content
        content = Content(
            title="Integration Test Content",
            content="This is a test content for integration testing.",
        )

        # Verify content creation
        assert content.title == "Integration Test Content"
        assert content.content == "This is a test content for integration testing."

        # Test content validation
        validation_result = ValidationResult(is_valid=True, errors=[], warnings=[])

        assert validation_result.is_valid is True
        assert len(validation_result.errors) == 0

    def test_content_format_handling_integration(self):
        """Test content format handling across different platforms"""
        content = Content(
            title="Multi-platform Content", content="Content that should work across platforms."
        )

        # Test different content formats
        formats = [ContentFormat.TEXT, ContentFormat.MARKDOWN]

        for content_format in formats:
            # Each format should be valid for processing
            assert content_format in ContentFormat.__members__.values()

    def test_platform_content_adaptation(self):
        """Test content adaptation for different platforms"""
        base_content = Content(
            title="Platform Adaptation Test",
            content="This content needs platform-specific formatting.",
        )

        platforms = [Platform.WEIBO, Platform.ZHIHU]

        for platform in platforms:
            # Test platform-specific adaptations
            adapted_content = Content(
                title=f"{base_content.title} - {platform.value}", content=base_content.content
            )

            assert adapted_content.title.endswith(f"- {platform.value}")


class TestPublishingIntegration:
    """Publishing workflow integration tests"""

    def test_publish_result_integration(self):
        """Test publish result handling integration"""
        # Test successful publish result
        success_result = PublishResult(
            success=True,
            platform=Platform.WEIBO,
            platform_post_id="test_post_123",
            publish_url="https://weibo.com/test_post_123",
        )

        assert success_result.success is True
        assert success_result.platform == Platform.WEIBO
        assert success_result.platform_post_id == "test_post_123"
        assert success_result.publish_url.startswith("https://")

        # Test failed publish result
        error_result = PublishResult(
            success=False, platform=Platform.ZHIHU, error_message="Authentication failed"
        )

        assert error_result.success is False
        assert error_result.platform == Platform.ZHIHU
        assert "Authentication" in error_result.error_message

    def test_task_status_workflow_integration(self):
        """Test task status workflow integration"""
        statuses = [TaskStatus.PENDING, TaskStatus.RUNNING, TaskStatus.COMPLETED, TaskStatus.FAILED]

        # Test status progression
        for status in statuses:
            assert status in TaskStatus.__members__.values()

        # Test status transitions
        current_status = TaskStatus.PENDING
        assert current_status == TaskStatus.PENDING

        # Progress to running
        current_status = TaskStatus.RUNNING
        assert current_status == TaskStatus.RUNNING

        # Complete successfully
        current_status = TaskStatus.COMPLETED
        assert current_status == TaskStatus.COMPLETED

    @patch("textup.adapters.weibo.WeiboAdapter")
    @patch("textup.adapters.zhihu.ZhihuAdapter")
    def test_multi_platform_publishing_integration(self, mock_zhihu, mock_weibo):
        """Test multi-platform publishing integration with mocked adapters"""
        # Setup mock adapters
        mock_weibo_instance = Mock()
        mock_weibo_instance.publish.return_value = PublishResult(
            success=True, platform=Platform.WEIBO, platform_post_id="wb123"
        )
        mock_weibo.return_value = mock_weibo_instance

        mock_zhihu_instance = Mock()
        mock_zhihu_instance.publish.return_value = PublishResult(
            success=True, platform=Platform.ZHIHU, platform_post_id="zh456"
        )
        mock_zhihu.return_value = mock_zhihu_instance

        # Test content
        content = Content(title="Multi-platform Test", content="Content for multiple platforms")

        # Simulate publishing to multiple platforms
        results = []

        # Publish to Weibo
        weibo_adapter = mock_weibo()
        weibo_result = weibo_adapter.publish(content)
        results.append(weibo_result)

        # Publish to Zhihu
        zhihu_adapter = mock_zhihu()
        zhihu_result = zhihu_adapter.publish(content)
        results.append(zhihu_result)

        # Verify results
        assert len(results) == 2
        assert all(result.success for result in results)
        assert results[0].platform == Platform.WEIBO
        assert results[1].platform == Platform.ZHIHU


class TestAuthenticationIntegration:
    """Authentication system integration tests"""

    def test_auth_result_integration(self):
        """Test authentication result integration"""
        # Test successful authentication
        success_auth = AuthResult(
            success=True,
            platform=Platform.WEIBO,
            user_id="test_user_123",
            auth_data={"token": "test_token_123", "expires_in": 3600},
        )

        assert success_auth.success is True
        assert success_auth.platform == Platform.WEIBO
        assert success_auth.user_id == "test_user_123"
        assert success_auth.auth_data["token"] == "test_token_123"

        # Test failed authentication
        failed_auth = AuthResult(
            success=False, platform=Platform.ZHIHU, error_message="Invalid credentials"
        )

        assert failed_auth.success is False
        assert failed_auth.platform == Platform.ZHIHU
        assert "Invalid" in failed_auth.error_message

    def test_platform_auth_workflow_integration(self):
        """Test platform authentication workflow integration"""
        platforms = [Platform.WEIBO, Platform.ZHIHU]

        for platform in platforms:
            # Test authentication workflow for each platform
            auth_result = AuthResult(
                success=True, platform=platform, auth_data={"token": f"token_for_{platform.value}"}
            )

            assert auth_result.platform == platform
            assert platform.value in auth_result.auth_data["token"]


class TestErrorHandlingIntegration:
    """Error handling integration tests"""

    def test_exception_hierarchy_integration(self):
        """Test exception hierarchy integration"""
        # Test different types of exceptions
        config_error = ConfigurationError("Config not found")
        publish_error = PublishError("Publish failed")
        network_error = NetworkError("Connection timeout")
        auth_error = AuthenticationError("Invalid credentials")

        exceptions = [config_error, publish_error, network_error, auth_error]

        for exception in exceptions:
            assert isinstance(exception, Exception)
            assert len(str(exception)) > 0

    def test_validation_error_integration(self):
        """Test validation error integration"""
        from textup.models import ValidationError as ModelValidationError

        validation_errors = [
            ModelValidationError(field="title", message="Title is required"),
            ModelValidationError(field="content", message="Content is too short"),
        ]

        validation_result = ValidationResult(is_valid=False, errors=validation_errors)

        assert validation_result.is_valid is False
        assert len(validation_result.errors) == 2
        assert validation_result.errors[0].field == "title"
        assert validation_result.errors[0].message == "Title is required"
        assert validation_result.errors[1].field == "content"
        assert validation_result.errors[1].message == "Content is too short"


class TestSystemIntegration:
    """System-wide integration tests"""

    def test_component_interaction_integration(self):
        """Test interaction between different system components"""
        # Test configuration and content interaction
        with tempfile.TemporaryDirectory() as temp_dir:
            config_manager = ConfigManager(temp_dir)

            # Create content
            content = Content(
                title="System Integration Test", content="Testing component interactions"
            )

            # Test that components can work together
            assert str(config_manager.config_dir) == temp_dir
            assert content.title == "System Integration Test"

            # Both components should be functional
            assert isinstance(content, Content)
            assert isinstance(config_manager, ConfigManager)

    def test_end_to_end_workflow_simulation(self):
        """Simulate end-to-end workflow without external dependencies"""
        # Step 1: Create content
        content = Content(title="E2E Test Content", content="End-to-end testing content")

        # Step 2: Validate content
        validation = ValidationResult(is_valid=True, errors=[], warnings=[])

        # Step 3: Simulate authentication
        auth_result = AuthResult(
            success=True, platform=Platform.WEIBO, auth_data={"token": "e2e_test_token"}
        )

        # Step 4: Simulate publishing
        publish_result = PublishResult(
            success=True,
            platform=Platform.WEIBO,
            platform_post_id="e2e_123",
            publish_url="https://weibo.com/e2e_123",
        )

        # Verify complete workflow
        assert content.title == "E2E Test Content"
        assert validation.is_valid is True
        assert auth_result.success is True
        assert publish_result.success is True
        assert publish_result.platform == Platform.WEIBO


# Utility functions for integration testing
def create_test_content(title="Test Content", content="Test content body"):
    """Create standardized test content"""
    return Content(title=title, content=content)


def create_mock_config(platforms=None):
    """Create mock configuration data"""
    if platforms is None:
        platforms = ["weibo", "zhihu"]

    return {
        "platforms": {
            platform: {"enabled": True, "credentials": {"key": f"test_{platform}_key"}}
            for platform in platforms
        },
        "general": {"timeout": 30, "retry_attempts": 3},
    }


def assert_publish_success(result, platform, post_id=None):
    """Assert successful publish result"""
    assert isinstance(result, PublishResult)
    assert result.success is True
    assert result.platform == platform
    if post_id:
        assert result.platform_post_id == post_id


def assert_validation_success(result):
    """Assert successful validation result"""
    assert isinstance(result, ValidationResult)
    assert result.is_valid is True
    assert len(result.errors) == 0


# Test fixtures
@pytest.fixture
def temp_config_dir():
    """Provide temporary configuration directory"""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


@pytest.fixture
def sample_content():
    """Provide sample content for testing"""
    return Content(
        title="Sample Integration Test Content",
        content="This is sample content for integration testing.",
    )


@pytest.fixture
def mock_successful_auth():
    """Provide mock successful authentication"""
    return AuthResult(
        success=True,
        platform=Platform.WEIBO,
        auth_data={"token": "mock_auth_token_123", "expires_in": 3600},
    )


# Test markers
pytestmark = pytest.mark.integration
