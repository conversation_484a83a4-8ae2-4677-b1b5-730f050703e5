"""
TextUp 服务层综合测试

本模块包含对所有服务层组件的全面测试，旨在提升测试覆盖率并验证核心功能。
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
import asyncio
from datetime import datetime

import sys
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent / "src"))

from textup.services.config_manager import ConfigManager
from textup.services.content_manager import ContentManager
from textup.services.publish_engine import PublishEngine
from textup.services.error_handler import ErrorHandler, RetryPolicy, CircuitBreaker
from textup.models import (
    Content,
    ContentFormat,
    Platform,
    TaskStatus,
    PublishTask,
    PublishResult,
    AuthResult,
    ValidationResult,
    ValidationError,
)
from textup.utils.exceptions import (
    ConfigurationError,
    ContentError,
    PublishError,
    AuthenticationError,
    ValidationError as ValidationException,
)


class TestConfigManagerComprehensive:
    """配置管理器综合测试"""

    @pytest.fixture
    def temp_config_dir(self):
        """创建临时配置目录"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def config_manager(self, temp_config_dir):
        """创建配置管理器实例"""
        return ConfigManager(temp_config_dir)

    def test_initialization_creates_directory(self, temp_config_dir):
        """测试初始化时创建配置目录"""
        config_dir = Path(temp_config_dir) / "new_config"
        manager = ConfigManager(str(config_dir))
        assert config_dir.exists()
        assert config_dir.is_dir()

    @pytest.mark.asyncio
    async def test_load_config_creates_default(self, config_manager):
        """测试加载配置时创建默认配置"""
        config = await config_manager.load_config()
        assert isinstance(config, dict)
        assert "app" in config
        assert "platforms" in config
        assert config["app"]["name"] == "TextUp"

    @pytest.mark.asyncio
    async def test_save_and_load_config(self, config_manager):
        """测试保存和加载配置"""
        test_config = {
            "app": {"name": "Test App", "version": "1.0"},
            "platforms": {"zhihu": {"enabled": True}},
        }

        await config_manager.save_config(test_config)
        loaded_config = await config_manager.load_config()

        assert loaded_config["app"]["name"] == "Test App"
        assert loaded_config["platforms"]["zhihu"]["enabled"] is True

    @pytest.mark.asyncio
    async def test_set_and_get_config_value(self, config_manager):
        """测试设置和获取配置值"""
        # 设置简单值
        await config_manager.set_config_value("app.debug", True)
        value = await config_manager.get_config_value("app.debug")
        assert value is True

        # 设置嵌套值
        await config_manager.set_config_value("platforms.zhihu.client_id", "test123")
        value = await config_manager.get_config_value("platforms.zhihu.client_id")
        assert value == "test123"

    @pytest.mark.asyncio
    async def test_get_nonexistent_config_value(self, config_manager):
        """测试获取不存在的配置值"""
        value = await config_manager.get_config_value("nonexistent.key")
        assert value is None

        # 带默认值
        value = await config_manager.get_config_value("nonexistent.key", "default")
        assert value == "default"

    @pytest.mark.asyncio
    async def test_validate_config(self, config_manager):
        """测试配置验证"""
        # 加载默认配置
        await config_manager.load_config()

        is_valid, errors = await config_manager.validate_config()
        assert is_valid is True
        assert len(errors) == 0

    @pytest.mark.asyncio
    async def test_get_platform_config(self, config_manager):
        """测试获取平台配置"""
        await config_manager.set_config_value("platforms.zhihu.client_id", "test123")

        zhihu_config = await config_manager.get_platform_config(Platform.ZHIHU)
        assert zhihu_config is not None
        assert "client_id" in zhihu_config

    @pytest.mark.asyncio
    async def test_backup_and_restore(self, config_manager):
        """测试配置备份和恢复"""
        # 设置一些配置
        await config_manager.set_config_value("app.name", "Backup Test")

        # 创建备份
        backup_path = await config_manager.backup_config()
        assert Path(backup_path).exists()

        # 修改配置
        await config_manager.set_config_value("app.name", "Modified")

        # 恢复备份
        await config_manager.restore_config(backup_path)

        # 验证恢复
        value = await config_manager.get_config_value("app.name")
        assert value == "Backup Test"


class TestContentManagerComprehensive:
    """内容管理器综合测试"""

    @pytest.fixture
    def content_manager(self):
        """创建内容管理器实例"""
        return ContentManager()

    @pytest.fixture
    def sample_content(self):
        """创建示例内容"""
        return Content(
            title="测试文章",
            content="这是一篇**测试文章**，包含[链接](http://example.com)和图片![alt](image.jpg)。",
            content_format=ContentFormat.MARKDOWN,
            tags=["测试", "markdown"],
        )

    @pytest.mark.asyncio
    async def test_parse_content_markdown(self, content_manager):
        """测试Markdown内容解析"""
        markdown_text = """# 标题

这是正文内容，包含**粗体**和*斜体*。

- 列表项1
- 列表项2

[链接](http://example.com)
![图片](image.jpg)
"""

        result = await content_manager.parse_content(markdown_text, ContentFormat.MARKDOWN)
        assert result is not None
        assert result.title == "标题"
        assert "正文内容" in result.content
        assert len(result.images) > 0
        assert len(result.links) > 0

    @pytest.mark.asyncio
    async def test_validate_content_success(self, content_manager, sample_content):
        """测试内容验证成功"""
        result = await content_manager.validate_content(sample_content)
        assert result.is_valid is True
        assert len(result.errors) == 0

    @pytest.mark.asyncio
    async def test_validate_content_failure(self, content_manager):
        """测试内容验证失败"""
        invalid_content = Content(
            title="", content="内容过短", tags=["tag"] * 25  # 空标题  # 内容太短  # 标签过多
        )

        result = await content_manager.validate_content(invalid_content)
        assert result.is_valid is False
        assert len(result.errors) > 0

    @pytest.mark.asyncio
    async def test_transform_content_to_platform(self, content_manager, sample_content):
        """测试内容平台转换"""
        # 转换为知乎格式
        zhihu_content = await content_manager.transform_content(sample_content, Platform.ZHIHU)
        assert zhihu_content is not None
        assert zhihu_content.platform == Platform.ZHIHU

        # 转换为微博格式
        weibo_content = await content_manager.transform_content(sample_content, Platform.WEIBO)
        assert weibo_content is not None
        assert weibo_content.platform == Platform.WEIBO

    @pytest.mark.asyncio
    async def test_extract_images_and_links(self, content_manager):
        """测试提取图片和链接"""
        content_with_media = """
        看看这张图片 ![测试图片](https://example.com/image1.jpg)
        还有这个链接 [百度](https://baidu.com)
        另一张图片 ![另一张](./local_image.png)
        """

        images = await content_manager.extract_images(content_with_media)
        links = await content_manager.extract_links(content_with_media)

        assert len(images) == 2
        assert len(links) == 1
        assert "https://example.com/image1.jpg" in images
        assert "https://baidu.com" in links

    @pytest.mark.asyncio
    async def test_content_format_conversion(self, content_manager):
        """测试内容格式转换"""
        markdown_text = "# 标题\n\n这是**粗体**文本。"

        # Markdown to HTML
        html_result = await content_manager.convert_format(
            markdown_text, ContentFormat.MARKDOWN, ContentFormat.HTML
        )
        assert "<h1>" in html_result
        assert "<strong>" in html_result

        # Markdown to Text
        text_result = await content_manager.convert_format(
            markdown_text, ContentFormat.MARKDOWN, ContentFormat.TEXT
        )
        assert "标题" in text_result
        assert "粗体" in text_result
        assert "<" not in text_result

    @pytest.mark.asyncio
    async def test_content_statistics(self, content_manager, sample_content):
        """测试内容统计"""
        stats = await content_manager.calculate_statistics(sample_content)

        assert stats.word_count > 0
        assert stats.char_count > 0
        assert stats.estimated_read_time > 0
        assert stats.image_count >= 0

    @pytest.mark.asyncio
    async def test_batch_processing(self, content_manager):
        """测试批量处理"""
        contents = [Content(title=f"文章{i}", content=f"内容{i}" * 10) for i in range(5)]

        results = await content_manager.batch_process(contents)
        assert len(results) == 5

        for result in results:
            assert result.is_valid is True


class TestPublishEngineComprehensive:
    """发布引擎综合测试"""

    @pytest.fixture
    def mock_adapters(self):
        """创建模拟适配器"""
        zhihu_adapter = Mock()
        zhihu_adapter.authenticate = AsyncMock(return_value=AuthResult(success=True))
        zhihu_adapter.publish = AsyncMock(
            return_value=PublishResult(
                success=True, platform=Platform.ZHIHU, platform_post_id="zhihu_123"
            )
        )

        weibo_adapter = Mock()
        weibo_adapter.authenticate = AsyncMock(return_value=AuthResult(success=True))
        weibo_adapter.publish = AsyncMock(
            return_value=PublishResult(
                success=True, platform=Platform.WEIBO, platform_post_id="weibo_456"
            )
        )

        return {Platform.ZHIHU: zhihu_adapter, Platform.WEIBO: weibo_adapter}

    @pytest.fixture
    def publish_engine(self, mock_adapters):
        """创建发布引擎实例"""
        engine = PublishEngine()
        engine.adapters = mock_adapters
        return engine

    @pytest.fixture
    def sample_task(self):
        """创建示例发布任务"""
        return PublishTask(
            content=Content(title="测试发布", content="测试发布内容"),
            platforms=[Platform.ZHIHU, Platform.WEIBO],
            schedule_time=None,
        )

    @pytest.mark.asyncio
    async def test_single_platform_publish(self, publish_engine, sample_task):
        """测试单平台发布"""
        task = PublishTask(content=sample_task.content, platforms=[Platform.ZHIHU])

        results = await publish_engine.publish(task)

        assert len(results) == 1
        assert results[0].success is True
        assert results[0].platform == Platform.ZHIHU

    @pytest.mark.asyncio
    async def test_multi_platform_publish(self, publish_engine, sample_task):
        """测试多平台发布"""
        results = await publish_engine.publish(sample_task)

        assert len(results) == 2
        platforms = [r.platform for r in results]
        assert Platform.ZHIHU in platforms
        assert Platform.WEIBO in platforms

    @pytest.mark.asyncio
    async def test_publish_with_authentication_failure(self, publish_engine, sample_task):
        """测试认证失败的发布"""
        # 设置知乎认证失败
        publish_engine.adapters[Platform.ZHIHU].authenticate.return_value = AuthResult(
            success=False, error_message="认证失败"
        )

        results = await publish_engine.publish(sample_task)

        # 应该有两个结果：一个失败（知乎），一个成功（微博）
        assert len(results) == 2
        zhihu_result = next(r for r in results if r.platform == Platform.ZHIHU)
        assert zhihu_result.success is False

    @pytest.mark.asyncio
    async def test_publish_with_content_error(self, publish_engine, sample_task):
        """测试内容发布错误"""
        # 设置微博发布失败
        publish_engine.adapters[Platform.WEIBO].publish.return_value = PublishResult(
            success=False, platform=Platform.WEIBO, error_message="内容格式错误"
        )

        results = await publish_engine.publish(sample_task)

        assert len(results) == 2
        weibo_result = next(r for r in results if r.platform == Platform.WEIBO)
        assert weibo_result.success is False
        assert "内容格式错误" in weibo_result.error_message

    @pytest.mark.asyncio
    async def test_scheduled_publish(self, publish_engine):
        """测试定时发布"""
        future_time = datetime.now().timestamp() + 3600  # 1小时后
        scheduled_task = PublishTask(
            content=Content(title="定时发布", content="定时发布内容"),
            platforms=[Platform.ZHIHU],
            schedule_time=future_time,
        )

        # 模拟添加到队列
        task_id = await publish_engine.schedule_publish(scheduled_task)
        assert task_id is not None

        # 检查任务状态
        status = await publish_engine.get_task_status(task_id)
        assert status == TaskStatus.PENDING

    @pytest.mark.asyncio
    async def test_cancel_scheduled_task(self, publish_engine):
        """测试取消定时任务"""
        scheduled_task = PublishTask(
            content=Content(title="待取消", content="内容"),
            platforms=[Platform.ZHIHU],
            schedule_time=datetime.now().timestamp() + 3600,
        )

        task_id = await publish_engine.schedule_publish(scheduled_task)
        success = await publish_engine.cancel_task(task_id)

        assert success is True
        status = await publish_engine.get_task_status(task_id)
        assert status == TaskStatus.CANCELLED

    @pytest.mark.asyncio
    async def test_concurrent_publishing(self, publish_engine):
        """测试并发发布"""
        tasks = []
        for i in range(5):
            task = PublishTask(
                content=Content(title=f"并发测试{i}", content=f"内容{i}"),
                platforms=[Platform.ZHIHU],
            )
            tasks.append(publish_engine.publish(task))

        # 并发执行所有任务
        results_list = await asyncio.gather(*tasks)

        assert len(results_list) == 5
        for results in results_list:
            assert len(results) == 1
            assert results[0].success is True


class TestErrorHandlerComprehensive:
    """错误处理器综合测试"""

    @pytest.fixture
    def error_handler(self):
        """创建错误处理器实例"""
        return ErrorHandler()

    @pytest.mark.asyncio
    async def test_retry_policy_success_after_failure(self, error_handler):
        """测试重试机制：失败后成功"""
        call_count = 0

        async def flaky_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise ConnectionError("网络错误")
            return "成功"

        policy = RetryPolicy(max_attempts=3, delay=0.1)
        result = await error_handler.execute_with_retry(flaky_function, policy)

        assert result == "成功"
        assert call_count == 3

    @pytest.mark.asyncio
    async def test_retry_policy_max_attempts_exceeded(self, error_handler):
        """测试重试机制：达到最大重试次数"""

        async def always_fail():
            raise ValueError("始终失败")

        policy = RetryPolicy(max_attempts=2, delay=0.1)

        with pytest.raises(ValueError):
            await error_handler.execute_with_retry(always_fail, policy)

    @pytest.mark.asyncio
    async def test_circuit_breaker_open_after_failures(self, error_handler):
        """测试熔断器：失败后开启"""
        circuit_breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=1.0)

        async def failing_function():
            raise RuntimeError("服务不可用")

        # 触发足够多的失败来打开熔断器
        for _ in range(3):
            try:
                await error_handler.execute_with_circuit_breaker(failing_function, circuit_breaker)
            except RuntimeError:
                pass

        # 熔断器应该已经打开
        assert circuit_breaker.is_open is True

        # 下一次调用应该快速失败
        with pytest.raises(Exception) as exc_info:
            await error_handler.execute_with_circuit_breaker(failing_function, circuit_breaker)

        assert "熔断器已打开" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_error_categorization(self, error_handler):
        """测试错误分类"""
        # 网络错误
        network_error = ConnectionError("连接超时")
        category = error_handler.categorize_error(network_error)
        assert category == "network"

        # 认证错误
        auth_error = AuthenticationError("认证失败")
        category = error_handler.categorize_error(auth_error)
        assert category == "authentication"

        # 配置错误
        config_error = ConfigurationError("配置无效")
        category = error_handler.categorize_error(config_error)
        assert category == "configuration"

    @pytest.mark.asyncio
    async def test_error_recovery_strategies(self, error_handler):
        """测试错误恢复策略"""
        # 网络错误应该可以重试
        network_error = ConnectionError("网络异常")
        should_retry = error_handler.should_retry(network_error)
        assert should_retry is True

        # 认证错误不应该重试
        auth_error = AuthenticationError("无效token")
        should_retry = error_handler.should_retry(auth_error)
        assert should_retry is False

    def test_error_logging_and_metrics(self, error_handler):
        """测试错误日志记录和指标"""
        test_error = ValueError("测试错误")

        # 记录错误
        error_handler.log_error(test_error, {"context": "test"})

        # 获取错误统计
        stats = error_handler.get_error_stats()
        assert stats["total_errors"] > 0
        assert "ValueError" in stats["error_types"]

    @pytest.mark.asyncio
    async def test_graceful_degradation(self, error_handler):
        """测试优雅降级"""

        async def primary_service():
            raise Exception("主服务不可用")

        async def fallback_service():
            return "降级服务结果"

        result = await error_handler.execute_with_fallback(primary_service, fallback_service)

        assert result == "降级服务结果"


class TestServiceIntegration:
    """服务集成测试"""

    @pytest.fixture
    def temp_dir(self):
        """创建临时目录"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)

    @pytest.mark.asyncio
    async def test_full_publish_workflow(self, temp_dir):
        """测试完整发布工作流"""
        # 初始化服务
        config_manager = ConfigManager(temp_dir)
        content_manager = ContentManager()
        error_handler = ErrorHandler()

        # 创建内容
        content = Content(
            title="集成测试文章",
            content="# 标题\n\n这是测试内容，包含**粗体**和[链接](http://example.com)。",
            content_format=ContentFormat.MARKDOWN,
        )

        # 验证内容
        validation_result = await content_manager.validate_content(content)
        assert validation_result.is_valid is True

        # 转换内容格式
        transformed = await content_manager.transform_content(content, Platform.ZHIHU)
        assert transformed is not None

        # 保存配置
        await config_manager.set_config_value("platforms.zhihu.enabled", True)
        platform_config = await config_manager.get_platform_config(Platform.ZHIHU)
        assert platform_config is not None

    @pytest.mark.asyncio
    async def test_error_handling_integration(self, temp_dir):
        """测试错误处理集成"""
        config_manager = ConfigManager(temp_dir)
        error_handler = ErrorHandler()

        # 模拟配置加载错误
        async def load_invalid_config():
            raise ConfigurationError("配置文件损坏")

        # 使用错误处理器包装
        policy = RetryPolicy(max_attempts=2, delay=0.1)

        with pytest.raises(ConfigurationError):
            await error_handler.execute_with_retry(load_invalid_config, policy)

        # 验证错误被记录
        stats = error_handler.get_error_stats()
        assert stats["total_errors"] > 0
