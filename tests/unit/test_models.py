"""
TextUp 模型单元测试

测试核心数据模型的功能，包括验证、序列化等。
"""

import pytest
from datetime import datetime, timedelta
from typing import List, Dict, Any
from unittest.mock import Mock, patch

from textup.models import (
    Content,
    TransformedContent,
    PublishTask,
    PublishRecord,
    Platform,
    TaskStatus,
    PublishStatus,
    ContentFormat,
    ValidationError,
    ValidationResult,
    ContentMetrics,
    ZhihuCredentials,
    WeiboCredentials,
    PublishResult,
    AuthResult,
    PlatformLimits,
)


class TestContent:
    """测试Content模型"""

    def test_create_valid_content(self):
        """测试创建有效内容"""
        content = Content(
            title="测试标题",
            content="这是一个测试内容",
            format=ContentFormat.MARKDOWN,
            tags=["测试", "单元测试"],
            author="测试作者",
        )

        assert content.title == "测试标题"
        assert content.content == "这是一个测试内容"
        assert content.format == ContentFormat.MARKDOWN
        assert content.tags == ["测试", "单元测试"]
        assert content.author == "测试作者"
        assert isinstance(content.created_at, datetime)

    def test_validate_title(self):
        """测试标题验证"""
        # 测试有效标题
        content = Content(title="有效标题", content="内容")
        errors = content.validate_title("有效标题")
        assert len(errors) == 0

        # 测试空标题
        errors = content.validate_title("")
        assert len(errors) == 1
        assert "不能为空" in errors[0].message

        # 测试过长标题
        long_title = "a" * 101
        errors = content.validate_title(long_title)
        assert len(errors) == 1
        assert "不能超过100字符" in errors[0].message

    def test_validate_content(self):
        """测试内容验证"""
        content = Content(title="标题", content="内容")

        # 测试有效内容
        errors = content.validate_content("有效的内容")
        assert len(errors) == 0

        # 测试空内容
        errors = content.validate_content("")
        assert len(errors) == 1
        assert "不能为空" in errors[0].message

    def test_validate_tags(self):
        """测试标签验证"""
        content = Content(title="标题", content="内容")

        # 测试有效标签
        errors = content.validate_tags(["标签1", "标签2"])
        assert len(errors) == 0

        # 测试过多标签
        too_many_tags = [f"标签{i}" for i in range(11)]
        errors = content.validate_tags(too_many_tags)
        assert len(errors) == 1
        assert "不能超过10个" in errors[0].message

        # 测试空标签
        errors = content.validate_tags(["", "有效标签"])
        assert len(errors) == 1
        assert "不能为空" in errors[0].message

    def test_get_word_count(self):
        """测试字数统计"""
        content = Content(title="标题", content="这是一个测试内容，包含中文和English mixed text.")

        word_count = content.get_word_count()
        assert word_count > 0

    def test_get_char_count(self):
        """测试字符数统计"""
        content = Content(title="标题", content="测试内容123")

        char_count = content.get_char_count()
        assert char_count == 6  # "测试内容123" = 6个字符

    def test_has_images(self):
        """测试图片检测"""
        # 无图片内容
        content = Content(title="标题", content="纯文本内容")
        assert not content.has_images()

        # 包含Markdown图片
        content_with_md_image = Content(
            title="标题", content="内容 ![图片](https://example.com/image.jpg) 更多内容"
        )
        assert content_with_md_image.has_images()

        # 包含HTML图片
        content_with_html_image = Content(
            title="标题",
            content='内容 <img src="https://example.com/image.jpg" alt="图片"> 更多内容',
        )
        assert content_with_html_image.has_images()

    def test_extract_images(self):
        """测试图片提取"""
        content = Content(
            title="标题",
            content="""
            这是内容 ![图片1](https://example.com/img1.jpg)
            更多内容 <img src="https://example.com/img2.png" alt="图片2">
            结束
            """,
        )

        images = content.extract_images()
        assert len(images) == 2
        assert "https://example.com/img1.jpg" in images
        assert "https://example.com/img2.png" in images

    def test_get_summary(self):
        """测试摘要生成"""
        long_content = "这是一个很长的内容。" * 50
        content = Content(title="标题", content=long_content)

        summary = content.get_summary(max_length=100)
        assert len(summary) <= 103  # 100 + "..."
        assert summary.endswith("...")

        # 测试短内容
        short_content = Content(title="标题", content="短内容")
        summary = short_content.get_summary(max_length=100)
        assert summary == "短内容"

    def test_to_json(self):
        """测试JSON序列化"""
        content = Content(title="标题", content="内容", tags=["标签1", "标签2"])

        json_str = content.to_json()
        assert isinstance(json_str, str)
        assert "标题" in json_str
        assert "内容" in json_str

    def test_to_dict(self):
        """测试字典转换"""
        content = Content(title="标题", content="内容", tags=["标签1", "标签2"])

        data_dict = content.to_dict()
        assert isinstance(data_dict, dict)
        assert data_dict["title"] == "标题"
        assert data_dict["content"] == "内容"
        assert data_dict["tags"] == ["标签1", "标签2"]


class TestTransformedContent:
    """测试TransformedContent模型"""

    def test_create_transformed_content(self):
        """测试创建转换后内容"""
        original = Content(title="原始标题", content="原始内容")

        transformed = TransformedContent(
            original_content=original,
            title="转换后标题",
            content="转换后内容",
            html="<p>转换后内容</p>",
            platform_specific_data={"zhihu": {"column_id": "123"}},
        )

        assert transformed.title == "转换后标题"
        assert transformed.content == "转换后内容"
        assert transformed.html == "<p>转换后内容</p>"
        assert transformed.platform_specific_data["zhihu"]["column_id"] == "123"

    def test_calculate_metrics(self):
        """测试指标计算"""
        original = Content(title="原始标题", content="原始内容")

        transformed = TransformedContent(
            original_content=original,
            title="转换后标题",
            content="这是一个测试内容，用于计算各种指标",
            html="<p>这是一个测试内容，用于计算各种指标</p>",
        )

        metrics = transformed.calculate_metrics()
        assert isinstance(metrics, ContentMetrics)
        assert metrics.word_count > 0
        assert metrics.char_count > 0
        assert metrics.read_time_minutes > 0


class TestPublishTask:
    """测试PublishTask模型"""

    def test_create_publish_task(self):
        """测试创建发布任务"""
        content = Content(title="标题", content="内容")

        task = PublishTask(
            content=content,
            platforms=[Platform.ZHIHU, Platform.WEIBO],
            status=TaskStatus.PENDING,
            platform_options={
                Platform.ZHIHU: {"column_id": "123"},
                Platform.WEIBO: {"is_original": True},
            },
        )

        assert len(task.platforms) == 2
        assert Platform.ZHIHU in task.platforms
        assert Platform.WEIBO in task.platforms
        assert task.status == TaskStatus.PENDING

    def test_validate_platforms(self):
        """测试平台验证"""
        content = Content(title="标题", content="内容")
        task = PublishTask(content=content, platforms=[Platform.ZHIHU])

        # 测试有效平台
        errors = task.validate_platforms([Platform.ZHIHU, Platform.WEIBO])
        assert len(errors) == 0

        # 测试空平台列表
        errors = task.validate_platforms([])
        assert len(errors) == 1
        assert "至少选择一个平台" in errors[0].message


class TestPublishRecord:
    """测试PublishRecord模型"""

    def test_create_publish_record(self):
        """测试创建发布记录"""
        record = PublishRecord(
            task_id="task123",
            platform=Platform.ZHIHU,
            status=PublishStatus.SUCCESS,
            platform_post_id="post123",
            platform_url="https://zhihu.com/answer/123",
        )

        assert record.task_id == "task123"
        assert record.platform == Platform.ZHIHU
        assert record.status == PublishStatus.SUCCESS
        assert record.platform_post_id == "post123"
        assert record.platform_url == "https://zhihu.com/answer/123"


class TestCredentials:
    """测试认证凭证模型"""

    def test_zhihu_credentials(self):
        """测试知乎认证凭证"""
        credentials = ZhihuCredentials(
            client_id="test_client_id",
            client_secret="test_client_secret",
            redirect_uri="https://example.com/callback",
        )

        assert credentials.client_id == "test_client_id"
        assert credentials.client_secret == "test_client_secret"
        assert credentials.redirect_uri == "https://example.com/callback"

    def test_weibo_credentials(self):
        """测试微博认证凭证"""
        credentials = WeiboCredentials(
            app_key="test_app_key",
            app_secret="test_app_secret",
            redirect_uri="https://example.com/callback",
        )

        assert credentials.app_key == "test_app_key"
        assert credentials.app_secret == "test_app_secret"
        assert credentials.redirect_uri == "https://example.com/callback"


class TestPublishResult:
    """测试发布结果模型"""

    def test_successful_publish_result(self):
        """测试成功发布结果"""
        result = PublishResult(
            success=True,
            platform=Platform.ZHIHU,
            platform_post_id="post123",
            platform_url="https://zhihu.com/answer/123",
            publish_time=datetime.now(),
            message="发布成功",
        )

        assert result.success is True
        assert result.platform == Platform.ZHIHU
        assert result.platform_post_id == "post123"
        assert result.platform_url == "https://zhihu.com/answer/123"
        assert result.message == "发布成功"
        assert result.error_message is None

    def test_failed_publish_result(self):
        """测试失败发布结果"""
        result = PublishResult(
            success=False,
            platform=Platform.WEIBO,
            error_message="发布失败：权限不足",
            error_details={"status_code": 403},
        )

        assert result.success is False
        assert result.platform == Platform.WEIBO
        assert result.error_message == "发布失败：权限不足"
        assert result.error_details["status_code"] == 403
        assert result.platform_post_id is None

    def test_validate_error_message(self):
        """测试错误消息验证"""
        result = PublishResult(success=False, platform=Platform.ZHIHU)

        # 测试有效错误消息
        errors = result.validate_error_message("有效的错误消息")
        assert len(errors) == 0

        # 测试空错误消息（失败时必须有错误消息）
        errors = result.validate_error_message("")
        assert len(errors) == 1

    def test_validate_platform_post_id(self):
        """测试平台文章ID验证"""
        result = PublishResult(success=True, platform=Platform.ZHIHU)

        # 测试有效ID
        errors = result.validate_platform_post_id("valid_id_123")
        assert len(errors) == 0

        # 测试空ID（成功时必须有ID）
        errors = result.validate_platform_post_id("")
        assert len(errors) == 1


class TestAuthResult:
    """测试认证结果模型"""

    def test_successful_auth_result(self):
        """测试成功认证结果"""
        result = AuthResult(
            success=True,
            platform=Platform.ZHIHU,
            auth_data={"access_token": "token123", "user_id": "user123"},
            message="认证成功",
        )

        assert result.success is True
        assert result.platform == Platform.ZHIHU
        assert result.auth_data["access_token"] == "token123"
        assert result.message == "认证成功"

    def test_failed_auth_result(self):
        """测试失败认证结果"""
        result = AuthResult(
            success=False,
            platform=Platform.WEIBO,
            error_message="认证失败：无效的客户端ID",
            auth_url="https://api.weibo.com/oauth2/authorize?...",
        )

        assert result.success is False
        assert result.platform == Platform.WEIBO
        assert result.error_message == "认证失败：无效的客户端ID"
        assert result.auth_url.startswith("https://api.weibo.com/oauth2/authorize")


class TestValidationResult:
    """测试验证结果模型"""

    def test_valid_validation_result(self):
        """测试有效验证结果"""
        result = ValidationResult(is_valid=True, errors=[])
        assert result.is_valid is True
        assert len(result.errors) == 0

    def test_invalid_validation_result(self):
        """测试无效验证结果"""
        errors = [
            ValidationError(field="title", message="标题不能为空", value=""),
            ValidationError(field="content", message="内容太短", value="短"),
        ]

        result = ValidationResult(is_valid=False, errors=errors)
        assert result.is_valid is False
        assert len(result.errors) == 2
        assert result.errors[0].field == "title"
        assert result.errors[1].field == "content"


class TestContentMetrics:
    """测试内容指标模型"""

    def test_create_content_metrics(self):
        """测试创建内容指标"""
        metrics = ContentMetrics(
            word_count=100, char_count=500, paragraph_count=5, image_count=2, estimated_read_time=3
        )

        assert metrics.word_count == 100
        assert metrics.char_count == 500
        assert metrics.paragraph_count == 5
        assert metrics.image_count == 2
        assert metrics.estimated_read_time == 3

    def test_calculate_read_time(self):
        """测试阅读时间计算"""
        metrics = ContentMetrics(word_count=300, char_count=1500)

        read_time = metrics.calculate_read_time()
        assert read_time > 0
        # 按照平均阅读速度（每分钟200字），300字大约需要1.5分钟
        assert 1 <= read_time <= 3


class TestPlatformLimits:
    """测试平台限制模型"""

    def test_platform_limits(self):
        """测试平台限制"""
        limits = PlatformLimits(
            max_title_length=100,
            max_content_length=10000,
            max_tags=10,
            max_images=9,
            supported_formats=[ContentFormat.MARKDOWN, ContentFormat.HTML],
        )

        assert limits.max_title_length == 100
        assert limits.max_content_length == 10000
        assert limits.max_tags == 10
        assert limits.max_images == 9
        assert ContentFormat.MARKDOWN in limits.supported_formats
        assert ContentFormat.HTML in limits.supported_formats


if __name__ == "__main__":
    pytest.main([__file__])
