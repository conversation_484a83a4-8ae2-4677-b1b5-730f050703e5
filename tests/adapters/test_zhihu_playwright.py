"""
知乎Playwright适配器测试

本模块包含ZhihuPlaywrightAdapter的单元测试和集成测试。
使用pytest和mock来模拟浏览器操作，避免实际的网络请求。
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from pathlib import Path

from textup.adapters.zhihu_playwright import (
    ZhihuPlaywrightAdapter,
    BrowserManager,
    AuthManager,
    ContentPublisher,
    AntiDetectionSystem
)
from textup.models import (
    Platform,
    TransformedContent,
    ContentFormat,
    AuthResult,
    PublishResult,
    ValidationResult,
    ValidationError
)
from textup.utils import TextUpError, InvalidCredentialsError


class TestBrowserManager:
    """浏览器管理器测试"""

    @pytest.fixture
    def browser_manager(self):
        """创建浏览器管理器实例"""
        return BrowserManager(headless=True, debug=False)

    @pytest.mark.asyncio
    async def test_browser_manager_init(self, browser_manager):
        """测试浏览器管理器初始化"""
        with patch('textup.adapters.zhihu_playwright.async_playwright') as mock_playwright:
            mock_playwright_instance = AsyncMock()
            mock_playwright.return_value.start.return_value = mock_playwright_instance

            mock_browser = AsyncMock()
            mock_context = AsyncMock()
            mock_page = AsyncMock()

            mock_playwright_instance.chromium.launch.return_value = mock_browser
            mock_browser.new_context.return_value = mock_context
            mock_context.new_page.return_value = mock_page

            page = await browser_manager.init_browser()

            assert page == mock_page
            assert browser_manager.browser == mock_browser
            assert browser_manager.context == mock_context
            assert browser_manager.page == mock_page

    @pytest.mark.asyncio
    async def test_browser_manager_close(self, browser_manager):
        """测试浏览器关闭"""
        browser_manager.page = AsyncMock()
        browser_manager.context = AsyncMock()
        browser_manager.browser = AsyncMock()
        browser_manager.playwright = AsyncMock()

        await browser_manager.close()

        browser_manager.page.close.assert_called_once()
        browser_manager.context.close.assert_called_once()
        browser_manager.browser.close.assert_called_once()
        browser_manager.playwright.stop.assert_called_once()

    def test_get_random_user_agent(self, browser_manager):
        """测试获取随机User-Agent"""
        user_agent = browser_manager._get_random_user_agent()
        assert isinstance(user_agent, str)
        assert "Mozilla" in user_agent
        assert "Chrome" in user_agent


class TestAuthManager:
    """认证管理器测试"""

    @pytest.fixture
    def mock_page(self):
        """创建模拟页面"""
        page = AsyncMock()
        page.locator.return_value = AsyncMock()
        return page

    @pytest.fixture
    def auth_manager(self, mock_page):
        """创建认证管理器实例"""
        return AuthManager(mock_page)

    @pytest.mark.asyncio
    async def test_login_success(self, auth_manager, mock_page):
        """测试登录成功"""
        # Mock页面元素
        mock_page.goto = AsyncMock()
        mock_page.wait_for_load_state = AsyncMock()
        mock_page.locator.return_value.count = AsyncMock(return_value=1)
        mock_page.locator.return_value.click = AsyncMock()
        mock_page.locator.return_value.fill = AsyncMock()

        # Mock登录状态验证
        with patch.object(auth_manager, '_load_session', return_value=False), \
             patch.object(auth_manager, '_verify_login_status', return_value=True), \
             patch.object(auth_manager, '_save_session'), \
             patch.object(auth_manager, '_handle_verification'):

            result = await auth_manager.login("<EMAIL>", "password")

            assert result is True
            mock_page.goto.assert_called_with("https://www.zhihu.com/signin")

    @pytest.mark.asyncio
    async def test_login_with_existing_session(self, auth_manager):
        """测试使用已有会话登录"""
        with patch.object(auth_manager, '_load_session', return_value=True), \
             patch.object(auth_manager, '_verify_login_status', return_value=True):

            result = await auth_manager.login("<EMAIL>", "password")

            assert result is True

    @pytest.mark.asyncio
    async def test_login_failure(self, auth_manager, mock_page):
        """测试登录失败"""
        mock_page.goto = AsyncMock()
        mock_page.wait_for_load_state = AsyncMock()
        mock_page.locator.return_value.count = AsyncMock(return_value=0)

        with patch.object(auth_manager, '_load_session', return_value=False), \
             patch.object(auth_manager, '_verify_login_status', return_value=False), \
             patch.object(auth_manager, '_handle_verification'):

            result = await auth_manager.login("<EMAIL>", "wrong_password")

            assert result is False

    def test_hash_username(self, auth_manager):
        """测试用户名哈希"""
        username = "<EMAIL>"
        hashed = auth_manager._hash_username(username)

        assert isinstance(hashed, str)
        assert len(hashed) == 32  # MD5哈希长度


class TestContentPublisher:
    """内容发布器测试"""

    @pytest.fixture
    def mock_page(self):
        """创建模拟页面"""
        page = AsyncMock()
        page.locator.return_value = AsyncMock()
        page.evaluate = AsyncMock()
        page.goto = AsyncMock()
        page.wait_for_load_state = AsyncMock()
        return page

    @pytest.fixture
    def content_publisher(self, mock_page):
        """创建内容发布器实例"""
        return ContentPublisher(mock_page)

    @pytest.mark.asyncio
    async def test_navigate_to_editor_success(self, content_publisher, mock_page):
        """测试导航到编辑器成功"""
        mock_page.locator.return_value.wait_for = AsyncMock()

        result = await content_publisher._navigate_to_editor()

        assert result is True
        mock_page.goto.assert_called_with("https://zhuanlan.zhihu.com/write")

    @pytest.mark.asyncio
    async def test_fill_title_success(self, content_publisher, mock_page):
        """测试填写标题成功"""
        mock_page.locator.return_value.count = AsyncMock(return_value=1)
        mock_page.locator.return_value.click = AsyncMock()
        mock_page.locator.return_value.fill = AsyncMock()
        mock_page.locator.return_value.type = AsyncMock()

        result = await content_publisher._fill_title("测试标题")

        assert result is True

    @pytest.mark.asyncio
    async def test_fill_content_success(self, content_publisher, mock_page):
        """测试填写内容成功"""
        mock_page.locator.return_value.count = AsyncMock(return_value=1)
        mock_page.locator.return_value.click = AsyncMock()
        mock_page.evaluate.return_value = True

        result = await content_publisher._fill_content("# 测试内容")

        assert result is True

    def test_markdown_to_html(self, content_publisher):
        """测试Markdown转HTML"""
        markdown_text = "# 标题\n\n这是一段文字。\n\n```python\nprint('hello')\n```"
        html_result = content_publisher._markdown_to_html(markdown_text)

        assert "<h1>" in html_result
        assert "<pre" in html_result
        assert "print('hello')" in html_result

    @pytest.mark.asyncio
    async def test_execute_publish_success(self, content_publisher, mock_page):
        """测试执行发布成功"""
        mock_page.locator.return_value.count = AsyncMock(return_value=1)
        mock_page.locator.return_value.is_enabled = AsyncMock(return_value=True)
        mock_page.locator.return_value.click = AsyncMock()
        mock_page.url = "https://zhuanlan.zhihu.com/p/123456789"

        # Mock成功发布的检查
        success_locator = AsyncMock()
        success_locator.count = AsyncMock(return_value=1)
        mock_page.locator.side_effect = lambda selector: success_locator if "发布成功" in selector else AsyncMock()

        result = await content_publisher._execute_publish()

        assert result["success"] is True
        assert "123456789" in result["url"]

    @pytest.mark.asyncio
    async def test_publish_article_full_flow(self, content_publisher, mock_page):
        """测试完整的文章发布流程"""
        content_data = {
            "title": "测试文章",
            "content": "# 测试内容\n\n这是测试内容",
            "tags": ["测试", "自动化"]
        }

        # Mock所有子方法
        with patch.object(content_publisher, '_navigate_to_editor', return_value=True), \
             patch.object(content_publisher, '_fill_title', return_value=True), \
             patch.object(content_publisher, '_fill_content', return_value=True), \
             patch.object(content_publisher, '_add_tags'), \
             patch.object(content_publisher, '_execute_publish', return_value={
                 "success": True,
                 "url": "https://zhuanlan.zhihu.com/p/123456789"
             }):

            result = await content_publisher.publish_article(content_data)

            assert result["success"] is True
            assert "url" in result


class TestAntiDetectionSystem:
    """反检测系统测试"""

    @pytest.fixture
    def mock_page(self):
        """创建模拟页面"""
        page = AsyncMock()
        page.mouse = AsyncMock()
        page.evaluate = AsyncMock()
        return page

    @pytest.fixture
    def anti_detection(self, mock_page):
        """创建反检测系统实例"""
        return AntiDetectionSystem(mock_page)

    @pytest.mark.asyncio
    async def test_simulate_human_behavior(self, anti_detection):
        """测试模拟人类行为"""
        with patch.object(anti_detection, '_random_mouse_movement'), \
             patch.object(anti_detection, '_random_scrolling'), \
             patch.object(anti_detection, '_random_pause'):

            await anti_detection.simulate_human_behavior()

            anti_detection._random_mouse_movement.assert_called_once()
            anti_detection._random_scrolling.assert_called_once()
            anti_detection._random_pause.assert_called_once()

    @pytest.mark.asyncio
    async def test_random_mouse_movement(self, anti_detection, mock_page):
        """测试随机鼠标移动"""
        mock_page.evaluate.return_value = {"width": 1920, "height": 1080}

        await anti_detection._random_mouse_movement()

        mock_page.mouse.move.assert_called_once()

    @pytest.mark.asyncio
    async def test_random_action_sequence(self, anti_detection):
        """测试随机动作序列"""
        with patch.object(anti_detection, '_random_mouse_movement'), \
             patch.object(anti_detection, '_random_scrolling'), \
             patch.object(anti_detection, '_random_pause'):

            await anti_detection.random_action_sequence()

            # 至少应该执行一个动作
            call_count = (
                anti_detection._random_mouse_movement.call_count +
                anti_detection._random_scrolling.call_count +
                anti_detection._random_pause.call_count
            )
            assert call_count >= 1


class TestZhihuPlaywrightAdapter:
    """知乎Playwright适配器测试"""

    @pytest.fixture
    def adapter(self):
        """创建适配器实例"""
        return ZhihuPlaywrightAdapter()

    @pytest.fixture
    def valid_credentials(self):
        """有效的认证凭证"""
        return {
            "username": "<EMAIL>",
            "password": "test_password",
            "headless": True,
            "debug": False
        }

    @pytest.fixture
    def sample_content(self):
        """示例内容"""
        return TransformedContent(
            title="测试文章",
            content="# 测试内容\n\n这是一篇测试文章。",
            content_format=ContentFormat.MARKDOWN,
            tags=["测试", "自动化"],
            metadata={"category": "技术"}
        )

    def test_platform_property(self, adapter):
        """测试平台属性"""
        assert adapter.platform == Platform.ZHIHU

    def test_base_url_property(self, adapter):
        """测试基础URL属性"""
        assert adapter.base_url == "https://www.zhihu.com"

    def test_required_credentials_property(self, adapter):
        """测试必需认证字段属性"""
        required = adapter.required_credentials
        assert "username" in required
        assert "password" in required

    def test_validate_credentials_valid(self, adapter, valid_credentials):
        """测试有效凭证验证"""
        result = adapter._validate_credentials(valid_credentials)
        assert result.is_valid is True
        assert len(result.errors) == 0

    def test_validate_credentials_missing_fields(self, adapter):
        """测试缺少必需字段的凭证验证"""
        invalid_credentials = {"username": "<EMAIL>"}
        result = adapter._validate_credentials(invalid_credentials)

        assert result.is_valid is False
        assert len(result.errors) > 0
        assert any(error.field == "password" for error in result.errors)

    def test_validate_credentials_invalid_username(self, adapter):
        """测试无效用户名格式"""
        invalid_credentials = {
            "username": "invalid_username",
            "password": "test_password"
        }
        result = adapter._validate_credentials(invalid_credentials)

        assert result.is_valid is False
        assert any(error.field == "username" for error in result.errors)

    def test_validate_format_valid_content(self, adapter, sample_content):
        """测试有效内容格式验证"""
        result = adapter._validate_format_impl(sample_content)
        assert result.is_valid is True

    def test_validate_format_empty_title(self, adapter):
        """测试空标题验证"""
        content = TransformedContent(
            title="",
            content="测试内容",
            content_format=ContentFormat.MARKDOWN
        )
        result = adapter._validate_format_impl(content)

        assert result.is_valid is False
        assert any(error.field == "title" for error in result.errors)

    def test_validate_format_empty_content(self, adapter):
        """测试空内容验证"""
        content = TransformedContent(
            title="测试标题",
            content="",
            content_format=ContentFormat.MARKDOWN
        )
        result = adapter._validate_format_impl(content)

        assert result.is_valid is False
        assert any(error.field == "content" for error in result.errors)

    def test_validate_format_too_many_tags(self, adapter):
        """测试标签数量过多验证"""
        content = TransformedContent(
            title="测试标题",
            content="测试内容",
            content_format=ContentFormat.MARKDOWN,
            tags=["标签1", "标签2", "标签3", "标签4", "标签5", "标签6"]  # 超过5个
        )
        result = adapter._validate_format_impl(content)

        assert result.is_valid is False
        assert any(error.field == "tags" for error in result.errors)

    @pytest.mark.asyncio
    async def test_authenticate_success(self, adapter, valid_credentials):
        """测试认证成功"""
        with patch('textup.adapters.zhihu_playwright.BrowserManager') as mock_browser_manager, \
             patch('textup.adapters.zhihu_playwright.AuthManager') as mock_auth_manager, \
             patch('textup.adapters.zhihu_playwright.ContentPublisher') as mock_content_publisher:

            # Mock浏览器管理器
            mock_browser_instance = AsyncMock()
            mock_browser_manager.return_value = mock_browser_instance
            mock_page = AsyncMock()
            mock_browser_instance.init_browser.return_value = mock_page

            # Mock认证管理器
            mock_auth_instance = AsyncMock()
            mock_auth_manager.return_value = mock_auth_instance
            mock_auth_instance.login.return_value = True

            result = await adapter._authenticate_impl(valid_credentials)

            assert result.success is True
            assert result.platform == Platform.ZHIHU
            assert adapter.content_publisher is not None

    @pytest.mark.asyncio
    async def test_authenticate_failure(self, adapter, valid_credentials):
        """测试认证失败"""
        with patch('textup.adapters.zhihu_playwright.BrowserManager') as mock_browser_manager, \
             patch('textup.adapters.zhihu_playwright.AuthManager') as mock_auth_manager:

            # Mock浏览器管理器
            mock_browser_instance = AsyncMock()
            mock_browser_manager.return_value = mock_browser_instance
            mock_page = AsyncMock()
            mock_browser_instance.init_browser.return_value = mock_page

            # Mock认证管理器返回失败
            mock_auth_instance = AsyncMock()
            mock_auth_manager.return_value = mock_auth_instance
            mock_auth_instance.login.return_value = False

            result = await adapter._authenticate_impl(valid_credentials)

            assert result.success is False
            assert "登录失败" in result.error_message

    @pytest.mark.asyncio
    async def test_publish_success(self, adapter, sample_content):
        """测试发布成功"""
        # Mock已认证状态
        adapter.content_publisher = AsyncMock()
        adapter.content_publisher.publish_article.return_value = {
            "success": True,
            "url": "https://zhuanlan.zhihu.com/p/123456789",
            "message": "发布成功"
        }

        # Mock反检测系统
        adapter.anti_detection = AsyncMock()

        result = await adapter._publish_impl(sample_content, {})

        assert result.success is True
        assert result.platform_post_id == "123456789"
        assert result.platform_url == "https://zhuanlan.zhihu.com/p/123456789"

    @pytest.mark.asyncio
    async def test_publish_not_authenticated(self, adapter, sample_content):
        """测试未认证时发布"""
        result = await adapter._publish_impl(sample_content, {})

        assert result.success is False
        assert "未初始化" in result.error_message

    @pytest.mark.asyncio
    async def test_publish_failure(self, adapter, sample_content):
        """测试发布失败"""
        # Mock已认证状态
        adapter.content_publisher = AsyncMock()
        adapter.content_publisher.publish_article.return_value = {
            "success": False,
            "error": "发布失败"
        }

        # Mock反检测系统
        adapter.anti_detection = AsyncMock()

        result = await adapter._publish_impl(sample_content, {})

        assert result.success is False
        assert "发布失败" in result.error_message

    @pytest.mark.asyncio
    async def test_get_publish_status_success(self, adapter):
        """测试获取发布状态成功"""
        # Mock页面
        adapter._page = AsyncMock()
        adapter._page.goto = AsyncMock()
        adapter._page.wait_for_load_state = AsyncMock()
        adapter._page.locator.return_value.count = AsyncMock(return_value=1)

        result = await adapter._get_publish_status_impl("123456789")

        assert result["status"] == "published"
        assert "123456789" in result["url"]

    @pytest.mark.asyncio
    async def test_get_publish_status_not_found(self, adapter):
        """测试文章不存在"""
        # Mock页面
        adapter._page = AsyncMock()
        adapter._page.goto = AsyncMock()
        adapter._page.wait_for_load_state = AsyncMock()
        adapter._page.locator.return_value.count = AsyncMock(return_value=0)

        result = await adapter._get_publish_status_impl("123456789")

        assert result["status"] == "not_found"

    def test_extract_post_id(self, adapter):
        """测试提取文章ID"""
        url = "https://zhuanlan.zhihu.com/p/123456789"
        post_id = adapter._extract_post_id(url)
        assert post_id == "123456789"

        # 测试无效URL
        invalid_url = "https://example.com/invalid"
        post_id = adapter._extract_post_id(invalid_url)
        assert post_id is None

    @pytest.mark.asyncio
    async def test_close(self, adapter):
        """测试关闭适配器"""
        adapter.browser_manager = AsyncMock()

        await adapter.close()

        adapter.browser_manager.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_context_manager(self, adapter):
        """测试异步上下文管理器"""
        adapter.browser_manager = AsyncMock()

        async with adapter as ctx_adapter:
            assert ctx_adapter == adapter

        adapter.browser_manager.close.assert_called_once()


@pytest.mark.integration
class TestZhihuPlaywrightAdapterIntegration:
    """集成测试（需要真实的浏览器环境）"""

    @pytest.mark.skip(reason="需要真实浏览器环境和有效凭证")
    @pytest.mark.asyncio
    async def test_full_publish_flow(self):
        """完整发布流程测试"""
        # 这个测试需要在有真实浏览器环境的情况下运行
        # 并且需要有效的知乎账号凭证
        pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
