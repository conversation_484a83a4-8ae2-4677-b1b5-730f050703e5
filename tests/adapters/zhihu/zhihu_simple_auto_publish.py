#!/usr/bin/env python3
"""
知乎简化版完全自动化发布脚本
无需cookie文件，直接实现自动化流程
"""
import asyncio
import os
import sys
from playwright.async_api import async_playwright
from datetime import datetime

class ZhihuSimpleAutoPublisher:
    def __init__(self):
        self.browser = None
        self.page = None
        
    async def init_browser(self, headless=False):
        """初始化浏览器"""
        playwright = await async_playwright().start()
        
        # 浏览器启动参数
        browser_args = [
            "--disable-blink-features=AutomationControlled",
            "--no-sandbox",
            "--disable-dev-shm-usage",
            "--disable-gpu",
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
        ]
        
        self.browser = await playwright.chromium.launch(
            headless=headless,
            args=browser_args,
            slow_mo=1000
        )
        
        # 创建新的浏览器上下文
        context = await self.browser.new_context(
            viewport={'width': 1280, 'height': 720},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        self.page = await context.new_page()
        return True
    
    async def login_and_publish(self, article_path, tags=None):
        """登录并发布文章"""
        try:
            # 读取文章
            print("📄 读取文章...")
            with open(article_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            # 解析标题和内容
            lines = content.strip().split("\n")
            title = lines[0].replace("# ", "") if lines[0].startswith("# ") else "测试文章"
            body = "\n".join(lines[1:]).strip()
            
            print(f"📝 文章标题: {title}")
            print(f"📊 内容长度: {len(body)} 字符")
            
            # 初始化浏览器
            await self.init_browser(headless=False)
            
            # 直接导航到知乎登录页面
            print("🌐 正在打开知乎登录页面...")
            await self.page.goto("https://www.zhihu.com/signin", wait_until="networkidle")
            
            print("📝 请在浏览器中完成以下操作：")
            print("   1. 登录您的知乎账号")
            print("   2. 登录成功后，脚本会自动继续")
            print("   3. 整个过程约需要2-3分钟")
            
            # 等待用户登录（90秒超时）
            print("⏳ 等待用户登录（90秒超时）...")
            try:
                await self.page.wait_for_selector(".Avatar", timeout=90000)
                print("✅ 登录成功，继续执行...")
            except:
                print("❌ 登录超时，终止发布")
                return False
            
            # 导航到写作页面
            print("📝 正在导航到写作页面...")
            await self.page.goto("https://zhuanlan.zhihu.com/write", wait_until="networkidle")
            await asyncio.sleep(3)
            
            # 填写标题
            print("🎯 正在填写标题...")
            title_selectors = [
                "input[placeholder*='标题']",
                ".WriteIndex-titleInput input",
                "input[type='text']"
            ]
            
            for selector in title_selectors:
                try:
                    title_input = await self.page.wait_for_selector(selector, timeout=10000)
                    if title_input:
                        await title_input.click()
                        await title_input.clear()
                        await title_input.fill(title)
                        await asyncio.sleep(1)
                        print(f"✅ 已填写标题: {title}")
                        break
                except:
                    continue
            
            # 填写内容
            print("🎯 正在填写内容...")
            content_selectors = [
                ".public-DraftEditor-content",
                ".DraftEditor-root",
                "div[contenteditable='true']"
            ]
            
            for selector in content_selectors:
                try:
                    content_editor = await self.page.wait_for_selector(selector, timeout=10000)
                    if content_editor:
                        await content_editor.click()
                        await content_editor.clear()
                        await content_editor.fill(body)
                        await asyncio.sleep(1)
                        print("✅ 已填写文章内容")
                        break
                except:
                    continue
            
            # 添加标签
            if tags:
                print("🏷️  正在添加标签...")
                try:
                    # 等待标签输入区域出现
                    await self.page.wait_for_selector("[class*='topic' i], [class*='tag' i]", timeout=10000)
                    
                    for tag in tags:
                        # 尝试多种方式添加标签
                        tag_added = False
                        
                        # 方式1: 直接查找标签输入框
                        tag_selectors = [
                            "input[placeholder*='标签'], input[placeholder*='话题'], input[placeholder*='tag']",
                            "[class*='topic' i] input, [class*='tag' i] input",
                            ".WriteIndex-topicInput input, .topic-input input"
                        ]
                        
                        for selector in tag_selectors:
                            try:
                                tag_input = await self.page.wait_for_selector(selector, timeout=5000)
                                if tag_input:
                                    await tag_input.click()
                                    await tag_input.fill(tag)
                                    await asyncio.sleep(1)
                                    
                                    # 尝试点击建议标签或按回车
                                    try:
                                        suggestion = await self.page.wait_for_selector(
                                            f"[class*='suggestion' i]:has-text('{tag}'), [class*='topic' i]:has-text('{tag}')",
                                            timeout=3000
                                        )
                                        if suggestion:
                                            await suggestion.click()
                                            tag_added = True
                                            break
                                    except:
                                        await tag_input.press("Enter")
                                        tag_added = True
                                        break
                            except:
                                continue
                                
                        if tag_added:
                            print(f"✅ 已添加标签: {tag}")
                            await asyncio.sleep(1)
                        else:
                            print(f"⚠️ 标签添加失败: {tag}")
            
                except Exception as e:
                    print(f"⚠️ 添加标签失败: {e}")
            
            # 发布文章
            print("🚀 正在发布文章...")
            await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await asyncio.sleep(2)
            
            # 等待发布按钮可用
            await self.page.wait_for_timeout(3000)
            
            publish_selectors = [
                "button:has-text('发布'):visible:not([disabled])",
                ".PublishPanel-publishButton:visible:not([disabled])",
                "button[type='submit']:visible:not([disabled])",
                "button:has-text('发布')",
                ".PublishPanel-publishButton",
                "button[type='submit']"
            ]
            
            publish_success = False
            for selector in publish_selectors:
                try:
                    publish_btn = await self.page.wait_for_selector(selector, timeout=15000)
                    if publish_btn:
                        # 检查按钮是否可用
                        is_disabled = await publish_btn.is_disabled()
                        if is_disabled:
                            print(f"⚠️ 发布按钮不可用: {selector}")
                            continue
                            
                        await publish_btn.scroll_into_view_if_needed()
                        await asyncio.sleep(1)
                        
                        # 多次尝试点击
                        for attempt in range(3):
                            try:
                                await publish_btn.click(force=True)
                                print("✅ 已点击发布按钮")
                                publish_success = True
                                break
                            except:
                                await asyncio.sleep(2)
                                continue
                                
                        if publish_success:
                            break
                except Exception as e:
                    print(f"⚠️ 发布按钮查找失败: {selector} - {str(e)[:100]}")
                    continue
            
            if publish_success:
                # 等待发布完成
                await asyncio.sleep(5)
                
                # 检查发布结果
                try:
                    await self.page.wait_for_selector("text*='成功', text*='发布', text*='完成'", timeout=15000)
                    print("🎉 文章发布成功！")
                    return True
                except:
                    # 检查URL变化
                    current_url = self.page.url
                    if "publish" not in current_url and "write" not in current_url:
                        print("🎉 文章发布成功，已跳转到文章页面")
                        return True
                    else:
                        print("⚠️ 发布按钮已点击，但未确认发布结果")
            else:
                print("❌ 发布按钮点击失败")
            
            return True
            
        except Exception as e:
            print(f"❌ 发布流程出错: {e}")
            return False
        finally:
            if self.browser:
                await self.browser.close()
                print("🔚 浏览器已关闭")

async def main():
    """主函数"""
    print("=" * 80)
    print("  🚀 知乎简化版完全自动化发布工具")
    print("  Article: tests/test-content/test.md")
    print("  Features: 一次性登录 + 自动填写 + 自动发布")
    print("=" * 80)
    
    # 配置参数
    article_path = "tests/test-content/test.md"
    tags = ["测试", "TextUp", "自动化工具", "效率提升", "内容创作"]
    
    # 检查文章文件
    if not os.path.exists(article_path):
        print(f"❌ 文章文件不存在: {article_path}")
        return
    
    publisher = ZhihuSimpleAutoPublisher()
    success = await publisher.login_and_publish(article_path, tags)
    
    if success:
        print("\n" + "=" * 80)
        print("🎉 发布完成！")
        print("📱 请打开知乎APP或网页版查看已发布的文章")
        print("=" * 80)
    else:
        print("\n" + "=" * 80)
        print("❌ 发布失败，请检查错误信息")
        print("=" * 80)

if __name__ == "__main__":
    asyncio.run(main())