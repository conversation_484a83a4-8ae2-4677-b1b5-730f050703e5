#!/usr/bin/env python3
"""
知乎Playwright适配器快速测试脚本

本脚本用于快速验证ZhihuPlaywrightAdapter的基本功能，
不需要真实的浏览器环境或网络连接。

使用方法:
    python test_zhihu_playwright_quick.py
"""

import asyncio
import sys
import traceback
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent / "src"
sys.path.insert(0, str(project_root))

try:
    from textup.adapters.zhihu_playwright import ZhihuPlaywrightAdapter
    from textup.adapters.factory import create_adapter, AdapterFactory, AdapterType
    from textup.models import Platform, TransformedContent, ContentFormat
    print("✅ 成功导入所有必需模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请确保项目依赖已正确安装")
    sys.exit(1)


class QuickTest:
    """快速测试类"""

    def __init__(self):
        self.adapter = None
        self.test_results = []

    def log_test(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        status = "✅" if success else "❌"
        self.test_results.append({
            'name': test_name,
            'success': success,
            'message': message
        })
        print(f"{status} {test_name}: {message}")

    def test_adapter_creation(self):
        """测试适配器创建"""
        try:
            # 直接创建适配器
            adapter = ZhihuPlaywrightAdapter()
            self.log_test("直接创建适配器", True, f"平台: {adapter.platform}")

            # 使用工厂创建适配器
            factory_adapter = create_adapter('zhihu', 'playwright')
            self.log_test("工厂创建适配器", True, f"类型: {type(factory_adapter).__name__}")

        except Exception as e:
            self.log_test("适配器创建", False, str(e))

    def test_adapter_properties(self):
        """测试适配器属性"""
        try:
            adapter = ZhihuPlaywrightAdapter()

            # 测试基本属性
            assert adapter.platform == Platform.ZHIHU
            self.log_test("平台属性", True, adapter.platform.value)

            assert adapter.base_url == "https://www.zhihu.com"
            self.log_test("基础URL", True, adapter.base_url)

            required_creds = adapter.required_credentials
            assert "username" in required_creds and "password" in required_creds
            self.log_test("必需凭证", True, str(required_creds))

        except Exception as e:
            self.log_test("适配器属性", False, str(e))

    def test_credential_validation(self):
        """测试凭证验证"""
        try:
            adapter = ZhihuPlaywrightAdapter()

            # 测试有效凭证
            valid_creds = {
                "username": "<EMAIL>",
                "password": "test123"
            }
            result = adapter._validate_credentials(valid_creds)
            assert result.is_valid
            self.log_test("有效凭证验证", True, "验证通过")

            # 测试无效凭证
            invalid_creds = {
                "username": "",
                "password": "test123"
            }
            result = adapter._validate_credentials(invalid_creds)
            assert not result.is_valid
            assert len(result.errors) > 0
            self.log_test("无效凭证验证", True, f"错误数: {len(result.errors)}")

        except Exception as e:
            self.log_test("凭证验证", False, str(e))

    def test_content_validation(self):
        """测试内容验证"""
        try:
            adapter = ZhihuPlaywrightAdapter()

            # 创建有效内容
            valid_content = TransformedContent(
                title="测试标题",
                content="这是一个测试内容，长度足够通过验证。",
                html="",
                text="",
                images=[],
                links=[],
                content_format=ContentFormat.MARKDOWN,
                tags=["测试", "自动化"],
                metrics=None
            )

            result = adapter._validate_format_impl(valid_content)
            assert result.is_valid
            self.log_test("有效内容验证", True, "格式验证通过")

            # 测试无效内容（标题为空）
            invalid_content = TransformedContent(
                title="",
                content="测试内容",
                html="",
                text="",
                images=[],
                links=[],
                content_format=ContentFormat.MARKDOWN,
                tags=[],
                metrics=None
            )

            result = adapter._validate_format_impl(invalid_content)
            assert not result.is_valid
            self.log_test("无效内容验证", True, f"检测到错误: {len(result.errors)}")

        except Exception as e:
            self.log_test("内容验证", False, str(e))

    def test_factory_functionality(self):
        """测试工厂功能"""
        try:
            # 测试适配器注册
            available = AdapterFactory.get_available_adapters()
            zhihu_adapters = available.get("zhihu", {}).get("adapters", {})

            has_playwright = "playwright" in zhihu_adapters
            self.log_test("工厂适配器注册", has_playwright,
                         f"Playwright适配器{'已' if has_playwright else '未'}注册")

            # 测试推荐适配器类型
            recommended = AdapterFactory._select_best_adapter(Platform.ZHIHU)
            self.log_test("推荐适配器类型", True, recommended.value)

        except Exception as e:
            self.log_test("工厂功能", False, str(e))

    def test_post_id_extraction(self):
        """测试文章ID提取"""
        try:
            adapter = ZhihuPlaywrightAdapter()

            # 测试有效URL
            valid_url = "https://zhuanlan.zhihu.com/p/123456789"
            post_id = adapter._extract_post_id(valid_url)
            assert post_id == "123456789"
            self.log_test("有效URL提取ID", True, f"提取到ID: {post_id}")

            # 测试无效URL
            invalid_url = "https://example.com/invalid"
            post_id = adapter._extract_post_id(invalid_url)
            assert post_id is None
            self.log_test("无效URL处理", True, "正确返回None")

        except Exception as e:
            self.log_test("文章ID提取", False, str(e))

    async def test_async_context_manager(self):
        """测试异步上下文管理器"""
        try:
            async with ZhihuPlaywrightAdapter() as adapter:
                assert adapter is not None
                assert isinstance(adapter, ZhihuPlaywrightAdapter)

            self.log_test("异步上下文管理器", True, "正确进入和退出")

        except Exception as e:
            self.log_test("异步上下文管理器", False, str(e))

    def test_import_dependencies(self):
        """测试可选依赖导入"""
        try:
            # 检查是否正确处理可选依赖
            from textup.adapters.zhihu_playwright import HAS_BS4, HAS_MARKDOWN2

            bs4_status = "已安装" if HAS_BS4 else "未安装"
            md2_status = "已安装" if HAS_MARKDOWN2 else "未安装"

            self.log_test("BeautifulSoup4依赖", True, bs4_status)
            self.log_test("Markdown2依赖", True, md2_status)

            # 测试fallback逻辑
            adapter = ZhihuPlaywrightAdapter()
            content_publisher = adapter.content_publisher

            # 这里模拟没有markdown2时的fallback
            if not HAS_MARKDOWN2:
                test_md = "# 标题\n\n内容"
                # 这里应该有fallback逻辑
                self.log_test("Markdown fallback", True, "Fallback逻辑存在")

        except Exception as e:
            self.log_test("依赖导入测试", False, str(e))

    async def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始知乎Playwright适配器快速测试")
        print("=" * 50)

        # 同步测试
        self.test_adapter_creation()
        self.test_adapter_properties()
        self.test_credential_validation()
        self.test_content_validation()
        self.test_factory_functionality()
        self.test_post_id_extraction()
        self.test_import_dependencies()

        # 异步测试
        await self.test_async_context_manager()

        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests

        print("\n" + "=" * 50)
        print(f"📊 测试结果统计:")
        print(f"   总测试数: {total_tests}")
        print(f"   ✅ 通过: {passed_tests}")
        print(f"   ❌ 失败: {failed_tests}")
        print(f"   通过率: {(passed_tests/total_tests)*100:.1f}%")

        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   - {result['name']}: {result['message']}")

        return failed_tests == 0


async def main():
    """主函数"""
    try:
        tester = QuickTest()
        success = await tester.run_all_tests()

        if success:
            print("\n🎉 所有测试通过！适配器基本功能正常。")
            print("\n📋 下一步:")
            print("1. 安装Playwright浏览器: python scripts/install_playwright.py")
            print("2. 配置知乎账号信息")
            print("3. 运行完整示例: python docs/examples/zhihu_playwright_example.py")
            return 0
        else:
            print("\n😞 部分测试失败，请检查问题后重试。")
            return 1

    except Exception as e:
        print(f"\n💥 测试过程发生错误: {e}")
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n🛑 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 程序异常退出: {e}")
        sys.exit(1)
