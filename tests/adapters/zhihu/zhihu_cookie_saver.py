#!/usr/bin/env python3
"""
知乎Cookie保存脚本
用于手动登录知乎并保存cookie，以便实现真正的完全自动化发布
"""
import asyncio
import json
from playwright.async_api import async_playwright

async def save_zhihu_cookies():
    """保存知乎登录cookie"""
    print("=" * 60)
    print("  🍪 知乎Cookie保存工具")
    print("  请在打开的浏览器中手动登录知乎账号")
    print("  登录成功后，cookie将自动保存到zhihu_cookies.json文件")
    print("=" * 60)
    
    playwright = await async_playwright().start()
    
    # 启动浏览器（非无头模式，方便手动操作）
    browser = await playwright.chromium.launch(
        headless=False,
        args=[
            "--disable-blink-features=AutomationControlled",
            "--no-sandbox",
            "--disable-dev-shm-usage"
        ]
    )
    
    # 创建浏览器上下文
    context = await browser.new_context(
        viewport={'width': 1280, 'height': 720},
        user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    )
    
    page = await context.new_page()
    
    try:
        # 访问知乎登录页面
        print("🌐 正在打开知乎登录页面...")
        await page.goto("https://www.zhihu.com/signin", wait_until="networkidle")
        print("✅ 已打开知乎登录页面")
        print("📝 请在浏览器中手动登录您的知乎账号")
        print("⏰ 登录成功后，请等待10秒或看到'Cookie保存成功'提示")
        
        # 等待用户登录（60秒超时）
        print("⏳ 等待用户登录（60秒超时）...")
        try:
            await page.wait_for_selector(".Avatar", timeout=60000)
            print("✅ 检测到登录成功")
        except:
            print("⚠️  登录超时或未检测到登录状态")
            print("💡 请确保已成功登录知乎账号")
            return False
        
        # 保存cookie
        print("💾 正在保存登录cookie...")
        cookies = await context.cookies()
        
        # 过滤知乎相关的cookie
        zhihu_cookies = [cookie for cookie in cookies if "zhihu" in cookie['domain']]
        
        # 保存到文件
        with open("zhihu_cookies.json", "w", encoding="utf-8") as f:
            json.dump(zhihu_cookies, f, indent=2, ensure_ascii=False)
        
        print("🎉 Cookie保存成功！")
        print("📁 文件已保存至: zhihu_cookies.json")
        print("🚀 现在可以使用zhihu_truly_auto_publish.py实现完全自动化发布了")
        
        # 显示保存的cookie数量
        print(f"📊 共保存了 {len(zhihu_cookies)} 个cookie")
        
        return True
        
    except Exception as e:
        print(f"❌ 保存cookie时出错: {e}")
        return False
    finally:
        await browser.close()
        print("🔚 浏览器已关闭")

async def main():
    """主函数"""
    success = await save_zhihu_cookies()
    if success:
        print("\n" + "=" * 60)
        print("✅ 知乎cookie保存完成！")
        print("💡 下一步：运行 zhihu_truly_auto_publish.py 实现完全自动化发布")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ 知乎cookie保存失败！")
        print("💡 请重新运行此脚本并确保成功登录知乎账号")
        print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())