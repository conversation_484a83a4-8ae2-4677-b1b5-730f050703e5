#!/usr/bin/env python3
"""
知乎完全自动化发布脚本
自动完成登录、专栏选择、标签添加和文章发布
"""
import asyncio
import json
import os
from playwright.async_api import async_playwright
from datetime import datetime

class ZhihuAutoPublisher:
    def __init__(self):
        self.browser = None
        self.page = None
        self.is_logged_in = False
        
    async def init_browser(self):
        """初始化浏览器"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,  # 设置为True可以无头模式运行
            args=[
                "--disable-blink-features=AutomationControlled",
                "--no-sandbox",
                "--disable-dev-shm-usage"
            ]
        )
        self.page = await self.browser.new_page()
        
        # 设置用户代理
        await self.page.set_user_agent(
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        
    async def check_login_status(self):
        """检查登录状态"""
        try:
            await self.page.goto("https://www.zhihu.com", wait_until="networkidle")
            await asyncio.sleep(2)
            
            # 检查是否有登录按钮
            login_button = await self.page.query_selector("a[href*='login']")
            if login_button:
                print("🔐 需要登录")
                return False
            else:
                # 检查是否有用户头像
                avatar = await self.page.query_selector(".Avatar, .avatar")
                if avatar:
                    print("✅ 已登录")
                    return True
                else:
                    print("🔍 登录状态不确定")
                    return False
        except Exception as e:
            print(f"❌ 检查登录状态失败: {e}")
            return False
    
    async def login_with_cookies(self):
        """使用cookie登录"""
        try:
            # 尝试从文件加载cookie
            cookie_file = "zhihu_cookies.json"
            if os.path.exists(cookie_file):
                with open(cookie_file, 'r') as f:
                    cookies = json.load(f)
                
                await self.page.context.add_cookies(cookies)
                await self.page.reload(wait_until="networkidle")
                await asyncio.sleep(2)
                
                # 检查登录状态
                if await self.check_login_status():
                    self.is_logged_in = True
                    return True
            
            print("⚠️  Cookie登录失败，需要手动登录")
            return False
            
        except Exception as e:
            print(f"❌ Cookie登录失败: {e}")
            return False
    
    async def manual_login(self):
        """手动登录流程"""
        try:
            print("🔐 请手动登录知乎...")
            await self.page.goto("https://www.zhihu.com/signin", wait_until="networkidle")
            
            # 等待用户登录
            await asyncio.sleep(30)  # 给用户30秒时间登录
            
            # 检查登录状态
            if await self.check_login_status():
                self.is_logged_in = True
                
                # 保存cookie
                cookies = await self.page.context.cookies()
                with open("zhihu_cookies.json", 'w') as f:
                    json.dump(cookies, f)
                print("✅ 登录成功，已保存cookie")
                return True
            else:
                print("❌ 登录失败")
                return False
                
        except Exception as e:
            print(f"❌ 登录过程出错: {e}")
            return False
    
    async def navigate_to_write_page(self):
        """导航到写作页面"""
        try:
            print("🔗 导航到知乎写作页面...")
            await self.page.goto("https://zhuanlan.zhihu.com/write", wait_until="networkidle")
            await asyncio.sleep(3)
            print("✅ 已到达写作页面")
            return True
        except Exception as e:
            print(f"❌ 导航到写作页面失败: {e}")
            return False
    
    async def fill_article_content(self, title, content):
        """填写文章内容"""
        try:
            # 填写标题
            print("🎯 填写文章标题...")
            title_selectors = [
                "input[placeholder*='标题']",
                ".WriteIndex-titleInput input",
                "input[type='text']"
            ]
            
            for selector in title_selectors:
                try:
                    title_input = await self.page.wait_for_selector(selector, timeout=5000)
                    if title_input:
                        await title_input.click()
                        await title_input.fill(title)
                        print(f"✅ 已填写标题: {title}")
                        break
                except:
                    continue
            
            # 填写内容
            print("📝 填写文章内容...")
            content_selectors = [
                ".public-DraftEditor-content",
                "[data-text='true']",
                ".DraftEditor-root",
                "div[contenteditable='true']"
            ]
            
            for selector in content_selectors:
                try:
                    content_editor = await self.page.wait_for_selector(selector, timeout=5000)
                    if content_editor:
                        await content_editor.click()
                        await content_editor.fill(content)
                        print("✅ 已填写文章内容")
                        break
                except:
                    continue
            
            return True
            
        except Exception as e:
            print(f"❌ 填写内容失败: {e}")
            return False
    
    async def add_tags(self, tags):
        """添加标签"""
        try:
            if not tags:
                return True
                
            print("🏷️  添加标签...")
            
            # 查找标签输入框
            tag_input = await self.page.wait_for_selector(
                "input[placeholder*='添加话题'], input[placeholder*='标签']", 
                timeout=5000
            )
            
            if tag_input:
                for tag in tags:
                    await tag_input.click()
                    await tag_input.fill(tag)
                    await asyncio.sleep(1)
                    await tag_input.press("Enter")
                    await asyncio.sleep(0.5)
                    print(f"✅ 已添加标签: {tag}")
            
            return True
            
        except Exception as e:
            print(f"⚠️ 添加标签失败: {e}")
            return True  # 标签不是必须的，继续执行
    
    async def select_column(self, column_name=None):
        """选择专栏"""
        try:
            if not column_name:
                print("📂 使用默认发布，不选择特定专栏")
                return True
            
            print(f"📂 选择专栏: {column_name}")
            
            # 查找专栏选择器
            column_selector = await self.page.wait_for_selector(
                ".WriteIndex-columnSelect, .column-select", 
                timeout=5000
            )
            
            if column_selector:
                await column_selector.click()
                await asyncio.sleep(1)
                
                # 选择指定专栏
                column_option = await self.page.query_selector(f"text='{column_name}'")
                if column_option:
                    await column_option.click()
                    print(f"✅ 已选择专栏: {column_name}")
                else:
                    print(f"⚠️ 未找到专栏: {column_name}")
            
            return True
            
        except Exception as e:
            print(f"⚠️ 选择专栏失败: {e}")
            return True  # 专栏选择不是必须的，继续执行
    
    async def publish_article(self):
        """发布文章"""
        try:
            print("🚀 准备发布文章...")
            
            # 查找发布按钮
            publish_buttons = [
                "button:has-text('发布')",
                ".PublishPanel-publishButton",
                "button[type='submit']",
                ".Button--primary"
            ]
            
            for selector in publish_buttons:
                try:
                    publish_btn = await self.page.wait_for_selector(selector, timeout=5000)
                    if publish_btn:
                        # 检查按钮是否可点击
                        is_disabled = await publish_btn.get_attribute("disabled")
                        if is_disabled:
                            print("⏳ 发布按钮不可用，等待...")
                            await asyncio.sleep(2)
                            continue
                        
                        await publish_btn.click()
                        print("✅ 已点击发布按钮")
                        
                        # 等待发布完成
                        await asyncio.sleep(5)
                        
                        # 检查是否发布成功
                        success_indicators = [
                            "text*='发布成功'",
                            "text*='已发布'",
                            ".success-message"
                        ]
                        
                        for indicator in success_indicators:
                            if await self.page.query_selector(indicator):
                                print("🎉 文章发布成功！")
                                return True
                        
                        # 检查当前URL
                        current_url = self.page.url
                        if "publish" not in current_url and "write" not in current_url:
                            print("🎉 文章发布成功，已跳转到新页面")
                            return True
                        
                        break
                        
                except:
                    continue
            
            print("⚠️ 发布状态不确定，请手动确认")
            return True
            
        except Exception as e:
            print(f"❌ 发布文章失败: {e}")
            return False
    
    async def publish_to_zhihu(self, article_path, tags=None, column_name=None):
        """完整的发布流程"""
        try:
            # 读取文章
            with open(article_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            # 解析标题和内容
            lines = content.strip().split("\n")
            title = lines[0].replace("# ", "") if lines[0].startswith("# ") else "测试文章"
            body = "\n".join(lines[1:]).strip()
            
            print(f"📝 文章标题: {title}")
            print(f"📄 文章内容长度: {len(body)} 字符")
            
            # 初始化浏览器
            await self.init_browser()
            
            # 检查登录状态
            if not await self.check_login_status():
                # 尝试cookie登录
                if not await self.login_with_cookies():
                    # 手动登录
                    if not await self.manual_login():
                        return False
            
            # 导航到写作页面
            if not await self.navigate_to_write_page():
                return False
            
            # 填写内容
            if not await self.fill_article_content(title, body):
                return False
            
            # 选择专栏
            await self.select_column(column_name)
            
            # 添加标签
            await self.add_tags(tags)
            
            # 发布文章
            success = await self.publish_article()
            
            # 等待用户确认
            if success:
                print("⏳ 发布流程完成，等待5秒...")
                await asyncio.sleep(5)
            
            return success
            
        except Exception as e:
            print(f"❌ 发布流程出错: {e}")
            return False
        finally:
            if self.browser:
                await self.browser.close()
                print("🔚 浏览器已关闭")

async def main():
    """主函数"""
    print("=" * 70)
    print("  知乎完全自动化发布工具")
    print("  Article: tests/test-content/test.md")
    print("  Platform: 知乎 (Zhihu)")
    print("  Features: 自动登录、专栏选择、标签添加、文章发布")
    print("=" * 70)
    
    # 配置参数
    article_path = "tests/test-content/test.md"
    tags = ["测试", "TextUp", "自动化", "工具"]
    column_name = None  # 设置为None使用默认发布，或指定专栏名称
    
    publisher = ZhihuAutoPublisher()
    success = await publisher.publish_to_zhihu(article_path, tags, column_name)
    
    if success:
        print("🎉 知乎文章发布完成！")
        print("📱 请检查知乎账号确认文章已发布")
    else:
        print("❌ 发布过程中出现问题，请检查日志")

if __name__ == "__main__":
    asyncio.run(main())