#!/usr/bin/env python3
"""
知乎完全自动化发布脚本 - 终极版
自动完成登录验证、内容填写、标签添加和文章发布
支持cookie持久化和完全无人工干预
"""
import asyncio
import json
import os
import sys
from playwright.async_api import async_playwright
from datetime import datetime

class ZhihuCompleteAutoPublisher:
    def __init__(self):
        self.browser = None
        self.page = None
        self.cookies_file = "zhihu_cookies.json"
        
    async def init_browser(self, headless=False):
        """初始化浏览器"""
        playwright = await async_playwright().start()
        
        # 浏览器启动参数
        browser_args = [
            "--disable-blink-features=AutomationControlled",
            "--no-sandbox",
            "--disable-dev-shm-usage",
            "--disable-gpu",
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
        ]
        
        self.browser = await playwright.chromium.launch(
            headless=headless,
            args=browser_args,
            slow_mo=1000  # 减慢操作速度，避免被检测
        )
        
        # 创建新的浏览器上下文
        context = await self.browser.new_context(
            viewport={'width': 1280, 'height': 720},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        self.page = await context.new_page()
        
        # 加载已保存的cookie
        if os.path.exists(self.cookies_file):
            with open(self.cookies_file, 'r') as f:
                cookies = json.load(f)
            await context.add_cookies(cookies)
            print("✅ 已加载保存的登录cookie")
    
    async def save_cookies(self):
        """保存当前cookie到文件"""
        try:
            cookies = await self.page.context.cookies()
            with open(self.cookies_file, 'w') as f:
                json.dump(cookies, f, indent=2)
            print("✅ 已保存登录cookie")
        except Exception as e:
            print(f"⚠️ 保存cookie失败: {e}")
    
    async def check_and_login(self):
        """检查登录状态并在需要时登录"""
        try:
            print("🔍 检查登录状态...")
            await self.page.goto("https://www.zhihu.com", wait_until="networkidle")
            await asyncio.sleep(2)
            
            # 检查登录状态
            login_indicators = [
                ".Avatar",
                ".avatar",
                "[data-za-detail-view-id*='avatar']",
                ".AppHeader-userInfo"
            ]
            
            is_logged_in = False
            for selector in login_indicators:
                try:
                    element = await self.page.wait_for_selector(selector, timeout=5000)
                    if element:
                        is_logged_in = True
                        break
                except:
                    continue
            
            if is_logged_in:
                print("✅ 已登录状态正常")
                await self.save_cookies()
                return True
            else:
                print("🔐 需要重新登录")
                return await self.perform_login()
                
        except Exception as e:
            print(f"❌ 检查登录状态失败: {e}")
            return False
    
    async def perform_login(self):
        """执行登录流程"""
        try:
            print("🔐 启动登录流程...")
            await self.page.goto("https://www.zhihu.com/signin", wait_until="networkidle")
            
            # 等待登录完成
            print("⏳ 等待用户登录（60秒超时）...")
            
            # 监听登录成功
            try:
                await self.page.wait_for_selector(".Avatar", timeout=60000)
                print("✅ 登录成功")
                await self.save_cookies()
                return True
            except:
                print("❌ 登录超时")
                return False
                
        except Exception as e:
            print(f"❌ 登录失败: {e}")
            return False
    
    async def navigate_to_write(self):
        """导航到写作页面"""
        try:
            print("📝 导航到写作页面...")
            await self.page.goto("https://zhuanlan.zhihu.com/write", wait_until="networkidle")
            
            # 等待页面完全加载
            await asyncio.sleep(3)
            
            # 检查是否有弹窗或提示
            try:
                close_buttons = await self.page.query_selector_all("button:has-text('关闭'), .close")
                for btn in close_buttons:
                    await btn.click()
                    await asyncio.sleep(0.5)
            except:
                pass
            
            print("✅ 已到达写作页面")
            return True
            
        except Exception as e:
            print(f"❌ 导航到写作页面失败: {e}")
            return False
    
    async def fill_content(self, title, content):
        """填写文章内容"""
        try:
            print("🎯 开始填写内容...")
            
            # 填写标题
            title_selectors = [
                "input[placeholder*='标题']",
                ".WriteIndex-titleInput input",
                "input[type='text']",
                "[data-testid='title-input']"
            ]
            
            for selector in title_selectors:
                try:
                    title_input = await self.page.wait_for_selector(selector, timeout=10000)
                    if title_input:
                        await title_input.click()
                        await title_input.clear()
                        await title_input.fill(title)
                        await asyncio.sleep(1)
                        print(f"✅ 已填写标题: {title}")
                        break
                except:
                    continue
            
            # 填写内容
            content_selectors = [
                ".public-DraftEditor-content",
                ".DraftEditor-root",
                "div[contenteditable='true']",
                "[data-testid='content-editor']"
            ]
            
            for selector in content_selectors:
                try:
                    content_editor = await self.page.wait_for_selector(selector, timeout=10000)
                    if content_editor:
                        await content_editor.click()
                        await content_editor.clear()
                        await content_editor.fill(content)
                        await asyncio.sleep(1)
                        print("✅ 已填写文章内容")
                        break
                except:
                    continue
            
            return True
            
        except Exception as e:
            print(f"❌ 填写内容失败: {e}")
            return False
    
    async def add_tags(self, tags):
        """添加标签"""
        if not tags:
            return True
            
        try:
            print("🏷️  添加标签...")
            
            # 查找标签输入区域
            tag_container = await self.page.wait_for_selector(
                ".WriteIndex-topicInput, .topic-input, [data-testid='topic-input']",
                timeout=10000
            )
            
            if tag_container:
                tag_input = await tag_container.query_selector("input")
                if tag_input:
                    for tag in tags:
                        await tag_input.click()
                        await tag_input.fill(tag)
                        await asyncio.sleep(2)
                        
                        # 等待下拉建议
                        try:
                            suggestion = await self.page.wait_for_selector(
                                ".topic-suggestion-item, .topic-item",
                                timeout=5000
                            )
                            if suggestion:
                                await suggestion.click()
                        except:
                            # 如果没有建议，直接按回车
                            await tag_input.press("Enter")
                        
                        await asyncio.sleep(1)
                        print(f"✅ 已添加标签: {tag}")
            
            return True
            
        except Exception as e:
            print(f"⚠️ 添加标签失败: {e}")
            return True
    
    async def publish_article(self):
        """发布文章"""
        try:
            print("🚀 准备发布文章...")
            
            # 滚动到页面底部确保按钮可见
            await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await asyncio.sleep(1)
            
            # 查找发布按钮
            publish_selectors = [
                "button:has-text('发布'):visible",
                ".PublishPanel-publishButton:visible",
                "button[type='submit']:visible",
                ".Button--primary:visible",
                "[data-testid='publish-button']:visible"
            ]
            
            for selector in publish_selectors:
                try:
                    publish_btn = await self.page.wait_for_selector(selector, timeout=10000)
                    if publish_btn:
                        # 检查按钮是否可点击
                        is_disabled = await publish_btn.get_attribute("disabled")
                        if is_disabled:
                            print("⏳ 发布按钮不可用，等待...")
                            await asyncio.sleep(2)
                            continue
                        
                        await publish_btn.scroll_into_view_if_needed()
                        await publish_btn.click()
                        print("✅ 已点击发布按钮")
                        
                        # 等待发布完成
                        await asyncio.sleep(5)
                        
                        # 检查发布结果
                        success_indicators = [
                            "text*='发布成功'",
                            "text*='已发布'",
                            ".success-message",
                            ".publish-success"
                        ]
                        
                        for indicator in success_indicators:
                            try:
                                await self.page.wait_for_selector(indicator, timeout=10000)
                                print("🎉 文章发布成功！")
                                return True
                            except:
                                continue
                        
                        # 检查URL变化
                        current_url = self.page.url
                        if "publish" not in current_url and "write" not in current_url:
                            print("🎉 文章发布成功，已跳转到文章页面")
                            return True
                        
                        break
                        
                except Exception as e:
                    print(f"⚠️ 发布按钮查找失败: {e}")
                    continue
            
            print("⚠️ 发布状态不确定，请手动确认")
            return False
            
        except Exception as e:
            print(f"❌ 发布文章失败: {e}")
            return False
    
    async def publish_article_to_zhihu(self, article_path, tags=None, column_name=None):
        """完整的发布流程"""
        try:
            # 读取文章
            print("📄 读取文章...")
            with open(article_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            # 解析标题和内容
            lines = content.strip().split("\n")
            title = lines[0].replace("# ", "") if lines[0].startswith("# ") else "测试文章"
            body = "\n".join(lines[1:]).strip()
            
            print(f"📝 文章标题: {title}")
            print(f"📊 内容长度: {len(body)} 字符")
            
            # 初始化浏览器
            await self.init_browser(headless=False)
            
            # 检查登录状态
            if not await self.check_and_login():
                print("❌ 登录失败，终止发布")
                return False
            
            # 导航到写作页面
            if not await self.navigate_to_write():
                print("❌ 导航失败，终止发布")
                return False
            
            # 填写内容
            if not await self.fill_content(title, body):
                print("❌ 内容填写失败，终止发布")
                return False
            
            # 添加标签
            await self.add_tags(tags)
            
            # 发布文章
            success = await self.publish_article()
            
            if success:
                print("🎉 知乎文章发布完成！")
                print(f"📱 文章标题: {title}")
                print(f"⏰ 发布时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print("🔗 请检查知乎账号确认文章已发布")
            
            return success
            
        except Exception as e:
            print(f"❌ 发布流程出错: {e}")
            return False
        finally:
            if self.browser:
                await self.browser.close()
                print("🔚 浏览器已关闭")

async def main():
    """主函数"""
    print("=" * 80)
    print("  🚀 知乎完全自动化发布工具 - 终极版")
    print("  Article: tests/test-content/test.md")
    print("  Features: 自动登录验证 + 内容填写 + 标签添加 + 一键发布")
    print("=" * 80)
    
    # 配置参数
    article_path = "tests/test-content/test.md"
    tags = ["测试", "TextUp", "自动化工具", "效率提升", "内容创作"]
    
    publisher = ZhihuCompleteAutoPublisher()
    
    # 检查文章文件
    if not os.path.exists(article_path):
        print(f"❌ 文章文件不存在: {article_path}")
        return
    
    success = await publisher.publish_article_to_zhihu(article_path, tags)
    
    if success:
        print("\n" + "=" * 80)
        print("🎉 发布成功！")
        print("📱 请打开知乎APP或网页版查看已发布的文章")
        print("=" * 80)
    else:
        print("\n" + "=" * 80)
        print("❌ 发布失败，请检查错误信息")
        print("💡 可以尝试重新运行脚本或手动发布")
        print("=" * 80)

if __name__ == "__main__":
    asyncio.run(main())