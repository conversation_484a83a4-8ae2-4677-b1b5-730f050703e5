#!/usr/bin/env python3
"""
知乎适配器变更测试脚本

本脚本用于验证知乎适配器从HTTP API迁移到Playwright自动化后的功能完整性。

测试内容：
1. 导入兼容性测试
2. 工厂模式测试
3. 适配器实例化测试
4. 基础功能测试
5. 错误处理测试

运行方法：
python test_zhihu_changes.py
"""

import sys
import warnings
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def test_imports():
    """测试导入功能"""
    print("🔍 测试1: 导入兼容性测试")

    try:
        # 测试基础导入
        from textup.adapters import ZhihuAdapter, ZhihuPlaywrightAdapter
        print("  ✅ 基础适配器导入成功")

        # 测试工厂导入
        from textup.adapters.factory import create_adapter, AdapterFactory
        print("  ✅ 工厂模式导入成功")

        # 测试模型导入
        from textup.models import Platform, TransformedContent, ContentFormat
        print("  ✅ 模型导入成功")

        # 检查适配器是否为同一个类
        if ZhihuAdapter == ZhihuPlaywrightAdapter:
            print("  ✅ ZhihuAdapter正确指向ZhihuPlaywrightAdapter")
        else:
            print("  ❌ ZhihuAdapter没有正确指向ZhihuPlaywrightAdapter")
            return False

        return True

    except ImportError as e:
        print(f"  ❌ 导入失败: {e}")
        return False

def test_factory_pattern():
    """测试工厂模式"""
    print("\n🔍 测试2: 工厂模式测试")

    try:
        from textup.adapters.factory import create_adapter, get_recommended_adapter_type

        # 测试自动选择
        adapter_auto = create_adapter('zhihu', 'auto')
        print(f"  ✅ 自动选择适配器: {type(adapter_auto).__name__}")

        # 测试明确指定API类型
        adapter_api = create_adapter('zhihu', 'api')
        print(f"  ✅ API类型适配器: {type(adapter_api).__name__}")

        # 测试明确指定Playwright类型
        adapter_playwright = create_adapter('zhihu', 'playwright')
        print(f"  ✅ Playwright类型适配器: {type(adapter_playwright).__name__}")

        # 验证所有适配器都是同一类型
        if (type(adapter_auto) == type(adapter_api) == type(adapter_playwright)):
            print("  ✅ 所有适配器类型一致（都是Playwright实现）")
        else:
            print("  ❌ 适配器类型不一致")
            return False

        # 测试推荐适配器类型
        recommended = get_recommended_adapter_type('zhihu')
        print(f"  ✅ 推荐的适配器类型: {recommended}")

        return True

    except Exception as e:
        print(f"  ❌ 工厂模式测试失败: {e}")
        return False

def test_adapter_initialization():
    """测试适配器实例化"""
    print("\n🔍 测试3: 适配器实例化测试")

    try:
        from textup.adapters import ZhihuPlaywrightAdapter
        from textup.models import Platform

        # 测试基础实例化
        adapter = ZhihuPlaywrightAdapter()
        print("  ✅ 基础实例化成功")

        # 测试平台属性
        if adapter.platform == Platform.ZHIHU:
            print("  ✅ 平台属性正确")
        else:
            print("  ❌ 平台属性错误")
            return False

        # 测试必需的认证字段
        required_creds = adapter.required_credentials
        if isinstance(required_creds, list) and len(required_creds) > 0:
            print(f"  ✅ 必需认证字段: {required_creds}")
        else:
            print("  ❌ 必需认证字段配置错误")
            return False

        # 测试带参数的实例化（使用基础参数）
        adapter_with_params = ZhihuPlaywrightAdapter()
        print("  ✅ 带参数实例化成功")

        return True

    except Exception as e:
        print(f"  ❌ 适配器实例化测试失败: {e}")
        return False

async def test_basic_functionality():
    """测试基础功能"""
    print("\n🔍 测试4: 基础功能测试")

    try:
        from textup.adapters import ZhihuPlaywrightAdapter
        from textup.models import TransformedContent, ContentFormat

        # 创建适配器实例
        adapter = ZhihuPlaywrightAdapter()

        # 测试内容验证
        test_content = TransformedContent(
            title="测试文章",
            content="这是一个测试文章的内容。",
            content_format=ContentFormat.MARKDOWN,
            html="<p>这是一个测试文章的内容。</p>",
            text="这是一个测试文章的内容。"
        )

        # 调用格式验证（不连接网络）
        validation_result = adapter._validate_format_impl(test_content)

        if validation_result.is_valid:
            print("  ✅ 内容格式验证通过")
        else:
            print(f"  ⚠️  内容格式验证警告: {validation_result.errors}")

        # 测试基础URL配置
        base_url = adapter.base_url
        if base_url and isinstance(base_url, str):
            print(f"  ✅ 基础URL配置: {base_url}")
        else:
            print("  ❌ 基础URL配置错误")
            return False

        return True

    except Exception as e:
        print(f"  ❌ 基础功能测试失败: {e}")
        return False

def test_deprecation_warnings():
    """测试弃用警告"""
    print("\n🔍 测试5: 弃用警告测试")

    try:
        # 捕获警告
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")

            # 这应该触发弃用警告
            from textup.adapters.zhihu import ZhihuAdapter

            if len(w) > 0 and issubclass(w[0].category, DeprecationWarning):
                print("  ✅ 弃用警告正确触发")
                print(f"  📝 警告信息: {w[0].message}")
            else:
                print("  ⚠️  弃用警告未触发（可能已被过滤）")

        return True

    except Exception as e:
        print(f"  ❌ 弃用警告测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n🔍 测试6: 错误处理测试")

    try:
        from textup.adapters.factory import create_adapter
        from textup.utils import TextUpError

        # 测试无效平台
        try:
            create_adapter('invalid_platform')
            print("  ❌ 应该抛出错误但没有")
            return False
        except (TextUpError, ValueError):
            print("  ✅ 无效平台错误处理正确")

        # 测试无效适配器类型（对于其他平台）
        try:
            create_adapter('weibo', 'invalid_type')
            print("  ❌ 应该抛出错误但没有")
            return False
        except (TextUpError, ValueError):
            print("  ✅ 无效适配器类型错误处理正确")

        return True

    except Exception as e:
        print(f"  ❌ 错误处理测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 知乎适配器变更测试开始\n")
    print("=" * 60)

    # 执行所有测试
    tests = [
        test_imports,
        test_factory_pattern,
        test_adapter_initialization,
        test_basic_functionality,
        test_deprecation_warnings,
        test_error_handling
    ]

    passed_tests = 0
    total_tests = len(tests)

    for i, test_func in enumerate(tests, 1):
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()

            if result:
                passed_tests += 1
                print(f"  📈 测试 {i} 通过")
            else:
                print(f"  📉 测试 {i} 失败")

        except Exception as e:
            print(f"  💥 测试 {i} 异常: {e}")

        print("-" * 60)

    # 输出测试结果
    print(f"\n📊 测试结果: {passed_tests}/{total_tests} 通过")

    if passed_tests == total_tests:
        print("🎉 所有测试通过！知乎适配器变更成功。")
        print("\n✨ 变更总结:")
        print("  - 知乎适配器现在只使用Playwright自动化方式")
        print("  - 保持了向后兼容性，旧代码无需修改")
        print("  - HTTP API相关代码已移除，因为知乎没有公开API")
        print("  - 工厂模式正确处理适配器类型选择")
        return 0
    else:
        print("❌ 部分测试失败，请检查问题并修复。")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
