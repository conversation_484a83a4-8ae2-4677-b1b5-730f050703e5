#!/usr/bin/env python3
import asyncio
from playwright.async_api import async_playwright

async def publish_to_zhihu():
    print("🚀 启动知乎文章发布脚本...")
    
    # 读取测试文章
    with open("tests/test-content/test.md", "r", encoding="utf-8") as f:
        content = f.read()
    
    lines = content.strip().split("\n")
    title = lines[0].replace("# ", "") if lines[0].startswith("# ") else "测试文章"
    body = "\n".join(lines[1:]).strip()
    
    print(f"📝 文章标题: {title}")
    print(f"📄 文章内容: {body[:100]}...")
    
    async with async_playwright() as p:
        print("🌐 启动Chrome浏览器（可见模式）...")
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        try:
            print("🔗 导航到知乎写作页面...")
            await page.goto("https://zhuanlan.zhihu.com/write")
            await page.wait_for_load_state("networkidle")
            await asyncio.sleep(5)
            
            print("⏳ 浏览器已打开知乎写作页面，请手动登录并发布文章")
            print("浏览器将保持打开30秒钟...")
            await asyncio.sleep(30)
            
        except Exception as e:
            print(f"❌ 发布过程中出错: {e}")
            await asyncio.sleep(5)
        finally:
            await browser.close()
            print("🔚 浏览器已关闭")

if __name__ == "__main__":
    asyncio.run(publish_to_zhihu())
