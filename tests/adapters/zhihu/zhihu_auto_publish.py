#!/usr/bin/env python3
"""
知乎文章自动发布脚本
使用Playwright自动化发布文章到知乎平台
"""
import asyncio
from playwright.async_api import async_playwright

async def publish_to_zhihu():
    print("🚀 启动知乎文章发布脚本...")
    
    # 读取测试文章
    with open("tests/test-content/test.md", "r", encoding="utf-8") as f:
        content = f.read()
    
    # 解析标题和内容
    lines = content.strip().split("\n")
    title = lines[0].replace("# ", "") if lines[0].startswith("# ") else "测试文章"
    body = "\n".join(lines[1:]).strip()
    
    print(f"📝 文章标题: {title}")
    print(f"📄 文章内容预览: {body[:100]}...")
    
    async with async_playwright() as p:
        print("🌐 启动Chrome浏览器（可见模式）...")
        browser = await p.chromium.launch(
            headless=False,
            args=["--disable-blink-features=AutomationControlled"]
        )
        page = await browser.new_page()
        
        try:
            print("🔗 导航到知乎写作页面...")
            await page.goto("https://zhuanlan.zhihu.com/write")
            await page.wait_for_load_state("networkidle")
            
            print("⏳ 等待页面加载完成...")
            await asyncio.sleep(3)
            
            # 尝试查找并填写标题
            print("🎯 尝试查找标题输入框...")
            title_selectors = [
                "input[placeholder*=\"标题\"]",
                ".WriteIndex-titleInput input", 
                "input[type=\"text\"]"
            ]
            
            for selector in title_selectors:
                try:
                    title_input = await page.wait_for_selector(selector, timeout=2000)
                    if title_input:
                        print(f"✅ 找到标题输入框: {selector}")
                        await title_input.click()
                        await title_input.fill(title)
                        print(f"⌨️  已输入标题: {title}")
                        break
                except:
                    continue
            
            # 等待一下让标题输入生效
            await asyncio.sleep(1)
            
            # 尝试查找并填写内容
            print("📝 尝试查找内容编辑区...")
            content_selectors = [
                ".public-DraftEditor-content",
                "[data-text=\"true\"]",
                ".DraftEditor-root",
                "div[contenteditable=\"true\"]"
            ]
            
            for selector in content_selectors:
                try:
                    content_editor = await page.wait_for_selector(selector, timeout=2000)
                    if content_editor:
                        print(f"✅ 找到内容编辑器: {selector}")
                        await content_editor.click()
                        await content_editor.fill(body)
                        print("⌨️  已输入文章内容")
                        break
                except:
                    continue
            
            print("⏳ 内容已填写，请手动检查并发布文章...")
            print("浏览器将保持打开60秒钟供您操作...")
            await asyncio.sleep(60)
            
        except Exception as e:
            print(f"❌ 发布过程中出错: {e}")
            print("⏳ 浏览器将保持打开10秒钟...")
            await asyncio.sleep(10)
        finally:
            await browser.close()
            print("🔚 浏览器已关闭")
            print("📄 如果文章发布成功，请到知乎查看发布结果")

if __name__ == "__main__":
    print("=" * 60)
    print("  知乎文章自动发布工具")
    print("  Article: tests/test-content/test.md")
    print("  Platform: 知乎 (Zhihu)")
    print("=" * 60)
    asyncio.run(publish_to_zhihu())
