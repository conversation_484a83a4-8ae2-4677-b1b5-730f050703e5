#!/usr/bin/env python3
"""
知乎调试版发布脚本
用于手动调试页面元素选择器
"""
import asyncio
import os
from playwright.async_api import async_playwright

class ZhihuDebugPublisher:
    def __init__(self):
        self.browser = None
        self.page = None
        
    async def init_browser(self, headless=False):
        """初始化浏览器"""
        playwright = await async_playwright().start()
        
        browser_args = [
            "--disable-blink-features=AutomationControlled",
            "--no-sandbox",
            "--disable-dev-shm-usage",
            "--disable-gpu",
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
        ]
        
        self.browser = await playwright.chromium.launch(
            headless=headless,
            args=browser_args,
            slow_mo=1000
        )
        
        context = await self.browser.new_context(
            viewport={'width': 1280, 'height': 720},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        self.page = await context.new_page()
        return True
    
    async def debug_page_structure(self):
        """调试页面结构"""
        try:
            # 读取文章
            print("📄 读取文章...")
            with open("tests/test-content/test.md", "r", encoding="utf-8") as f:
                content = f.read()
            
            lines = content.strip().split("\n")
            title = lines[0].replace("# ", "") if lines[0].startswith("# ") else "测试文章"
            body = "\n".join(lines[1:]).strip()
            
            print(f"📝 文章标题: {title}")
            
            # 初始化浏览器
            await self.init_browser(headless=False)
            
            # 导航到知乎登录页面
            print("🌐 正在打开知乎登录页面...")
            await self.page.goto("https://www.zhihu.com/signin", wait_until="networkidle")
            
            print("📝 请手动登录知乎账号...")
            print("⏳ 等待用户登录（90秒超时）...")
            
            try:
                await self.page.wait_for_selector(".Avatar", timeout=90000)
                print("✅ 登录成功！")
            except:
                print("❌ 登录超时")
                return
            
            # 导航到写作页面
            print("📝 正在导航到写作页面...")
            await self.page.goto("https://zhuanlan.zhihu.com/write", wait_until="networkidle")
            await asyncio.sleep(3)
            
            # 等待用户查看页面
            print("🔍 页面已加载，请查看浏览器中的写作页面")
            print("📋 以下是当前页面的关键元素：")
            
            # 获取页面信息
            page_info = await self.page.evaluate('''
                () => {
                    const info = {
                        title: document.title,
                        url: window.location.href,
                        selectors: []
                    };
                    
                    // 查找标题输入框
                    const titleInputs = document.querySelectorAll('input[type="text"], input[placeholder*="标题"], .WriteIndex-titleInput input');
                    titleInputs.forEach((el, i) => {
                        info.selectors.push({
                            type: 'title_input',
                            selector: el.tagName.toLowerCase() + (el.className ? '.' + el.className.split(' ').join('.') : '') + (el.placeholder ? `[placeholder="${el.placeholder}"]` : ''),
                            text: el.value || el.placeholder || '',
                            visible: el.offsetParent !== null
                        });
                    });
                    
                    // 查找内容编辑器
                    const contentEditors = document.querySelectorAll('[contenteditable="true"], .public-DraftEditor-content, .DraftEditor-root');
                    contentEditors.forEach((el, i) => {
                        info.selectors.push({
                            type: 'content_editor',
                            selector: el.tagName.toLowerCase() + (el.className ? '.' + el.className.split(' ').join('.') : ''),
                            visible: el.offsetParent !== null
                        });
                    });
                    
                    // 查找标签区域
                    const tagAreas = document.querySelectorAll('[class*="topic" i], [class*="tag" i]');
                    tagAreas.forEach((el, i) => {
                        info.selectors.push({
                            type: 'tag_area',
                            selector: el.tagName.toLowerCase() + (el.className ? '.' + el.className.split(' ').join('.') : ''),
                            visible: el.offsetParent !== null
                        });
                    });
                    
                    // 查找发布按钮
                    const publishButtons = document.querySelectorAll('button:contains("发布"), .PublishPanel-publishButton, button[type="submit"]');
                    publishButtons.forEach((el, i) => {
                        info.selectors.push({
                            type: 'publish_button',
                            selector: el.tagName.toLowerCase() + (el.className ? '.' + el.className.split(' ').join('.') : '') + (el.textContent ? `[text="${el.textContent.trim()}"]` : ''),
                            text: el.textContent || '',
                            visible: el.offsetParent !== null,
                            disabled: el.disabled
                        });
                    });
                    
                    return info;
                }
            ''')
            
            print("📊 页面元素信息：")
            for selector in page_info.selectors:
                print(f"  {selector.type}: {selector.selector} - {'可见' if selector.visible else '隐藏'} {selector.text or ''}")
            
            # 等待用户操作
            print("\n🔧 调试完成！")
            print("📱 请手动完成发布或记录选择器信息")
            
            # 保持浏览器打开
            input("按Enter键关闭浏览器...")
            
        except Exception as e:
            print(f"❌ 调试出错: {e}")
        finally:
            if self.browser:
                await self.browser.close()
                print("🔚 浏览器已关闭")

async def main():
    """主函数"""
    print("=" * 80)
    print("  🔍 知乎调试版发布工具")
    print("  用于手动调试页面元素选择器")
    print("=" * 80)
    
    debugger = ZhihuDebugPublisher()
    await debugger.debug_page_structure()

if __name__ == "__main__":
    asyncio.run(main())