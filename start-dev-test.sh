#!/bin/bash

# TextUp 开发测试启动脚本
# =======================
# 一键启动开发测试环境的简化入口

set -e

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 项目信息
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"
DEV_SCRIPTS_DIR="$PROJECT_ROOT/dev-scripts"

# 显示欢迎信息
show_welcome() {
    clear
    echo -e "${BLUE}╔═══════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║${WHITE}                    TextUp 开发测试快速启动                           ${BLUE}║${NC}"
    echo -e "${BLUE}║${CYAN}                   Development Testing Quick Start                    ${BLUE}║${NC}"
    echo -e "${BLUE}╠═══════════════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${BLUE}║${NC} 🚀 快速开始开发测试环境                                              ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC} 📖 参考文档: docs/development-testing-guide.md                      ${BLUE}║${NC}"
    echo -e "${BLUE}║${NC} ⏰ 启动时间: $(date '+%Y-%m-%d %H:%M:%S')${BLUE}                                        ║${NC}"
    echo -e "${BLUE}╚═══════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 快速环境检查
quick_env_check() {
    local issues=0

    echo -e "${CYAN}🔍 快速环境检查...${NC}"

    # 检查是否在正确目录
    if [ ! -f "pyproject.toml" ]; then
        echo -e "${RED}❌ 错误: 请在TextUp项目根目录运行此脚本${NC}"
        echo "   当前目录: $(pwd)"
        echo "   期望文件: pyproject.toml"
        exit 1
    fi

    # 检查Python
    if command -v python >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Python: $(python --version)${NC}"
    else
        echo -e "${RED}❌ Python: 未安装${NC}"
        ((issues++))
    fi

    # 检查uv
    if command -v uv >/dev/null 2>&1; then
        echo -e "${GREEN}✅ UV: $(uv --version)${NC}"
    else
        echo -e "${YELLOW}⚠️ UV: 未安装 (推荐安装)${NC}"
    fi

    # 检查虚拟环境
    if [ -n "$VIRTUAL_ENV" ]; then
        echo -e "${GREEN}✅ 虚拟环境: 已激活${NC}"
    else
        echo -e "${YELLOW}⚠️ 虚拟环境: 未激活${NC}"
        if [ -d ".venv" ]; then
            echo -e "   💡 运行: ${CYAN}source .venv/bin/activate${NC}"
        else
            echo -e "   💡 运行: ${CYAN}uv venv && source .venv/bin/activate${NC}"
        fi
    fi

    # 检查源码
    if [ -d "src/textup" ]; then
        echo -e "${GREEN}✅ 源码目录: 存在${NC}"
    else
        echo -e "${RED}❌ 源码目录: src/textup/ 缺失${NC}"
        ((issues++))
    fi

    echo ""

    if [ $issues -gt 0 ]; then
        echo -e "${RED}⚠️ 发现 $issues 个问题需要解决${NC}"
        echo ""
        return 1
    else
        echo -e "${GREEN}🎉 环境检查通过！${NC}"
        echo ""
        return 0
    fi
}

# 显示快速选项
show_quick_options() {
    echo -e "${WHITE}🎯 选择启动方式:${NC}"
    echo ""
    echo -e "${GREEN}  1.${NC} 🎛️  完整控制台     - 启动完整的开发测试主控台 (推荐)"
    echo -e "${GREEN}  2.${NC} 🚀 快速测试      - 立即运行快速环境测试 (30秒)"
    echo -e "${GREEN}  3.${NC} 🧪 完整测试      - 立即运行完整功能测试 (5-10分钟)"
    echo -e "${GREEN}  4.${NC} 🔍 故障排查      - 立即运行环境诊断工具"
    echo ""
    echo -e "${BLUE}  5.${NC} 📖 查看文档      - 打开开发测试指南"
    echo -e "${BLUE}  6.${NC} 🛠️  环境设置      - 初始化开发环境"
    echo ""
    echo -e "${RED}  0.${NC} 🚪 退出程序"
    echo ""
}

# 初始化环境
init_environment() {
    echo -e "${CYAN}🛠️ 初始化开发环境...${NC}"
    echo ""

    # 创建必要目录
    echo "📁 创建必要目录..."
    mkdir -p dev-logs dev-config test-content test-results
    echo -e "${GREEN}✅ 目录创建完成${NC}"

    # 检查虚拟环境
    if [ ! -d ".venv" ]; then
        echo "🐍 创建虚拟环境..."
        if command -v uv >/dev/null 2>&1; then
            uv venv
        else
            python -m venv .venv
        fi
        echo -e "${GREEN}✅ 虚拟环境创建完成${NC}"
    fi

    # 提醒激活虚拟环境
    if [ -z "$VIRTUAL_ENV" ]; then
        echo -e "${YELLOW}💡 请手动激活虚拟环境:${NC}"
        echo -e "   ${CYAN}source .venv/bin/activate${NC}"
        echo ""
    fi

    # 安装依赖
    echo "📦 安装项目依赖..."
    if command -v uv >/dev/null 2>&1; then
        uv pip install -e .
    else
        pip install -e .
    fi
    echo -e "${GREEN}✅ 依赖安装完成${NC}"

    echo ""
    echo -e "${GREEN}🎉 环境初始化完成！${NC}"
    echo ""
    read -p "按回车键继续..."
}

# 打开文档
open_documentation() {
    local doc_file="docs/development-testing-guide.md"

    if [ -f "$doc_file" ]; then
        echo -e "${CYAN}📖 打开开发测试指南...${NC}"

        # 尝试用不同的方式打开文档
        if command -v code >/dev/null 2>&1; then
            code "$doc_file"
            echo "✅ 使用 VS Code 打开文档"
        elif command -v open >/dev/null 2>&1; then
            open "$doc_file"
            echo "✅ 使用系统默认编辑器打开文档"
        elif command -v xdg-open >/dev/null 2>&1; then
            xdg-open "$doc_file"
            echo "✅ 使用系统默认编辑器打开文档"
        else
            echo "📄 请手动查看文档: $doc_file"
        fi
    else
        echo -e "${RED}❌ 文档文件不存在: $doc_file${NC}"
    fi

    echo ""
    read -p "按回车键继续..."
}

# 主程序
main() {
    # 处理命令行参数
    case "$1" in
        --help|-h)
            echo "TextUp 开发测试快速启动脚本"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --help, -h      显示帮助信息"
            echo "  --console       直接启动完整控制台"
            echo "  --quick         直接运行快速测试"
            echo "  --full          直接运行完整测试"
            echo "  --init          初始化开发环境"
            echo ""
            echo "示例:"
            echo "  $0              # 交互式启动"
            echo "  $0 --console    # 直接启动控制台"
            echo "  $0 --quick      # 直接运行快速测试"
            echo ""
            exit 0
            ;;
        --console)
            if [ -f "$DEV_SCRIPTS_DIR/dev-test-master.sh" ]; then
                exec "$DEV_SCRIPTS_DIR/dev-test-master.sh"
            else
                echo -e "${RED}❌ 主控台脚本不存在${NC}"
                exit 1
            fi
            ;;
        --quick)
            if [ -f "$DEV_SCRIPTS_DIR/quick-test.sh" ]; then
                exec "$DEV_SCRIPTS_DIR/quick-test.sh"
            else
                echo -e "${RED}❌ 快速测试脚本不存在${NC}"
                exit 1
            fi
            ;;
        --full)
            if [ -f "$DEV_SCRIPTS_DIR/test-all-platforms.sh" ]; then
                exec "$DEV_SCRIPTS_DIR/test-all-platforms.sh"
            else
                echo -e "${RED}❌ 完整测试脚本不存在${NC}"
                exit 1
            fi
            ;;
        --init)
            show_welcome
            init_environment
            exit 0
            ;;
    esac

    # 交互式模式
    show_welcome

    # 快速环境检查
    if ! quick_env_check; then
        echo -e "${YELLOW}💡 建议先运行环境初始化修复问题${NC}"
        echo ""
    fi

    # 主菜单循环
    while true; do
        show_quick_options
        read -p "请选择操作 (0-6): " choice
        echo ""

        case $choice in
            1)
                if [ -f "$DEV_SCRIPTS_DIR/dev-test-master.sh" ]; then
                    echo -e "${CYAN}🎛️ 启动完整控制台...${NC}"
                    exec "$DEV_SCRIPTS_DIR/dev-test-master.sh"
                else
                    echo -e "${RED}❌ 主控台脚本不存在: $DEV_SCRIPTS_DIR/dev-test-master.sh${NC}"
                fi
                ;;
            2)
                if [ -f "$DEV_SCRIPTS_DIR/quick-test.sh" ]; then
                    echo -e "${CYAN}🚀 运行快速测试...${NC}"
                    "$DEV_SCRIPTS_DIR/quick-test.sh"
                    read -p "按回车键继续..."
                else
                    echo -e "${RED}❌ 快速测试脚本不存在${NC}"
                fi
                ;;
            3)
                if [ -f "$DEV_SCRIPTS_DIR/test-all-platforms.sh" ]; then
                    echo -e "${CYAN}🧪 运行完整测试...${NC}"
                    "$DEV_SCRIPTS_DIR/test-all-platforms.sh"
                    read -p "按回车键继续..."
                else
                    echo -e "${RED}❌ 完整测试脚本不存在${NC}"
                fi
                ;;
            4)
                if [ -f "$DEV_SCRIPTS_DIR/troubleshoot.sh" ]; then
                    echo -e "${CYAN}🔍 运行故障排查...${NC}"
                    "$DEV_SCRIPTS_DIR/troubleshoot.sh"
                    read -p "按回车键继续..."
                else
                    echo -e "${RED}❌ 故障排查脚本不存在${NC}"
                fi
                ;;
            5)
                open_documentation
                ;;
            6)
                init_environment
                ;;
            0)
                echo -e "${CYAN}👋 再见！祝您开发愉快！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请输入 0-6 之间的数字${NC}"
                sleep 1
                clear
                show_welcome
                ;;
        esac

        echo ""
    done
}

# 信号处理
cleanup() {
    echo -e "\n${YELLOW}程序已中断${NC}"
    exit 0
}

trap cleanup INT TERM

# 脚本入口点
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
