#!/bin/bash

# TextUp 一键启动脚本
# TextUp One-Click Startup Script
# Version: 1.0.0
# Author: TextUp Development Team

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目信息
PROJECT_NAME="TextUp"
PROJECT_VERSION="1.0.0"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 打印欢迎信息
print_welcome() {
    clear
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════════════╗"
    echo "║                                                                      ║"
    echo "║                    🚀 欢迎使用 TextUp! 🚀                            ║"
    echo "║                                                                      ║"
    echo "║                    多平台内容发布工具                                  ║"
    echo "║                                                                      ║"
    echo "║   支持平台: 📱微博  📚知乎  📰今日头条  ✨更多平台                       ║"
    echo "║                                                                      ║"
    echo "╚══════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo
}

# 打印状态信息
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

# 打印警告信息
print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 打印错误信息
print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 打印成功信息
print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 检查Python版本
check_python() {
    print_status "检查Python环境..."

    if ! command -v python3 &> /dev/null; then
        print_error "未找到Python3，请先安装Python 3.9或更高版本"
        exit 1
    fi

    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
    PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)

    if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 9 ]); then
        print_error "Python版本过低: $PYTHON_VERSION，需要3.9或更高版本"
        exit 1
    fi

    print_success "Python版本: $PYTHON_VERSION ✓"
}

# 检查并安装uv
check_uv() {
    print_status "检查uv包管理器..."

    if ! command -v uv &> /dev/null; then
        print_warning "未找到uv包管理器，正在安装..."
        if [[ "$OSTYPE" == "darwin"* ]] || [[ "$OSTYPE" == "linux"* ]]; then
            curl -LsSf https://astral.sh/uv/install.sh | sh
            source ~/.bashrc || source ~/.zshrc || true
        else
            print_error "请手动安装uv包管理器: https://astral.sh/uv/"
            exit 1
        fi
    fi

    if command -v uv &> /dev/null; then
        UV_VERSION=$(uv --version)
        print_success "uv版本: $UV_VERSION ✓"
    else
        print_error "uv安装失败，请手动安装"
        exit 1
    fi
}

# 设置虚拟环境
setup_venv() {
    print_status "设置Python虚拟环境..."

    cd "$SCRIPT_DIR"

    if [ ! -d ".venv" ]; then
        print_status "创建虚拟环境..."
        uv venv
    fi

    # 激活虚拟环境
    source .venv/bin/activate

    print_success "虚拟环境已激活 ✓"
}

# 安装依赖
install_dependencies() {
    print_status "安装项目依赖..."

    # 确保在项目目录中
    cd "$SCRIPT_DIR"

    # 安装项目
    uv pip install -e .

    print_success "依赖安装完成 ✓"
}

# 验证安装
verify_installation() {
    print_status "验证TextUp安装..."

    if command -v textup &> /dev/null; then
        TEXTUP_VERSION=$(textup --version 2>/dev/null || echo "unknown")
        print_success "TextUp安装成功: $TEXTUP_VERSION ✓"
    else
        print_error "TextUp安装失败"
        exit 1
    fi
}

# 初始化配置
init_config() {
    print_status "初始化配置目录..."

    # 创建配置目录
    mkdir -p ~/.textup/config
    mkdir -p ~/.textup/logs
    mkdir -p ~/.textup/data

    print_success "配置目录创建完成 ✓"
}

# 显示主菜单
show_menu() {
    echo
    echo -e "${CYAN}╔════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║${NC}              ${PURPLE}TextUp 主菜单${NC}              ${CYAN}║${NC}"
    echo -e "${CYAN}╠════════════════════════════════════════╣${NC}"
    echo -e "${CYAN}║${NC}                                        ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}  ${GREEN}1.${NC} 查看系统状态                    ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}  ${GREEN}2.${NC} 配置管理                        ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}  ${GREEN}3.${NC} 平台认证                        ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}  ${GREEN}4.${NC} 发布文章                        ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}  ${GREEN}5.${NC} 运行测试                        ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}  ${GREEN}6.${NC} 查看帮助                        ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}  ${GREEN}7.${NC} 进入交互模式                    ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}  ${GREEN}0.${NC} 退出                            ${CYAN}║${NC}"
    echo -e "${CYAN}║${NC}                                        ${CYAN}║${NC}"
    echo -e "${CYAN}╚════════════════════════════════════════╝${NC}"
    echo
}

# 查看系统状态
show_status() {
    echo
    print_status "📊 TextUp 系统状态检查"
    echo "----------------------------------------"

    echo -e "${BLUE}🐍 Python版本:${NC} $(python --version)"
    echo -e "${BLUE}📦 uv版本:${NC} $(uv --version)"
    echo -e "${BLUE}🚀 TextUp版本:${NC} $(textup --version 2>/dev/null || echo '未知')"
    echo -e "${BLUE}📁 工作目录:${NC} $(pwd)"
    echo -e "${BLUE}🔧 虚拟环境:${NC} ${VIRTUAL_ENV:-未激活}"

    echo
    echo -e "${BLUE}📋 配置状态:${NC}"
    textup config --list 2>/dev/null | head -10 || echo "   配置未初始化"

    echo
    echo -e "${BLUE}🔐 认证状态:${NC}"
    textup auth --status 2>/dev/null || echo "   认证未配置"

    echo
    echo "按回车键返回主菜单..."
    read
}

# 配置管理
config_management() {
    while true; do
        echo
        echo -e "${PURPLE}═══════════ 配置管理 ═══════════${NC}"
        echo "1. 查看当前配置"
        echo "2. 交互式配置"
        echo "3. 设置单项配置"
        echo "4. 备份配置"
        echo "5. 恢复配置"
        echo "0. 返回主菜单"
        echo
        read -p "请选择操作 (0-5): " config_choice

        case $config_choice in
            1)
                echo
                print_status "当前配置:"
                textup config --list
                echo
                echo "按回车键继续..."
                read
                ;;
            2)
                echo
                print_status "启动交互式配置..."
                textup config --interactive
                ;;
            3)
                echo
                read -p "请输入配置项 (例: app.name): " config_key
                read -p "请输入配置值: " config_value
                textup config --set "${config_key}=${config_value}"
                print_success "配置已保存"
                ;;
            4)
                echo
                read -p "请输入备份文件名 (例: backup.yaml): " backup_file
                textup config --backup "$backup_file"
                print_success "配置已备份到 $backup_file"
                ;;
            5)
                echo
                read -p "请输入要恢复的备份文件: " restore_file
                if [ -f "$restore_file" ]; then
                    textup config --restore "$restore_file"
                    print_success "配置已从 $restore_file 恢复"
                else
                    print_error "备份文件不存在: $restore_file"
                fi
                ;;
            0)
                break
                ;;
            *)
                print_error "无效选择，请重新输入"
                ;;
        esac
    done
}

# 平台认证
platform_auth() {
    while true; do
        echo
        echo -e "${PURPLE}═══════════ 平台认证 ═══════════${NC}"
        echo "1. 查看认证状态"
        echo "2. 微博认证"
        echo "3. 知乎认证"
        echo "4. 今日头条认证"
        echo "5. 撤销认证"
        echo "0. 返回主菜单"
        echo
        read -p "请选择操作 (0-5): " auth_choice

        case $auth_choice in
            1)
                echo
                print_status "认证状态:"
                textup auth --status
                echo
                echo "按回车键继续..."
                read
                ;;
            2)
                echo
                print_status "微博平台认证..."
                textup auth --interactive --platform weibo
                ;;
            3)
                echo
                print_status "知乎平台认证..."
                textup auth --interactive --platform zhihu
                ;;
            4)
                echo
                print_status "今日头条平台认证..."
                textup auth --interactive --platform toutiao
                ;;
            5)
                echo
                echo "可用平台: weibo, zhihu, toutiao"
                read -p "请输入要撤销的平台: " platform
                textup auth --revoke --platform "$platform"
                print_success "已撤销 $platform 平台认证"
                ;;
            0)
                break
                ;;
            *)
                print_error "无效选择，请重新输入"
                ;;
        esac
    done
}

# 发布文章
publish_article() {
    while true; do
        echo
        echo -e "${PURPLE}═══════════ 发布文章 ═══════════${NC}"
        echo "1. 交互式发布"
        echo "2. 指定文件发布"
        echo "3. 批量发布"
        echo "4. 预览模式"
        echo "0. 返回主菜单"
        echo
        read -p "请选择操作 (0-4): " publish_choice

        case $publish_choice in
            1)
                echo
                print_status "启动交互式发布..."
                textup publish --interactive
                ;;
            2)
                echo
                read -p "请输入文章文件路径: " file_path
                if [ ! -f "$file_path" ]; then
                    print_error "文件不存在: $file_path"
                    continue
                fi
                read -p "请输入目标平台 (weibo/zhihu/toutiao 或 weibo,zhihu): " platforms
                textup publish --file "$file_path" --platforms "$platforms"
                ;;
            3)
                echo
                read -p "请输入文章目录路径: " dir_path
                if [ ! -d "$dir_path" ]; then
                    print_error "目录不存在: $dir_path"
                    continue
                fi
                read -p "请输入目标平台 (weibo/zhihu/toutiao 或 weibo,zhihu): " platforms
                textup publish --directory "$dir_path" --platforms "$platforms"
                ;;
            4)
                echo
                read -p "请输入文章文件路径: " file_path
                if [ ! -f "$file_path" ]; then
                    print_error "文件不存在: $file_path"
                    continue
                fi
                read -p "请输入目标平台: " platform
                textup publish --file "$file_path" --platform "$platform" --dry-run
                ;;
            0)
                break
                ;;
            *)
                print_error "无效选择，请重新输入"
                ;;
        esac
    done
}

# 运行测试
run_tests() {
    while true; do
        echo
        echo -e "${PURPLE}═══════════ 运行测试 ═══════════${NC}"
        echo "1. 基础功能测试"
        echo "2. CLI功能测试"
        echo "3. 今日头条专项测试"
        echo "4. 完整测试套件"
        echo "5. 覆盖率测试"
        echo "0. 返回主菜单"
        echo
        read -p "请选择测试类型 (0-5): " test_choice

        case $test_choice in
            1)
                echo
                print_status "运行基础功能测试..."
                uv run pytest tests/test_working_features.py -v
                ;;
            2)
                echo
                print_status "运行CLI功能测试..."
                uv run pytest tests/test_cli_simple_focus.py -v
                ;;
            3)
                echo
                print_status "运行今日头条专项测试..."
                if [ -f "test-toutiao-publish.py" ]; then
                    uv run python test-toutiao-publish.py
                else
                    print_error "今日头条测试脚本不存在"
                fi
                ;;
            4)
                echo
                print_status "运行完整测试套件..."
                uv run pytest tests/ -v
                ;;
            5)
                echo
                print_status "运行覆盖率测试..."
                uv run pytest --cov=src/textup --cov-report=term-missing --cov-report=html
                print_success "覆盖率报告已生成到 htmlcov/ 目录"
                ;;
            0)
                break
                ;;
            *)
                print_error "无效选择，请重新输入"
                ;;
        esac

        if [ $test_choice != "0" ]; then
            echo
            echo "按回车键继续..."
            read
        fi
    done
}

# 查看帮助
show_help() {
    echo
    echo -e "${BLUE}📚 TextUp 帮助信息${NC}"
    echo "════════════════════════════════════════"
    echo
    textup --help
    echo
    echo -e "${BLUE}📖 更多文档:${NC}"
    echo "  • 快速上手指南: docs/QUICK_START_GUIDE.md"
    echo "  • 本地测试指南: docs/LOCAL_TESTING_GUIDE.md"
    echo "  • 部署指南: docs/DEPLOYMENT_GUIDE.md"
    echo
    echo -e "${BLUE}🔗 相关链接:${NC}"
    echo "  • GitHub: https://github.com/textup-team/textup"
    echo "  • 文档: https://textup.readthedocs.io/"
    echo
    echo "按回车键返回主菜单..."
    read
}

# 进入交互模式
enter_interactive_mode() {
    echo
    print_success "进入TextUp交互模式..."
    echo -e "${YELLOW}提示: 输入 'exit' 或按 Ctrl+C 退出交互模式${NC}"
    echo

    # 启动交互式shell
    bash --rcfile <(echo "PS1='(textup) \u@\h:\w$ '; source .venv/bin/activate 2>/dev/null || true")
}

# 主程序流程
main() {
    print_welcome

    # 环境检查和初始化
    check_python
    check_uv
    setup_venv
    install_dependencies
    verify_installation
    init_config

    print_success "🎉 TextUp 启动成功！"

    # 主菜单循环
    while true; do
        show_menu
        read -p "请选择操作 (0-7): " choice

        case $choice in
            1)
                show_status
                ;;
            2)
                config_management
                ;;
            3)
                platform_auth
                ;;
            4)
                publish_article
                ;;
            5)
                run_tests
                ;;
            6)
                show_help
                ;;
            7)
                enter_interactive_mode
                ;;
            0)
                echo
                print_success "感谢使用 TextUp！再见！👋"
                echo
                exit 0
                ;;
            *)
                print_error "无效选择，请输入 0-7"
                echo "按回车键继续..."
                read
                ;;
        esac
    done
}

# 捕获中断信号
trap 'echo -e "\n\n${YELLOW}程序被用户中断${NC}"; exit 1' INT

# 启动主程序
main "$@"
