[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "textup"
version = "1.0.0"
description = "多平台文本内容发布工具 - Multi-platform text content publishing tool"
authors = ["TextUp Team <<EMAIL>>"]
license = "MIT"
readme = "README.md"
homepage = "https://github.com/textup/textup"
repository = "https://github.com/textup/textup"
documentation = "https://github.com/textup/textup#readme"
keywords = ["content-publishing", "multi-platform", "automation", "zhihu", "weibo", "xiaohongshu", "toutiao", "cli"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: System :: Archiving :: Backup",
    "Topic :: Text Processing :: Markup",
]
packages = [{include = "textup", from = "src"}]

[tool.poetry.scripts]
textup = "textup.cli:main"

[tool.poetry.dependencies]
python = "^3.9"
click = "^8.1.7"
pydantic = "^2.5.2"
requests = "^2.31.0"
pyyaml = "^6.0.1"
markdown = "^3.5.1"
python-frontmatter = "^1.0.0"
playwright = "^1.40.0"
cryptography = "^41.0.8"
rich = "^13.7.0"
typer = "^0.9.0"
httpx = "^0.25.2"
aiofiles = "^23.2.1"
python-dateutil = "^2.8.2"
sqlite-utils = "^3.35.2"
schedule = "^1.2.1"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-cov = "^4.1.0"
pytest-mock = "^3.12.0"
pytest-asyncio = "^0.21.1"
black = "^23.12.0"
isort = "^5.13.2"
flake8 = "^6.1.0"
mypy = "^1.7.1"
pre-commit = "^3.6.0"
bandit = "^1.7.5"
safety = "^2.3.5"
sphinx = "^7.2.6"
sphinx-rtd-theme = "^1.3.0"
mkdocs = "^1.5.3"
mkdocs-material = "^9.4.14"

[tool.black]
line-length = 100
target-version = ['py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=textup",
    "--cov-branch",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--cov-fail-under=80"
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "slow: Slow tests",
    "network: Tests that require network access"
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:"
]

[tool.bandit]
exclude_dirs = ["tests", "test_*"]
skips = ["B101", "B601"]

[tool.flake8]
max-line-length = 100
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    ".venv",
    "venv",
    "build",
    "dist",
    "*.egg-info"
]