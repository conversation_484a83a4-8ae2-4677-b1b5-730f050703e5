# 项目文件结构整理完成

## 📁 新的目录结构

### 文档目录 (docs/)
```
docs/
├── testing/
│   ├── README.md                    # 测试文档索引
│   ├── zhihu-cli-test-guide.md     # 知乎CLI测试指南
│   ├── readme-zhihu-test.md        # 知乎测试完整指南  
│   ├── zhihu-test-report.md        # 测试结果报告
│   └── readme-dev-testing.md       # 开发测试指南
```

### 测试目录 (tests/)
```
tests/
├── integration/zhihu/              # 知乎集成测试
│   └── test-zhihu-publish.sh      # 知乎发布测试脚本
├── data/                          # 测试数据
│   └── test-zhihu-article.md     # 测试文章
├── test-content/                  # 其他测试内容
└── test-results/                  # 测试结果
```

### 根目录脚本
```  
run-zhihu-test.sh                  # 快速测试入口脚本
```

## 🚀 使用方法

### 快速测试
```bash
# 预览模式测试
./run-zhihu-test.sh --preview-only --skip-install

# 完整测试
./run-zhihu-test.sh --skip-install
```

### 手动测试
```bash
# 激活环境
source .venv/bin/activate

# 预览测试
textup publish tests/data/test-zhihu-article.md --platform zhihu --dry-run

# 环境测试
./dev-scripts/quick-test.sh
```

## ✅ 整理效果

✅ 文档分类清晰 - docs/testing/ 目录
✅ 测试文件归位 - tests/ 目录分类
✅ 文件名小写化 - 统一命名规范
✅ 路径更新完成 - 脚本引用正确
✅ 查找更方便 - 目录结构合理

**项目结构整理完成！** 🎉
