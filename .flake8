[flake8]
max-line-length = 100
max-complexity = 10
select = E,W,F,C
ignore =
    E203,
    E501,
    W503,
    F401,
exclude =
    .git,
    __pycache__,
    .venv,
    venv,
    env,
    .env,
    build,
    dist,
    *.egg-info,
    .pytest_cache,
    .mypy_cache,
    .coverage,
    htmlcov

# Per-file ignores
per-file-ignores =
    __init__.py:F401
    test_*.py:S101,S311
    *_test.py:S101,S311

# Complexity settings
max-arguments = 6
max-attributes = 8
max-returns = 4
max-local-variables = 15
