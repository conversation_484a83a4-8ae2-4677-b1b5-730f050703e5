"""
TextUp 数据库模式定义

本模块定义了TextUp项目的数据库表结构，使用SQLAlchemy ORM。
支持SQLite作为默认数据库，也可配置为PostgreSQL。
"""

from sqlalchemy import (
    Column,
    String,
    Text,
    DateTime,
    Boolean,
    Integer,
    JSON,
    ForeignKey,
    create_engine,
    Index,
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker
from datetime import datetime
import uuid

Base = declarative_base()


def generate_uuid():
    """生成UUID字符串"""
    return str(uuid.uuid4())


class ContentTable(Base):
    """内容表"""

    __tablename__ = "contents"

    id = Column(String(36), primary_key=True, default=generate_uuid, comment="内容唯一标识")
    title = Column(String(500), nullable=False, comment="内容标题")
    content = Column(Text, nullable=False, comment="内容正文")
    content_format = Column(String(20), default="markdown", comment="内容格式")
    source_file_path = Column(String(1000), comment="源文件路径")
    tags = Column(JSON, comment="标签列表")
    metadata_json = Column(JSON, comment="元数据")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 关联关系
    publish_tasks = relationship("PublishTaskTable", back_populates="content")

    # 索引
    __table_args__ = (
        Index("idx_content_title", "title"),
        Index("idx_content_created_at", "created_at"),
        Index("idx_content_format", "content_format"),
    )


class PublishTaskTable(Base):
    """发布任务表"""

    __tablename__ = "publish_tasks"

    id = Column(String(36), primary_key=True, default=generate_uuid, comment="任务唯一标识")
    content_id = Column(String(36), ForeignKey("contents.id"), nullable=False, comment="内容ID")
    platforms = Column(JSON, nullable=False, comment="目标平台列表")
    publish_strategy = Column(String(20), default="immediate", comment="发布策略")
    scheduled_time = Column(DateTime, comment="定时发布时间")
    status = Column(String(20), default="pending", comment="任务状态")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 关联关系
    content = relationship("ContentTable", back_populates="publish_tasks")
    publish_records = relationship("PublishRecordTable", back_populates="task")

    # 索引
    __table_args__ = (
        Index("idx_task_content_id", "content_id"),
        Index("idx_task_status", "status"),
        Index("idx_task_created_at", "created_at"),
        Index("idx_task_scheduled_time", "scheduled_time"),
    )


class PublishRecordTable(Base):
    """发布记录表"""

    __tablename__ = "publish_records"

    id = Column(String(36), primary_key=True, default=generate_uuid, comment="记录唯一标识")
    task_id = Column(String(36), ForeignKey("publish_tasks.id"), nullable=False, comment="任务ID")
    platform = Column(String(50), nullable=False, comment="发布平台")
    platform_post_id = Column(String(200), comment="平台文章ID")
    status = Column(String(20), default="pending", comment="发布状态")
    error_message = Column(Text, comment="错误信息")
    published_at = Column(DateTime, comment="发布时间")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")

    # 关联关系
    task = relationship("PublishTaskTable", back_populates="publish_records")

    # 索引
    __table_args__ = (
        Index("idx_record_task_id", "task_id"),
        Index("idx_record_platform", "platform"),
        Index("idx_record_status", "status"),
        Index("idx_record_published_at", "published_at"),
    )


class PlatformConfigTable(Base):
    """平台配置表"""

    __tablename__ = "platform_configs"

    id = Column(String(36), primary_key=True, default=generate_uuid, comment="配置ID")
    platform = Column(String(50), nullable=False, comment="平台标识")
    user_id = Column(String(100), comment="用户ID")
    config = Column(JSON, nullable=False, comment="平台配置")
    credentials_encrypted = Column(Text, comment="加密的凭证")
    is_active = Column(Boolean, default=True, comment="是否启用")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 索引
    __table_args__ = (
        Index("idx_platform_config_platform", "platform"),
        Index("idx_platform_config_user_id", "user_id"),
        Index("idx_platform_config_active", "is_active"),
    )


class OperationLogTable(Base):
    """操作日志表"""

    __tablename__ = "operation_logs"

    id = Column(String(36), primary_key=True, default=generate_uuid, comment="日志ID")
    operation_type = Column(String(50), nullable=False, comment="操作类型")
    resource_type = Column(String(50), comment="资源类型")
    resource_id = Column(String(36), comment="资源ID")
    user_id = Column(String(100), comment="用户ID")
    details = Column(JSON, comment="操作详情")
    status = Column(String(20), comment="操作状态")
    error_message = Column(Text, comment="错误信息")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")

    # 索引
    __table_args__ = (
        Index("idx_log_operation_type", "operation_type"),
        Index("idx_log_resource_type", "resource_type"),
        Index("idx_log_user_id", "user_id"),
        Index("idx_log_created_at", "created_at"),
    )


class SettingTable(Base):
    """系统设置表"""

    __tablename__ = "settings"

    id = Column(String(36), primary_key=True, default=generate_uuid, comment="设置ID")
    key = Column(String(100), nullable=False, unique=True, comment="设置键")
    value = Column(Text, comment="设置值")
    description = Column(String(500), comment="设置描述")
    is_public = Column(Boolean, default=False, comment="是否公开")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 索引
    __table_args__ = (
        Index("idx_setting_key", "key"),
        Index("idx_setting_public", "is_public"),
    )


# =============================================================================
# 数据库工具函数
# =============================================================================


def create_database_engine(database_url: str = "sqlite:///textup.db", echo: bool = False):
    """创建数据库引擎"""
    engine = create_engine(database_url, echo=echo)
    return engine


def create_session_factory(engine):
    """创建会话工厂"""
    return sessionmaker(bind=engine)


def create_all_tables(engine):
    """创建所有表"""
    Base.metadata.create_all(engine)


def drop_all_tables(engine):
    """删除所有表"""
    Base.metadata.drop_all(engine)


# =============================================================================
# 数据库初始化脚本
# =============================================================================


def init_database(database_url: str = "sqlite:///textup.db", echo: bool = False):
    """
    初始化数据库

    Args:
        database_url: 数据库连接URL
        echo: 是否打印SQL语句

    Returns:
        engine: 数据库引擎
        session_factory: 会话工厂
    """
    # 创建引擎
    engine = create_database_engine(database_url, echo)

    # 创建所有表
    create_all_tables(engine)

    # 创建会话工厂
    session_factory = create_session_factory(engine)

    # 初始化默认设置
    session = session_factory()
    try:
        _init_default_settings(session)
        session.commit()
    except Exception as e:
        session.rollback()
        raise e
    finally:
        session.close()

    return engine, session_factory


def _init_default_settings(session):
    """初始化默认设置"""
    default_settings = [
        {"key": "app.version", "value": "1.0.0", "description": "应用版本号", "is_public": True},
        {"key": "app.name", "value": "TextUp", "description": "应用名称", "is_public": True},
        {
            "key": "publish.default_strategy",
            "value": "immediate",
            "description": "默认发布策略",
            "is_public": True,
        },
        {
            "key": "publish.max_concurrent_tasks",
            "value": "5",
            "description": "最大并发任务数",
            "is_public": False,
        },
        {
            "key": "publish.retry_attempts",
            "value": "3",
            "description": "重试次数",
            "is_public": False,
        },
    ]

    for setting_data in default_settings:
        # 检查设置是否已存在
        existing = session.query(SettingTable).filter_by(key=setting_data["key"]).first()
        if not existing:
            setting = SettingTable(**setting_data)
            session.add(setting)


if __name__ == "__main__":
    # 测试数据库初始化
    print("正在初始化数据库...")
    engine, session_factory = init_database(echo=True)
    print("数据库初始化完成！")

    # 测试创建会话
    session = session_factory()
    try:
        # 查询设置
        settings = session.query(SettingTable).all()
        print(f"默认设置数量: {len(settings)}")
        for setting in settings:
            print(f"  {setting.key}: {setting.value}")
    finally:
        session.close()
