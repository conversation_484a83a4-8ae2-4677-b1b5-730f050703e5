"""
TextUp 核心数据模型

本模块定义了TextUp项目的所有核心数据模型，使用Pydantic进行数据验证和序列化。
模型设计遵循product1.md文档中的数据结构规范。
"""

from datetime import datetime
from typing import List, Dict, Any, Optional, Union
from enum import Enum
from pydantic import BaseModel, Field, field_validator
import uuid


class ContentFormat(str, Enum):
    """内容格式枚举"""

    MARKDOWN = "markdown"
    HTML = "html"
    TEXT = "text"
    PDF = "pdf"
    DOCX = "docx"


class Platform(str, Enum):
    """支持的平台枚举"""

    ZHIHU = "zhihu"
    WEIBO = "weibo"
    XIAOHONGSHU = "xiaohongshu"
    TOUTIAO = "toutiao"


class TaskStatus(str, Enum):
    """任务状态枚举"""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class PublishStatus(str, Enum):
    """发布状态枚举"""

    PENDING = "pending"
    PUBLISHING = "publishing"
    PUBLISHED = "published"
    FAILED = "failed"


class PublishStrategy(str, Enum):
    """发布策略枚举"""

    IMMEDIATE = "immediate"
    SCHEDULED = "scheduled"
    POLLING = "polling"
    INTELLIGENT = "intelligent"


class ErrorSeverity(str, Enum):
    """错误严重级别"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorType(str, Enum):
    """错误类型枚举"""

    NETWORK = "network"
    AUTHENTICATION = "auth"
    RATE_LIMIT = "rate_limit"
    VALIDATION = "validation"
    PLATFORM = "platform"
    SYSTEM = "system"


# =============================================================================
# 内容相关模型
# =============================================================================


class ContentMetrics(BaseModel):
    """内容指标模型"""

    word_count: int = Field(default=0, description="字数统计")
    char_count: int = Field(default=0, description="字符数统计")
    paragraph_count: int = Field(default=0, description="段落数")
    image_count: int = Field(default=0, description="图片数量")
    link_count: int = Field(default=0, description="链接数量")
    estimated_read_time: int = Field(default=0, description="预估阅读时间（秒）")

    @field_validator("estimated_read_time")
    @classmethod
    def calculate_read_time(cls, v, info):
        """根据字数计算阅读时间"""
        if info.data and "word_count" in info.data:
            # 假设每分钟200字
            return max(1, info.data["word_count"] // 200 * 60)
        return v


class Content(BaseModel):
    """内容模型"""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="内容唯一标识")
    title: str = Field(description="内容标题")
    content: str = Field(description="内容正文")
    content_format: ContentFormat = Field(default=ContentFormat.MARKDOWN, description="内容格式")
    source_file_path: Optional[str] = Field(None, description="源文件路径")
    tags: List[str] = Field(default_factory=list, description="标签列表")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")

    @field_validator("title")
    @classmethod
    def validate_title(cls, v):
        if not v or not v.strip():
            raise ValueError("标题不能为空")
        if len(v) > 200:
            raise ValueError("标题长度不能超过200字符")
        return v.strip()

    @field_validator("content")
    @classmethod
    def validate_content(cls, v):
        if not v or not v.strip():
            raise ValueError("内容不能为空")
        return v.strip()

    @field_validator("tags")
    @classmethod
    def validate_tags(cls, v):
        if len(v) > 20:
            raise ValueError("标签数量不能超过20个")
        return [tag.strip() for tag in v if tag.strip()]

    def to_json(self) -> str:
        """Convert to JSON string"""
        return self.model_dump_json()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return self.model_dump()

    def get_word_count(self) -> int:
        """获取字数统计"""
        return len(self.content.split())

    def get_char_count(self) -> int:
        """获取字符数统计"""
        return len(self.content)

    def has_images(self) -> bool:
        """检查是否包含图片"""
        if self.content_format == ContentFormat.MARKDOWN:
            import re

            return bool(re.search(r"!\[.*?\]\(.*?\)", self.content))
        elif self.content_format == ContentFormat.HTML:
            return "<img" in self.content.lower()
        return False

    def extract_images(self) -> List[str]:
        """提取图片URL"""
        images = []
        if self.content_format == ContentFormat.MARKDOWN:
            import re

            matches = re.findall(r"!\[.*?\]\((.*?)\)", self.content)
            images.extend(matches)
        elif self.content_format == ContentFormat.HTML:
            import re

            matches = re.findall(r'<img[^>]+src=["\']([^"\'>]+)["\']', self.content, re.IGNORECASE)
            images.extend(matches)
        return images

    def get_summary(self, max_length: int = 200) -> str:
        """获取内容摘要"""
        # 简单的摘要提取，去除Markdown标记
        import re

        text = self.content
        if self.content_format == ContentFormat.MARKDOWN:
            # 移除Markdown标记
            text = re.sub(r"#+\s*", "", text)  # 标题
            text = re.sub(r"\*\*(.*?)\*\*", r"\1", text)  # 粗体
            text = re.sub(r"\*(.*?)\*", r"\1", text)  # 斜体
            text = re.sub(r"!\[.*?\]\(.*?\)", "", text)  # 图片
            text = re.sub(r"\[.*?\]\(.*?\)", "", text)  # 链接

        # 取前几句话作为摘要
        sentences = text.split(". ")
        summary = ""
        for sentence in sentences:
            if len(summary + sentence) > max_length:
                break
            summary += sentence + ". "

        return summary.strip().rstrip(".")


class TransformedContent(BaseModel):
    """转换后的内容模型"""

    title: str = Field(description="标题")
    content: str = Field(description="原始内容")
    content_format: ContentFormat = Field(default=ContentFormat.MARKDOWN, description="内容格式")
    html: str = Field(description="HTML格式内容")
    text: str = Field(description="纯文本内容")
    tags: List[str] = Field(default_factory=list, description="标签列表")
    images: Optional[List[str]] = Field(None, description="图片URL列表")
    links: Optional[List[str]] = Field(None, description="链接列表")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    metrics: Optional[ContentMetrics] = Field(None, description="内容指标")

    @field_validator("metrics")
    @classmethod
    def calculate_metrics(cls, v, info):
        """自动计算内容指标"""
        if v is None and info.data and "text" in info.data:
            text = info.data["text"]
            return ContentMetrics(
                word_count=len(text.split()),
                char_count=len(text),
                paragraph_count=len([p for p in text.split("\n\n") if p.strip()]),
                image_count=len(info.data.get("images", [])),
                link_count=len(info.data.get("links", [])),
            )
        return v


# =============================================================================
# 任务相关模型
# =============================================================================


class PublishTask(BaseModel):
    """发布任务模型"""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="任务唯一标识")
    content_id: str = Field(description="内容ID")
    platforms: List[Platform] = Field(description="目标平台列表")
    publish_strategy: PublishStrategy = Field(
        default=PublishStrategy.IMMEDIATE, description="发布策略"
    )
    scheduled_time: Optional[datetime] = Field(None, description="定时发布时间")
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="任务状态")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")

    @field_validator("platforms")
    @classmethod
    def validate_platforms(cls, v):
        if not v:
            raise ValueError("至少需要选择一个发布平台")
        return list(set(v))  # 去重


class PublishRecord(BaseModel):
    """发布记录模型"""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="记录唯一标识")
    task_id: str = Field(description="任务ID")
    platform: Platform = Field(description="发布平台")
    platform_post_id: Optional[str] = Field(None, description="平台文章ID")
    status: PublishStatus = Field(default=PublishStatus.PENDING, description="发布状态")
    error_message: Optional[str] = Field(None, description="错误信息")
    published_at: Optional[datetime] = Field(None, description="发布时间")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")


# =============================================================================
# 平台配置模型
# =============================================================================


class PlatformConfig(BaseModel):
    """平台配置模型"""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="配置ID")
    platform: Platform = Field(description="平台标识")
    user_id: Optional[str] = Field(None, description="用户ID")
    config: Dict[str, Any] = Field(default_factory=dict, description="平台配置")
    credentials_encrypted: Optional[str] = Field(None, description="加密的凭证")
    is_active: bool = Field(default=True, description="是否启用")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")


# =============================================================================
# 平台特定模型
# =============================================================================


class ZhihuCredentials(BaseModel):
    """知乎认证凭证"""

    access_token: str = Field(description="访问令牌")
    refresh_token: str = Field(description="刷新令牌")
    user_id: str = Field(description="用户ID")
    expires_at: Optional[datetime] = Field(None, description="过期时间")


class ZhihuPublishOptions(BaseModel):
    """知乎发布选项"""

    type: str = Field(default="article", description="发布类型: article 或 answer")
    column_id: Optional[str] = Field(None, description="专栏ID")
    question_id: Optional[str] = Field(None, description="问题ID")
    visibility: str = Field(default="public", description="可见性: public 或 private")


class WeiboCredentials(BaseModel):
    """微博认证凭证"""

    access_token: str = Field(description="访问令牌")
    uid: str = Field(description="用户ID")
    cookies: Optional[str] = Field(None, description="Cookie信息")
    expires_at: Optional[datetime] = Field(None, description="过期时间")


class WeiboPublishOptions(BaseModel):
    """微博发布选项"""

    visibility: int = Field(default=0, description="可见性: 0公开 1好友 2自己")
    location: Optional[str] = Field(None, description="位置信息")
    lat: Optional[float] = Field(None, description="纬度")
    long: Optional[float] = Field(None, description="经度")


class XhsCredentials(BaseModel):
    """小红书认证凭证"""

    cookies: str = Field(description="Cookie信息")
    user_id: str = Field(description="用户ID")


class XhsPublishOptions(BaseModel):
    """小红书发布选项"""

    content_type: str = Field(default="note", description="内容类型: note 或 video")
    images: Optional[List[str]] = Field(None, description="图片列表")
    location: Optional[str] = Field(None, description="位置信息")


class ToutiaoCredentials(BaseModel):
    """今日头条认证凭证"""

    app_id: str = Field(description="应用ID")
    secret: str = Field(description="应用秘钥")
    access_token: Optional[str] = Field(None, description="访问令牌")
    expires_at: Optional[datetime] = Field(None, description="过期时间")


class ToutiaoPublishOptions(BaseModel):
    """今日头条发布选项"""

    category: str = Field(description="文章分类")
    original_url: Optional[str] = Field(None, description="原文链接")
    draft_id: Optional[str] = Field(None, description="草稿ID")


# =============================================================================
# 结果模型
# =============================================================================


class PublishResult(BaseModel):
    """发布结果模型"""

    success: bool = Field(description="是否成功")
    platform: Platform = Field(description="发布平台")
    platform_post_id: Optional[str] = Field(None, description="平台文章ID")
    publish_url: Optional[str] = Field(None, description="发布链接")
    error_message: Optional[str] = Field(None, description="错误信息")
    error_details: Optional[Dict[str, Any]] = Field(None, description="详细错误信息")
    published_at: Optional[datetime] = Field(None, description="发布时间")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="其他元数据")

    @field_validator("error_message")
    @classmethod
    def validate_error_message(cls, v, info):
        if info.data and not info.data.get("success", True) and not v:
            raise ValueError("失败状态下必须提供错误信息")
        return v

    @field_validator("platform_post_id")
    @classmethod
    def validate_platform_post_id(cls, v, info):
        if info.data and info.data.get("success", False) and not v:
            raise ValueError("成功状态下必须提供平台文章ID")
        return v


class AuthResult(BaseModel):
    """认证结果模型"""

    success: bool = Field(description="是否成功")
    platform: Platform = Field(description="认证平台")
    user_id: Optional[str] = Field(None, description="用户ID")
    username: Optional[str] = Field(None, description="用户名")
    auth_data: Optional[Dict[str, Any]] = Field(None, description="认证数据")
    expires_at: Optional[datetime] = Field(None, description="认证过期时间")
    auth_url: Optional[str] = Field(None, description="认证URL")
    error_message: Optional[str] = Field(None, description="错误信息")
    error_details: Optional[Dict[str, Any]] = Field(None, description="详细错误信息")

    @field_validator("error_message")
    @classmethod
    def validate_error_message(cls, v, info):
        if info.data and not info.data.get("success", True) and not v:
            raise ValueError("认证失败时必须提供错误信息")
        return v


class ValidationError(BaseModel):
    """验证错误模型"""

    field: str = Field(description="错误字段")
    message: str = Field(description="错误信息")
    value: Optional[Any] = Field(None, description="错误值")
    error_code: Optional[str] = Field(None, description="错误代码")
    level: str = Field(default="error", description="错误级别: error 或 warning")

    @field_validator("level")
    @classmethod
    def validate_level(cls, v):
        """验证错误级别"""
        if v not in ["error", "warning", "info"]:
            raise ValueError("错误级别必须是 error, warning 或 info 之一")
        return v


class ValidationResult(BaseModel):
    """验证结果模型"""

    is_valid: bool = Field(description="是否有效")
    errors: List[ValidationError] = Field(default_factory=list, description="错误列表")


class FormatValidationResult(BaseModel):
    """格式验证结果模型"""

    is_valid: bool = Field(description="是否有效")
    errors: List[str] = Field(default_factory=list, description="错误信息列表")
    warnings: List[str] = Field(default_factory=list, description="警告信息列表")
    format_info: Optional[Dict[str, Any]] = Field(None, description="格式信息")


class OperationLog(BaseModel):
    """操作日志模型"""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="日志ID")
    operation_type: str = Field(description="操作类型")
    resource_type: str = Field(description="资源类型")
    resource_id: Optional[str] = Field(None, description="资源ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    details: Dict[str, Any] = Field(default_factory=dict, description="操作详情")
    status: str = Field(description="操作状态")
    error_message: Optional[str] = Field(None, description="错误信息")
    duration_ms: Optional[int] = Field(None, description="操作时长（毫秒）")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")


class TaskProgress(BaseModel):
    """任务进度模型"""

    task_id: str = Field(description="任务ID")
    total_steps: int = Field(description="总步骤数")
    completed_steps: int = Field(default=0, description="已完成步骤数")
    current_step: str = Field(description="当前步骤")
    progress_percentage: float = Field(default=0.0, description="进度百分比")
    estimated_remaining_time: Optional[int] = Field(None, description="预估剩余时间（秒）")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")

    @field_validator("progress_percentage")
    @classmethod
    def calculate_progress(cls, v, info):
        """计算进度百分比"""
        if info.data and "total_steps" in info.data and "completed_steps" in info.data:
            total = info.data["total_steps"]
            completed = info.data["completed_steps"]
            if total > 0:
                return min(100.0, (completed / total) * 100.0)
        return v


class PlatformLimits(BaseModel):
    """平台限制模型"""

    platform: Platform = Field(description="平台标识")
    max_title_length: int = Field(description="最大标题长度")
    max_content_length: int = Field(description="最大内容长度")
    max_tags: int = Field(description="最大标签数")
    max_images: int = Field(description="最大图片数")
    supported_formats: List[ContentFormat] = Field(description="支持的格式")
    rate_limit_per_hour: int = Field(description="每小时发布限制")
    rate_limit_per_day: int = Field(description="每日发布限制")
    additional_constraints: Dict[str, Any] = Field(default_factory=dict, description="额外约束")


# =============================================================================
# 配置模型
# =============================================================================


class DatabaseConfig(BaseModel):
    """数据库配置"""

    type: str = Field(default="sqlite", description="数据库类型")
    url: str = Field(default="sqlite:///textup.db", description="数据库连接URL")
    echo: bool = Field(default=False, description="是否打印SQL")


class LoggingConfig(BaseModel):
    """日志配置"""

    level: str = Field(default="INFO", description="日志级别")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s", description="日志格式"
    )
    file_path: Optional[str] = Field(default=None, description="日志文件路径")


class AppConfig(BaseModel):
    """应用配置"""

    app_name: str = Field(default="TextUp", description="应用名称")
    version: str = Field(default="1.0.0", description="版本号")
    debug: bool = Field(default=False, description="调试模式")
    database: DatabaseConfig = Field(
        default_factory=lambda: DatabaseConfig(), description="数据库配置"
    )
    logging: LoggingConfig = Field(default_factory=lambda: LoggingConfig(), description="日志配置")
