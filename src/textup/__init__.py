"""
TextUp - 多平台文本内容发布工具

一个企业级的多平台文本内容自动化发布工具，支持知乎、微博、小红书、今日头条等主流平台。

主要功能：
- 多平台内容发布
- 智能调度系统
- 错误处理与重试
- 配置管理
- CLI工具

技术栈：Python 3.9+, Pydantic, SQLAlchemy, Click, Playwright
"""

__version__ = "1.0.0"
__author__ = "TextUp Team"
__email__ = "<EMAIL>"
__description__ = "多平台文本内容发布工具"

# 导入核心模型
from .models import (
    # 枚举类型
    ContentFormat,
    Platform,
    TaskStatus,
    PublishStatus,
    PublishStrategy,
    ErrorSeverity,
    ErrorType,
    # 核心模型
    Content,
    TransformedContent,
    PublishTask,
    PublishRecord,
    PlatformConfig,
    # 平台特定模型
    ZhihuCredentials,
    ZhihuPublishOptions,
    WeiboCredentials,
    WeiboPublishOptions,
    XhsCredentials,
    XhsPublishOptions,
    ToutiaoCredentials,
    ToutiaoPublishOptions,
    # 结果模型
    PublishResult,
    AuthResult,
    ValidationError,
    ValidationResult,
    FormatValidationResult,
    # 配置模型
    DatabaseConfig,
    LoggingConfig,
    AppConfig,
)

# 导入数据库相关
from .models.database import (
    Base,
    ContentTable,
    PublishTaskTable,
    PublishRecordTable,
    PlatformConfigTable,
    OperationLogTable,
    SettingTable,
    init_database,
    create_database_engine,
    create_session_factory,
)

__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    "__email__",
    "__description__",
    # 枚举类型
    "ContentFormat",
    "Platform",
    "TaskStatus",
    "PublishStatus",
    "PublishStrategy",
    "ErrorSeverity",
    "ErrorType",
    # 核心模型
    "Content",
    "TransformedContent",
    "PublishTask",
    "PublishRecord",
    "PlatformConfig",
    # 平台特定模型
    "ZhihuCredentials",
    "ZhihuPublishOptions",
    "WeiboCredentials",
    "WeiboPublishOptions",
    "XhsCredentials",
    "XhsPublishOptions",
    "ToutiaoCredentials",
    "ToutiaoPublishOptions",
    # 结果模型
    "PublishResult",
    "AuthResult",
    "ValidationError",
    "ValidationResult",
    "FormatValidationResult",
    # 配置模型
    "DatabaseConfig",
    "LoggingConfig",
    "AppConfig",
    # 数据库
    "Base",
    "ContentTable",
    "PublishTaskTable",
    "PublishRecordTable",
    "PlatformConfigTable",
    "OperationLogTable",
    "SettingTable",
    "init_database",
    "create_database_engine",
    "create_session_factory",
]
