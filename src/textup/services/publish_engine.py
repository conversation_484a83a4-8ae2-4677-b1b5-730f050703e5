"""
TextUp 发布引擎

本模块实现了TextUp项目的核心发布引擎，负责：
1. 管理发布任务的生命周期
2. 协调多平台并发发布
3. 处理任务队列和状态跟踪
4. 实现智能调度和重试机制
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable, AsyncGenerator
from dataclasses import dataclass
from enum import Enum
import uuid
import json
import logging

from ..models import (
    Content,
    TransformedContent,
    PublishTask,
    PublishRecord,
    Platform,
    TaskStatus,
    PublishStatus,
    PublishStrategy,
    PublishResult,
    ValidationResult,
    ErrorType,
    ErrorSeverity,
)
from ..adapters.base import BaseAdapter
from ..adapters.zhihu import ZhihuAdapter
from ..adapters.weibo import WeiboAdapter
from ..services.content_manager import ContentManager
from ..utils import (
    TextUpError,
    PlatformAPIError,
    RateLimitError,
    InvalidCredentialsError,
    handle_exception,
)

# 配置日志
logger = logging.getLogger(__name__)


class TaskPriority(Enum):
    """任务优先级"""

    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class QueuedTask:
    """队列中的任务"""

    task: PublishTask
    priority: TaskPriority = TaskPriority.NORMAL
    retry_count: int = 0
    created_at: datetime = None
    scheduled_for: Optional[datetime] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


class PublishEngine:
    """发布引擎核心类"""

    def __init__(
        self,
        content_manager: ContentManager,
        max_concurrent_tasks: int = 5,
        max_retries: int = 3,
        retry_delay: float = 2.0,
        queue_check_interval: float = 1.0,
    ):
        """
        初始化发布引擎

        Args:
            content_manager: 内容管理器
            max_concurrent_tasks: 最大并发任务数
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
            queue_check_interval: 队列检查间隔（秒）
        """
        self.content_manager = content_manager
        self.max_concurrent_tasks = max_concurrent_tasks
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.queue_check_interval = queue_check_interval

        # 任务队列
        self._task_queue: asyncio.PriorityQueue = asyncio.PriorityQueue()
        self._running_tasks: Dict[str, asyncio.Task] = {}
        self._task_results: Dict[str, List[PublishResult]] = {}

        # 平台适配器
        self._adapters: Dict[Platform, BaseAdapter] = {}
        self._adapter_configs: Dict[Platform, Dict[str, Any]] = {}

        # 状态跟踪
        self._is_running = False
        self._worker_task: Optional[asyncio.Task] = None
        self._stats = {
            "tasks_processed": 0,
            "tasks_successful": 0,
            "tasks_failed": 0,
            "total_publish_attempts": 0,
            "successful_publishes": 0,
            "failed_publishes": 0,
        }

        # 事件回调
        self._event_callbacks: Dict[str, List[Callable]] = {
            "task_started": [],
            "task_completed": [],
            "task_failed": [],
            "publish_success": [],
            "publish_failed": [],
            "engine_started": [],
            "engine_stopped": [],
        }

        logger.info(f"发布引擎初始化完成，最大并发: {max_concurrent_tasks}")

    def register_adapter(
        self, platform: Platform, adapter: BaseAdapter, config: Dict[str, Any] = None
    ):
        """
        注册平台适配器

        Args:
            platform: 平台标识
            adapter: 适配器实例
            config: 适配器配置
        """
        self._adapters[platform] = adapter
        self._adapter_configs[platform] = config or {}
        logger.info(f"平台适配器已注册: {platform.value}")

    def get_adapter(self, platform: Platform) -> Optional[BaseAdapter]:
        """
        获取平台适配器

        Args:
            platform: 平台标识

        Returns:
            适配器实例
        """
        return self._adapters.get(platform)

    def add_event_callback(self, event: str, callback: Callable):
        """
        添加事件回调

        Args:
            event: 事件名称
            callback: 回调函数
        """
        if event in self._event_callbacks:
            self._event_callbacks[event].append(callback)
        else:
            logger.warning(f"未知事件类型: {event}")

    async def _emit_event(self, event: str, *args, **kwargs):
        """触发事件"""
        for callback in self._event_callbacks.get(event, []):
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(*args, **kwargs)
                else:
                    callback(*args, **kwargs)
            except Exception as e:
                logger.error(f"事件回调执行失败 {event}: {str(e)}")

    async def submit_task(
        self, task: PublishTask, priority: TaskPriority = TaskPriority.NORMAL
    ) -> str:
        """
        提交发布任务

        Args:
            task: 发布任务
            priority: 任务优先级

        Returns:
            任务ID
        """
        queued_task = QueuedTask(task=task, priority=priority, scheduled_for=task.scheduled_time)

        # 根据优先级排序，优先级高的数字小，所以用负数
        priority_score = (
            -priority.value,  # 优先级（负数，高优先级排前面）
            time.time(),  # 时间戳（早提交的排前面）
        )

        await self._task_queue.put((priority_score, queued_task))
        logger.info(f"任务已提交: {task.id}, 优先级: {priority.name}")

        return task.id

    async def start(self):
        """启动发布引擎"""
        if self._is_running:
            logger.warning("发布引擎已经在运行")
            return

        self._is_running = True
        self._worker_task = asyncio.create_task(self._worker_loop())
        await self._emit_event("engine_started")
        logger.info("发布引擎已启动")

    async def stop(self):
        """停止发布引擎"""
        if not self._is_running:
            return

        self._is_running = False

        # 等待所有运行中的任务完成
        if self._running_tasks:
            logger.info(f"等待 {len(self._running_tasks)} 个任务完成...")
            await asyncio.gather(*self._running_tasks.values(), return_exceptions=True)

        # 停止工作循环
        if self._worker_task:
            self._worker_task.cancel()
            try:
                await self._worker_task
            except asyncio.CancelledError:
                pass

        await self._emit_event("engine_stopped")
        logger.info("发布引擎已停止")

    async def _worker_loop(self):
        """工作循环，处理任务队列"""
        logger.info("发布引擎工作循环已启动")

        while self._is_running:
            try:
                # 检查是否有空闲槽位
                if len(self._running_tasks) >= self.max_concurrent_tasks:
                    # 清理已完成的任务
                    await self._cleanup_completed_tasks()

                    if len(self._running_tasks) >= self.max_concurrent_tasks:
                        # 仍然满负荷，等待一会儿再检查
                        await asyncio.sleep(self.queue_check_interval)
                        continue

                # 尝试从队列获取任务
                try:
                    priority_score, queued_task = await asyncio.wait_for(
                        self._task_queue.get(), timeout=self.queue_check_interval
                    )
                except asyncio.TimeoutError:
                    continue

                # 检查是否需要延迟执行
                if queued_task.scheduled_for and datetime.now() < queued_task.scheduled_for:
                    # 重新放回队列，等待时间到达
                    await self._task_queue.put((priority_score, queued_task))
                    continue

                # 启动任务处理
                task_coroutine = self._process_task(queued_task)
                task_future = asyncio.create_task(task_coroutine)
                self._running_tasks[queued_task.task.id] = task_future

                logger.info(f"开始处理任务: {queued_task.task.id}")

            except Exception as e:
                logger.error(f"工作循环异常: {str(e)}")
                await asyncio.sleep(1)  # 防止快速失败循环

    async def _cleanup_completed_tasks(self):
        """清理已完成的任务"""
        completed_task_ids = []
        for task_id, task_future in self._running_tasks.items():
            if task_future.done():
                completed_task_ids.append(task_id)

        for task_id in completed_task_ids:
            del self._running_tasks[task_id]

    async def _process_task(self, queued_task: QueuedTask):
        """
        处理单个发布任务

        Args:
            queued_task: 队列中的任务
        """
        task = queued_task.task
        task_id = task.id

        try:
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            await self._emit_event("task_started", task)

            # 获取内容
            content = await self.content_manager.get_content_by_id(task.content_id)
            if not content:
                raise TextUpError(f"未找到内容: {task.content_id}")

            # 转换内容
            transformed_content = await self.content_manager.transform_content(content)

            # 并发发布到各个平台
            publish_tasks = []
            for platform in task.platforms:
                adapter = self._adapters.get(platform)
                if not adapter:
                    logger.warning(f"平台适配器未注册: {platform.value}")
                    continue

                publish_coroutine = self._publish_to_platform(
                    transformed_content, platform, adapter, task_id
                )
                publish_tasks.append(publish_coroutine)

            # 等待所有发布完成
            results = await asyncio.gather(*publish_tasks, return_exceptions=True)

            # 处理结果
            publish_results = []
            success_count = 0

            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"发布异常: {str(result)}")
                    continue

                if isinstance(result, PublishResult):
                    publish_results.append(result)
                    if result.success:
                        success_count += 1
                        await self._emit_event("publish_success", result)
                    else:
                        await self._emit_event("publish_failed", result)

            # 保存结果
            self._task_results[task_id] = publish_results

            # 更新统计
            self._stats["tasks_processed"] += 1
            self._stats["total_publish_attempts"] += len(publish_results)
            self._stats["successful_publishes"] += success_count
            self._stats["failed_publishes"] += len(publish_results) - success_count

            # 判断任务整体状态
            if success_count == len(publish_results) and success_count > 0:
                task.status = TaskStatus.COMPLETED
                self._stats["tasks_successful"] += 1
                await self._emit_event("task_completed", task, publish_results)
                logger.info(f"任务完成: {task_id}, 成功发布到 {success_count} 个平台")
            elif success_count > 0:
                task.status = TaskStatus.COMPLETED
                self._stats["tasks_successful"] += 1
                await self._emit_event("task_completed", task, publish_results)
                logger.warning(
                    f"任务部分完成: {task_id}, 成功发布到 {success_count}/{len(publish_results)} 个平台"
                )
            else:
                # 检查是否需要重试
                if queued_task.retry_count < self.max_retries:
                    queued_task.retry_count += 1
                    queued_task.scheduled_for = datetime.now() + timedelta(
                        seconds=self.retry_delay * (2**queued_task.retry_count)
                    )

                    # 重新加入队列
                    priority_score = (-queued_task.priority.value, time.time())
                    await self._task_queue.put((priority_score, queued_task))

                    logger.info(
                        f"任务重试 ({queued_task.retry_count}/{self.max_retries}): {task_id}"
                    )
                    return
                else:
                    task.status = TaskStatus.FAILED
                    self._stats["tasks_failed"] += 1
                    await self._emit_event("task_failed", task, publish_results)
                    logger.error(f"任务失败: {task_id}, 已达到最大重试次数")

        except Exception as e:
            task.status = TaskStatus.FAILED
            self._stats["tasks_failed"] += 1
            error_msg = f"任务处理异常: {str(e)}"
            logger.error(error_msg)
            await self._emit_event("task_failed", task, [])
            handle_exception("process_task", e, {"task_id": task_id})

    async def _publish_to_platform(
        self, content: TransformedContent, platform: Platform, adapter: BaseAdapter, task_id: str
    ) -> PublishResult:
        """
        发布内容到指定平台

        Args:
            content: 转换后的内容
            platform: 目标平台
            adapter: 平台适配器
            task_id: 任务ID

        Returns:
            发布结果
        """
        try:
            # 获取平台配置
            platform_config = self._adapter_configs.get(platform, {})
            publish_options = platform_config.get("publish_options", {})

            # 验证内容格式
            validation_result = await adapter.validate_format(content)
            if not validation_result.is_valid:
                error_messages = [error.message for error in validation_result.errors]
                return PublishResult(
                    success=False,
                    platform=platform,
                    platform_post_id=None,
                    publish_url=None,
                    error_message=f"内容验证失败: {'; '.join(error_messages)}",
                    error_details={"validation_errors": validation_result.errors},
                    published_at=None,
                )

            # 执行发布
            result = await adapter.publish(content, publish_options)

            logger.info(f"平台发布结果 {platform.value}: {'成功' if result.success else '失败'}")
            return result

        except RateLimitError as e:
            logger.warning(f"平台 {platform.value} 触发频率限制: {str(e)}")
            return PublishResult(
                success=False,
                platform=platform,
                platform_post_id=None,
                publish_url=None,
                error_message=f"触发频率限制: {str(e)}",
                error_details={"error_type": "rate_limit", "retry_after": e.retry_after},
                published_at=None,
            )

        except InvalidCredentialsError as e:
            logger.error(f"平台 {platform.value} 认证失败: {str(e)}")
            return PublishResult(
                success=False,
                platform=platform,
                platform_post_id=None,
                publish_url=None,
                error_message=f"认证失败: {str(e)}",
                error_details={"error_type": "authentication"},
                published_at=None,
            )

        except PlatformAPIError as e:
            logger.error(f"平台 {platform.value} API错误: {str(e)}")
            return PublishResult(
                success=False,
                platform=platform,
                platform_post_id=None,
                publish_url=None,
                error_message=f"平台API错误: {str(e)}",
                error_details={"error_type": "platform_api", "api_error": e.api_error},
                published_at=None,
            )

        except Exception as e:
            logger.error(f"平台 {platform.value} 发布异常: {str(e)}")
            return PublishResult(
                success=False,
                platform=platform,
                platform_post_id=None,
                publish_url=None,
                error_message=f"发布异常: {str(e)}",
                error_details={"error_type": "unknown", "exception": type(e).__name__},
                published_at=None,
            )

    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            任务状态信息
        """
        # 检查运行中的任务
        if task_id in self._running_tasks:
            task_future = self._running_tasks[task_id]
            return {
                "status": "running",
                "is_done": task_future.done(),
                "results": self._task_results.get(task_id, []),
            }

        # 检查已完成的任务结果
        if task_id in self._task_results:
            return {"status": "completed", "is_done": True, "results": self._task_results[task_id]}

        return None

    async def get_task_results(self, task_id: str) -> List[PublishResult]:
        """
        获取任务发布结果

        Args:
            task_id: 任务ID

        Returns:
            发布结果列表
        """
        return self._task_results.get(task_id, [])

    def get_stats(self) -> Dict[str, Any]:
        """
        获取引擎统计信息

        Returns:
            统计信息
        """
        return {
            **self._stats,
            "is_running": self._is_running,
            "running_tasks_count": len(self._running_tasks),
            "queue_size": self._task_queue.qsize(),
            "registered_platforms": list(self._adapters.keys()),
        }

    async def cancel_task(self, task_id: str) -> bool:
        """
        取消任务

        Args:
            task_id: 任务ID

        Returns:
            是否成功取消
        """
        if task_id in self._running_tasks:
            task_future = self._running_tasks[task_id]
            if not task_future.done():
                task_future.cancel()
                del self._running_tasks[task_id]
                logger.info(f"任务已取消: {task_id}")
                return True

        return False

    async def pause_engine(self):
        """暂停发布引擎"""
        self._is_running = False
        logger.info("发布引擎已暂停")

    async def resume_engine(self):
        """恢复发布引擎"""
        if not self._is_running:
            await self.start()
            logger.info("发布引擎已恢复")

    async def clear_queue(self):
        """清空任务队列"""
        # 创建新的队列替换旧的
        old_queue = self._task_queue
        self._task_queue = asyncio.PriorityQueue()

        # 记录清理的任务数量
        queue_size = old_queue.qsize()
        logger.info(f"任务队列已清空，清理了 {queue_size} 个待处理任务")

        return queue_size

    async def get_queue_info(self) -> Dict[str, Any]:
        """
        获取队列信息

        Returns:
            队列信息
        """
        return {
            "queue_size": self._task_queue.qsize(),
            "running_tasks": len(self._running_tasks),
            "max_concurrent": self.max_concurrent_tasks,
            "available_slots": max(0, self.max_concurrent_tasks - len(self._running_tasks)),
        }

    async def health_check(self) -> Dict[str, Any]:
        """
        健康检查

        Returns:
            健康状态信息
        """
        adapter_status = {}
        for platform, adapter in self._adapters.items():
            try:
                # 简单的适配器健康检查
                adapter_status[platform.value] = {
                    "status": "healthy",
                    "type": type(adapter).__name__,
                }
            except Exception as e:
                adapter_status[platform.value] = {"status": "error", "error": str(e)}

        return {
            "engine_status": "running" if self._is_running else "stopped",
            "queue_health": {
                "size": self._task_queue.qsize(),
                "running_tasks": len(self._running_tasks),
                "is_responsive": True,  # 简化的响应性检查
            },
            "adapters": adapter_status,
            "stats": self.get_stats(),
            "timestamp": datetime.now().isoformat(),
        }


class PublishEngineManager:
    """发布引擎管理器，单例模式"""

    _instance: Optional[PublishEngine] = None
    _lock = asyncio.Lock()

    @classmethod
    async def get_instance(cls, content_manager: ContentManager = None, **kwargs) -> PublishEngine:
        """获取发布引擎单例"""
        async with cls._lock:
            if cls._instance is None:
                if content_manager is None:
                    raise ValueError("首次创建发布引擎实例时必须提供 content_manager")
                cls._instance = PublishEngine(content_manager, **kwargs)
            return cls._instance

    @classmethod
    async def reset_instance(cls):
        """重置实例（主要用于测试）"""
        async with cls._lock:
            if cls._instance:
                await cls._instance.stop()
                cls._instance = None
