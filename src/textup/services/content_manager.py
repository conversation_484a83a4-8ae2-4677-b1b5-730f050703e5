"""
TextUp 内容管理器

本模块实现了内容管理器，负责处理文件读取、Markdown解析、内容验证和格式转换等功能。
支持多种内容格式，并提供智能的内容处理能力。
"""

import os
import re
import asyncio
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
import aiofiles
import markdown
import frontmatter
from datetime import datetime

from ..models import (
    Content,
    TransformedContent,
    ContentFormat,
    ValidationResult,
    ValidationError,
    ContentMetrics,
)
from ..utils import (
    ContentManagerProtocol,
    ContentError,
    ContentValidationError,
    ContentFormatError,
    FileNotFoundError,
    handle_exception,
)


class ContentManager(ContentManagerProtocol):
    """内容管理器实现类"""

    def __init__(
        self,
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        supported_extensions: Optional[List[str]] = None,
    ):
        """
        初始化内容管理器

        Args:
            max_file_size: 最大文件大小（字节）
            supported_extensions: 支持的文件扩展名列表
        """
        self.max_file_size = max_file_size
        self.supported_extensions = supported_extensions or [
            ".md",
            ".markdown",
            ".txt",
            ".html",
            ".htm",
        ]

        # 配置Markdown解析器
        self.markdown_parser = markdown.Markdown(
            extensions=[
                "meta",  # 元数据支持
                "tables",  # 表格支持
                "fenced_code",  # 代码块支持
                "codehilite",  # 代码高亮
                "toc",  # 目录生成
                "nl2br",  # 换行转换
                "attr_list",  # 属性列表
            ],
            extension_configs={
                "codehilite": {
                    "css_class": "highlight",
                    "use_pygments": False,
                },
                "toc": {
                    "anchorlink": True,
                    "permalink": True,
                },
            },
        )

    async def parse_content(self, file_path: str) -> Content:
        """
        解析文件内容

        Args:
            file_path: 文件路径

        Returns:
            Content: 解析后的内容对象

        Raises:
            FileNotFoundError: 文件不存在
            ContentError: 内容解析失败
        """
        try:
            # 验证文件路径
            path = Path(file_path)
            if not path.exists():
                raise FileNotFoundError(file_path)

            if not path.is_file():
                raise ContentError(f"路径不是文件: {file_path}")

            # 检查文件大小
            file_size = path.stat().st_size
            if file_size > self.max_file_size:
                raise ContentError(
                    f"文件大小超出限制: {file_size} > {self.max_file_size}",
                    context={"file_path": file_path, "file_size": file_size},
                )

            # 检查文件扩展名
            if path.suffix.lower() not in self.supported_extensions:
                raise ContentError(
                    f"不支持的文件格式: {path.suffix}",
                    context={"file_path": file_path, "extension": path.suffix},
                )

            # 读取文件内容
            content_text = await self._read_file(file_path)

            # 根据文件扩展名确定内容格式
            content_format = self._detect_content_format(path.suffix)

            # 解析内容
            if content_format == ContentFormat.MARKDOWN:
                return await self._parse_markdown_content(content_text, file_path)
            elif content_format == ContentFormat.HTML:
                return await self._parse_html_content(content_text, file_path)
            else:
                return await self._parse_text_content(content_text, file_path)

        except Exception as e:
            if isinstance(e, (FileNotFoundError, ContentError)):
                raise
            else:
                handle_exception("parse_content", e, {"file_path": file_path})

    async def _read_file(self, file_path: str) -> str:
        """异步读取文件内容"""
        try:
            async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
                return await f.read()
        except UnicodeDecodeError:
            # 尝试其他编码
            for encoding in ["gbk", "big5", "latin1"]:
                try:
                    async with aiofiles.open(file_path, "r", encoding=encoding) as f:
                        return await f.read()
                except UnicodeDecodeError:
                    continue
            raise ContentError(f"无法解码文件: {file_path}")

    def _detect_content_format(self, extension: str) -> ContentFormat:
        """根据文件扩展名检测内容格式"""
        extension = extension.lower()
        if extension in [".md", ".markdown"]:
            return ContentFormat.MARKDOWN
        elif extension in [".html", ".htm"]:
            return ContentFormat.HTML
        else:
            return ContentFormat.TEXT

    async def _parse_markdown_content(self, content_text: str, file_path: str) -> Content:
        """解析Markdown内容"""
        try:
            # 使用frontmatter解析元数据
            post = frontmatter.loads(content_text)

            # 提取元数据
            metadata = dict(post.metadata)
            title = metadata.get("title", self._extract_title_from_content(post.content))
            tags = metadata.get("tags", [])
            if isinstance(tags, str):
                tags = [tag.strip() for tag in tags.split(",")]

            # 创建Content对象
            content = Content(
                title=title,
                content=post.content,
                content_format=ContentFormat.MARKDOWN,
                source_file_path=file_path,
                tags=tags,
                metadata=metadata,
            )

            return content

        except Exception as e:
            raise ContentError(f"Markdown解析失败: {str(e)}", cause=e)

    async def _parse_html_content(self, content_text: str, file_path: str) -> Content:
        """解析HTML内容"""
        try:
            # 简单的HTML解析
            title = self._extract_html_title(content_text)
            tags = self._extract_html_meta_tags(content_text)

            content = Content(
                title=title,
                content=content_text,
                content_format=ContentFormat.HTML,
                source_file_path=file_path,
                tags=tags,
                metadata={},
            )

            return content

        except Exception as e:
            raise ContentError(f"HTML解析失败: {str(e)}", cause=e)

    async def _parse_text_content(self, content_text: str, file_path: str) -> Content:
        """解析纯文本内容"""
        try:
            # 从第一行提取标题
            lines = content_text.split("\n")
            title = lines[0].strip() if lines else Path(file_path).stem

            content = Content(
                title=title,
                content=content_text,
                content_format=ContentFormat.TEXT,
                source_file_path=file_path,
                tags=[],
                metadata={},
            )

            return content

        except Exception as e:
            raise ContentError(f"文本解析失败: {str(e)}", cause=e)

    def _extract_title_from_content(self, content: str) -> str:
        """从内容中提取标题"""
        # 查找第一个一级标题
        lines = content.split("\n")
        for line in lines:
            line = line.strip()
            if line.startswith("# "):
                return line[2:].strip()

        # 如果没有找到标题，返回第一行非空内容
        for line in lines:
            line = line.strip()
            if line:
                return line[:50] + ("..." if len(line) > 50 else "")

        return "无标题"

    def _extract_html_title(self, html_content: str) -> str:
        """从HTML中提取标题"""
        # 查找<title>标签
        title_match = re.search(
            r"<title[^>]*>(.*?)</title>", html_content, re.IGNORECASE | re.DOTALL
        )
        if title_match:
            return title_match.group(1).strip()

        # 查找第一个h1标签
        h1_match = re.search(r"<h1[^>]*>(.*?)</h1>", html_content, re.IGNORECASE | re.DOTALL)
        if h1_match:
            return re.sub(r"<[^>]+>", "", h1_match.group(1)).strip()

        return "无标题"

    def _extract_html_meta_tags(self, html_content: str) -> List[str]:
        """从HTML中提取标签"""
        tags = []

        # 查找keywords meta标签
        keywords_match = re.search(
            r'<meta[^>]*name=["\']keywords["\'][^>]*content=["\']([^"\']*)["\']',
            html_content,
            re.IGNORECASE,
        )
        if keywords_match:
            keywords = keywords_match.group(1)
            tags.extend([tag.strip() for tag in keywords.split(",") if tag.strip()])

        return tags

    async def validate_content(self, content: Content) -> ValidationResult:
        """
        验证内容

        Args:
            content: 要验证的内容

        Returns:
            ValidationResult: 验证结果
        """
        errors = []

        try:
            # 基本验证
            if not content.title or not content.title.strip():
                errors.append(
                    ValidationError(field="title", message="标题不能为空", value=content.title)
                )
            elif len(content.title) > 200:
                errors.append(
                    ValidationError(
                        field="title", message="标题长度不能超过200字符", value=len(content.title)
                    )
                )

            if not content.content or not content.content.strip():
                errors.append(
                    ValidationError(field="content", message="内容不能为空", value=content.content)
                )

            # 标签验证
            if len(content.tags) > 20:
                errors.append(
                    ValidationError(
                        field="tags", message="标签数量不能超过20个", value=len(content.tags)
                    )
                )

            # 内容格式特定验证
            if content.content_format == ContentFormat.MARKDOWN:
                await self._validate_markdown_content(content, errors)
            elif content.content_format == ContentFormat.HTML:
                await self._validate_html_content(content, errors)

            return ValidationResult(is_valid=len(errors) == 0, errors=errors)

        except Exception as e:
            errors.append(
                ValidationError(
                    field="validation",
                    message=f"验证过程中发生错误: {str(e)}",
                    error_code="VALIDATION_ERROR",
                )
            )
            return ValidationResult(is_valid=False, errors=errors)

    async def _validate_markdown_content(self, content: Content, errors: List[ValidationError]):
        """验证Markdown内容"""
        # 检查Markdown语法
        try:
            self.markdown_parser.convert(content.content)
        except Exception as e:
            errors.append(
                ValidationError(
                    field="content",
                    message=f"Markdown语法错误: {str(e)}",
                    error_code="MARKDOWN_SYNTAX_ERROR",
                )
            )

        # 检查图片链接
        image_pattern = r"!\[.*?\]\((.*?)\)"
        images = re.findall(image_pattern, content.content)
        for i, img_url in enumerate(images):
            if not img_url.strip():
                errors.append(
                    ValidationError(
                        field="content",
                        message=f"第{i+1}个图片链接为空",
                        error_code="EMPTY_IMAGE_URL",
                    )
                )

    async def _validate_html_content(self, content: Content, errors: List[ValidationError]):
        """验证HTML内容"""
        # 基本HTML标签检查
        html_content = content.content

        # 检查未闭合的标签（简单检查）
        open_tags = re.findall(r"<(\w+)[^>]*>", html_content)
        close_tags = re.findall(r"</(\w+)>", html_content)

        # 计算标签平衡
        tag_count = {}
        for tag in open_tags:
            tag_count[tag] = tag_count.get(tag, 0) + 1
        for tag in close_tags:
            tag_count[tag] = tag_count.get(tag, 0) - 1

        for tag, count in tag_count.items():
            if count != 0:
                errors.append(
                    ValidationError(
                        field="content",
                        message=f"HTML标签不平衡: <{tag}>",
                        error_code="HTML_TAG_UNBALANCED",
                    )
                )

    async def transform_content(self, content: Content, target_format: str) -> TransformedContent:
        """
        转换内容格式

        Args:
            content: 源内容
            target_format: 目标格式 (html, text, markdown)

        Returns:
            TransformedContent: 转换后的内容
        """
        try:
            html_content = ""
            text_content = ""

            # 根据源格式转换
            if content.content_format == ContentFormat.MARKDOWN:
                html_content = self.markdown_parser.convert(content.content)
                text_content = self._html_to_text(html_content)
            elif content.content_format == ContentFormat.HTML:
                html_content = content.content
                text_content = self._html_to_text(html_content)
            else:  # TEXT
                text_content = content.content
                html_content = self._text_to_html(content.content)

            # 提取图片和链接
            images = self._extract_images(content.content, content.content_format)
            links = self._extract_links(content.content, content.content_format)

            # 创建转换后的内容
            transformed = TransformedContent(
                title=content.title,
                content=content.content,
                content_format=content.content_format,
                html=html_content,
                text=text_content,
                tags=content.tags,
                images=images,
                links=links,
                metadata=content.metadata,
            )

            return transformed

        except Exception as e:
            raise ContentFormatError(
                source_format=content.content_format.value, target_format=target_format, cause=e
            )

    def _html_to_text(self, html: str) -> str:
        """将HTML转换为纯文本"""
        # 简单的HTML到文本转换
        text = re.sub(r"<[^>]+>", "", html)  # 移除HTML标签
        text = re.sub(r"&nbsp;", " ", text)  # 转换实体
        text = re.sub(r"&lt;", "<", text)
        text = re.sub(r"&gt;", ">", text)
        text = re.sub(r"&amp;", "&", text)
        text = re.sub(r"\s+", " ", text)  # 规范化空白字符
        return text.strip()

    def _text_to_html(self, text: str) -> str:
        """将纯文本转换为HTML"""
        # 简单的文本到HTML转换
        html = text.replace("&", "&amp;")
        html = html.replace("<", "&lt;")
        html = html.replace(">", "&gt;")
        html = html.replace("\n\n", "</p><p>")
        html = html.replace("\n", "<br>")
        return f"<p>{html}</p>"

    def _extract_images(self, content: str, content_format: ContentFormat) -> List[str]:
        """提取图片URL"""
        images = []

        if content_format == ContentFormat.MARKDOWN:
            # Markdown图片语法: ![alt](url)
            pattern = r"!\[.*?\]\((.*?)\)"
            images.extend(re.findall(pattern, content))
        elif content_format == ContentFormat.HTML:
            # HTML img标签
            pattern = r'<img[^>]+src=["\']([^"\']+)["\']'
            images.extend(re.findall(pattern, content, re.IGNORECASE))

        return [img.strip() for img in images if img.strip()]

    def _extract_links(self, content: str, content_format: ContentFormat) -> List[str]:
        """提取链接URL"""
        links = []

        if content_format == ContentFormat.MARKDOWN:
            # Markdown链接语法: [text](url)
            pattern = r"\[.*?\]\((.*?)\)"
            links.extend(re.findall(pattern, content))
        elif content_format == ContentFormat.HTML:
            # HTML a标签
            pattern = r'<a[^>]+href=["\']([^"\']+)["\']'
            links.extend(re.findall(pattern, content, re.IGNORECASE))

        return [link.strip() for link in links if link.strip()]

    async def batch_parse_content(self, file_paths: List[str]) -> List[Content]:
        """
        批量解析内容

        Args:
            file_paths: 文件路径列表

        Returns:
            List[Content]: 解析后的内容列表
        """
        tasks = [self.parse_content(path) for path in file_paths]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        contents = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # 记录错误但继续处理其他文件
                print(f"解析文件失败 {file_paths[i]}: {result}")
            else:
                contents.append(result)

        return contents

    async def save_content(self, content: Content, output_path: str) -> bool:
        """
        保存内容到文件

        Args:
            content: 要保存的内容
            output_path: 输出文件路径

        Returns:
            bool: 是否成功保存
        """
        try:
            # 根据输出格式决定内容
            output_format = self._detect_content_format(Path(output_path).suffix)

            if output_format == ContentFormat.MARKDOWN:
                output_content = self._content_to_markdown(content)
            elif output_format == ContentFormat.HTML:
                transformed = await self.transform_content(content, "html")
                output_content = transformed.html
            else:
                output_content = content.content

            # 确保输出目录存在
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)

            # 写入文件
            async with aiofiles.open(output_path, "w", encoding="utf-8") as f:
                await f.write(output_content)

            return True

        except Exception as e:
            handle_exception(
                "save_content", e, {"content_id": content.id, "output_path": output_path}
            )
            return False

    def _content_to_markdown(self, content: Content) -> str:
        """将Content转换为Markdown格式"""
        output_lines = []

        # 添加frontmatter
        if content.metadata or content.tags:
            output_lines.append("---")
            output_lines.append(f"title: {content.title}")
            if content.tags:
                output_lines.append(f"tags: {', '.join(content.tags)}")
            for key, value in content.metadata.items():
                if key not in ["title", "tags"]:
                    output_lines.append(f"{key}: {value}")
            output_lines.append("---")
            output_lines.append("")

        # 添加内容
        if content.content_format != ContentFormat.MARKDOWN:
            # 如果不是Markdown，添加标题
            output_lines.append(f"# {content.title}")
            output_lines.append("")

        output_lines.append(content.content)

        return "\n".join(output_lines)
