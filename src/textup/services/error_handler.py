"""
TextUp 错误处理与重试机制

本模块实现了TextUp项目的错误处理和重试机制，包括：
1. 分层异常处理实现
2. 智能重试策略
3. 错误日志和报告
4. 熔断器模式实现
5. 错误统计和分析
"""

import asyncio
import logging
import time
import traceback
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable, Type, Union
from dataclasses import dataclass, field
from enum import Enum
import random
import hashlib

from ..models import Platform, ErrorType, ErrorSeverity, PublishResult
from ..utils import (
    TextUpError,
    PlatformAPIError,
    RateLimitError,
    InvalidCredentialsError,
    TimeoutError,
    ValidationError,
)

# 配置日志
logger = logging.getLogger(__name__)


class RetryStrategy(Enum):
    """重试策略"""

    EXPONENTIAL_BACKOFF = "exponential_backoff"  # 指数退避
    LINEAR_BACKOFF = "linear_backoff"  # 线性退避
    FIXED_DELAY = "fixed_delay"  # 固定延迟
    IMMEDIATE = "immediate"  # 立即重试
    CUSTOM = "custom"  # 自定义策略


class CircuitBreakerState(Enum):
    """熔断器状态"""

    CLOSED = "closed"  # 关闭状态，正常工作
    OPEN = "open"  # 开启状态，拒绝请求
    HALF_OPEN = "half_open"  # 半开状态，尝试恢复


@dataclass
class RetryConfig:
    """重试配置"""

    max_attempts: int = 3
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    retry_on_exceptions: List[Type[Exception]] = field(
        default_factory=lambda: [PlatformAPIError, TimeoutError, ConnectionError]
    )
    no_retry_exceptions: List[Type[Exception]] = field(
        default_factory=lambda: [InvalidCredentialsError, ValidationError]
    )


class RetryPolicy:
    """重试策略"""

    def __init__(self, max_attempts: int = 3, base_delay: float = 1.0):
        self.max_attempts = max_attempts
        self.base_delay = base_delay


@dataclass
class CircuitBreakerConfig:
    """熔断器配置"""

    failure_threshold: int = 5  # 失败阈值
    recovery_timeout: int = 60  # 恢复超时时间（秒）
    expected_exception: Type[Exception] = Exception
    success_threshold: int = 3  # 半开状态成功阈值


@dataclass
class ErrorContext:
    """错误上下文"""

    operation: str
    platform: Optional[Platform] = None
    content_id: Optional[str] = None
    task_id: Optional[str] = None
    user_id: Optional[str] = None
    request_id: Optional[str] = None
    additional_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ErrorRecord:
    """错误记录"""

    id: str
    timestamp: datetime
    error_type: ErrorType
    severity: ErrorSeverity
    platform: Optional[Platform]
    operation: str
    exception_type: str
    error_message: str
    stack_trace: str
    context: ErrorContext
    retry_count: int = 0
    resolved: bool = False
    resolution_time: Optional[datetime] = None


class CircuitBreaker:
    """熔断器实现"""

    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time: Optional[datetime] = None
        self._lock = asyncio.Lock()

    async def call(self, func: Callable, *args, **kwargs):
        """通过熔断器调用函数"""
        async with self._lock:
            if self.state == CircuitBreakerState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitBreakerState.HALF_OPEN
                    self.success_count = 0
                    logger.info("熔断器进入半开状态")
                else:
                    raise Exception(f"熔断器开启，拒绝请求")

        try:
            result = (
                await func(*args, **kwargs)
                if asyncio.iscoroutinefunction(func)
                else func(*args, **kwargs)
            )

            async with self._lock:
                if self.state == CircuitBreakerState.HALF_OPEN:
                    self.success_count += 1
                    if self.success_count >= self.config.success_threshold:
                        self.state = CircuitBreakerState.CLOSED
                        self.failure_count = 0
                        logger.info("熔断器恢复到关闭状态")
                elif self.state == CircuitBreakerState.CLOSED:
                    self.failure_count = max(0, self.failure_count - 1)

            return result

        except Exception as e:
            async with self._lock:
                if isinstance(e, self.config.expected_exception):
                    self.failure_count += 1
                    self.last_failure_time = datetime.now()

                    if (
                        self.state == CircuitBreakerState.CLOSED
                        and self.failure_count >= self.config.failure_threshold
                    ):
                        self.state = CircuitBreakerState.OPEN
                        logger.warning("熔断器开启")
                    elif self.state == CircuitBreakerState.HALF_OPEN:
                        self.state = CircuitBreakerState.OPEN
                        logger.warning("熔断器重新开启")

            raise

    def _should_attempt_reset(self) -> bool:
        """检查是否应该尝试重置"""
        if self.last_failure_time is None:
            return True

        elapsed = (datetime.now() - self.last_failure_time).total_seconds()
        return elapsed >= self.config.recovery_timeout


class RetryExecutor:
    """重试执行器"""

    def __init__(self, config: RetryConfig):
        self.config = config

    async def execute(self, func: Callable, *args, **kwargs):
        """执行函数并在失败时重试"""
        last_exception = None

        for attempt in range(self.config.max_attempts):
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)

            except Exception as e:
                last_exception = e

                # 检查是否应该重试
                if not self._should_retry(e):
                    logger.info(f"异常不可重试: {type(e).__name__}")
                    raise e

                # 如果是最后一次尝试，直接抛出异常
                if attempt == self.config.max_attempts - 1:
                    break

                # 计算延迟时间
                delay = self._calculate_delay(attempt)
                logger.info(
                    f"重试 {attempt + 1}/{self.config.max_attempts}，延迟 {delay:.2f}秒: {str(e)}"
                )

                await asyncio.sleep(delay)

        # 重试次数用尽，抛出最后一个异常
        if last_exception:
            raise last_exception

    def _should_retry(self, exception: Exception) -> bool:
        """判断是否应该重试"""
        # 检查不可重试的异常
        for no_retry_type in self.config.no_retry_exceptions:
            if isinstance(exception, no_retry_type):
                return False

        # 检查可重试的异常
        for retry_type in self.config.retry_on_exceptions:
            if isinstance(exception, retry_type):
                return True

        # 默认不重试
        return False

    def _calculate_delay(self, attempt: int) -> float:
        """计算延迟时间"""
        if self.config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = self.config.base_delay * (self.config.exponential_base**attempt)
        elif self.config.strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = self.config.base_delay * (attempt + 1)
        elif self.config.strategy == RetryStrategy.FIXED_DELAY:
            delay = self.config.base_delay
        elif self.config.strategy == RetryStrategy.IMMEDIATE:
            delay = 0
        else:  # CUSTOM or fallback
            delay = self.config.base_delay

        # 限制最大延迟
        delay = min(delay, self.config.max_delay)

        # 添加抖动
        if self.config.jitter and delay > 0:
            jitter_range = delay * 0.1  # 10% 抖动
            delay += random.uniform(-jitter_range, jitter_range)

        return max(0, delay)


class ErrorCollector:
    """错误收集器"""

    def __init__(self, max_records: int = 1000):
        self.max_records = max_records
        self.records: List[ErrorRecord] = []
        self.error_counts: Dict[str, int] = {}
        self.platform_errors: Dict[Platform, int] = {}
        self._lock = asyncio.Lock()

    async def collect_error(
        self,
        exception: Exception,
        context: ErrorContext,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    ) -> str:
        """收集错误信息"""
        error_id = self._generate_error_id(exception, context)

        # 确定错误类型
        error_type = self._classify_error(exception)

        # 创建错误记录
        record = ErrorRecord(
            id=error_id,
            timestamp=datetime.now(),
            error_type=error_type,
            severity=severity,
            platform=context.platform,
            operation=context.operation,
            exception_type=type(exception).__name__,
            error_message=str(exception),
            stack_trace=traceback.format_exc(),
            context=context,
        )

        async with self._lock:
            # 添加记录
            self.records.append(record)

            # 限制记录数量
            if len(self.records) > self.max_records:
                self.records = self.records[-self.max_records :]

            # 更新统计
            exception_key = type(exception).__name__
            self.error_counts[exception_key] = self.error_counts.get(exception_key, 0) + 1

            if context.platform:
                self.platform_errors[context.platform] = (
                    self.platform_errors.get(context.platform, 0) + 1
                )

        logger.error(f"错误已收集: {error_id} - {str(exception)}")
        return error_id

    def _generate_error_id(self, exception: Exception, context: ErrorContext) -> str:
        """生成错误ID"""
        content = f"{type(exception).__name__}:{context.operation}:{context.platform}:{time.time()}"
        return hashlib.md5(content.encode()).hexdigest()[:12]

    def _classify_error(self, exception: Exception) -> ErrorType:
        """分类错误类型"""
        if isinstance(exception, (ConnectionError, TimeoutError)):
            return ErrorType.NETWORK
        elif isinstance(exception, InvalidCredentialsError):
            return ErrorType.AUTHENTICATION
        elif isinstance(exception, RateLimitError):
            return ErrorType.RATE_LIMIT
        elif isinstance(exception, ValidationError):
            return ErrorType.VALIDATION
        elif isinstance(exception, PlatformAPIError):
            return ErrorType.PLATFORM
        else:
            return ErrorType.SYSTEM

    async def get_error_summary(self) -> Dict[str, Any]:
        """获取错误摘要"""
        async with self._lock:
            total_errors = len(self.records)

            if total_errors == 0:
                return {
                    "total_errors": 0,
                    "error_rate": 0.0,
                    "top_errors": [],
                    "platform_breakdown": {},
                    "recent_errors": [],
                }

            # 最近24小时的错误
            cutoff_time = datetime.now() - timedelta(hours=24)
            recent_errors = [r for r in self.records if r.timestamp > cutoff_time]

            # 错误率（最近1小时）
            hour_cutoff = datetime.now() - timedelta(hours=1)
            hour_errors = [r for r in self.records if r.timestamp > hour_cutoff]

            return {
                "total_errors": total_errors,
                "recent_24h": len(recent_errors),
                "recent_1h": len(hour_errors),
                "error_counts": dict(self.error_counts),
                "platform_breakdown": {k.value: v for k, v in self.platform_errors.items()},
                "top_errors": self._get_top_errors(5),
                "recent_errors": [self._record_to_dict(r) for r in recent_errors[-10:]],
            }

    def _get_top_errors(self, limit: int) -> List[Dict[str, Any]]:
        """获取最常见的错误"""
        sorted_errors = sorted(self.error_counts.items(), key=lambda x: x[1], reverse=True)
        return [{"type": err_type, "count": count} for err_type, count in sorted_errors[:limit]]

    def _record_to_dict(self, record: ErrorRecord) -> Dict[str, Any]:
        """将错误记录转换为字典"""
        return {
            "id": record.id,
            "timestamp": record.timestamp.isoformat(),
            "error_type": record.error_type.value,
            "severity": record.severity.value,
            "platform": record.platform.value if record.platform else None,
            "operation": record.operation,
            "exception_type": record.exception_type,
            "error_message": record.error_message,
            "retry_count": record.retry_count,
            "resolved": record.resolved,
        }


class ErrorHandler:
    """错误处理器主类"""

    def __init__(self):
        self.error_collector = ErrorCollector()
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.retry_configs: Dict[str, RetryConfig] = {}
        self.default_retry_config = RetryConfig()
        self.error_callbacks: List[Callable] = []

        # 默认重试配置
        self._setup_default_configs()

    def _setup_default_configs(self):
        """设置默认配置"""
        # 平台API重试配置
        self.retry_configs["platform_api"] = RetryConfig(
            max_attempts=3,
            strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
            base_delay=2.0,
            max_delay=30.0,
            retry_on_exceptions=[PlatformAPIError, TimeoutError, ConnectionError],
        )

        # 网络请求重试配置
        self.retry_configs["network"] = RetryConfig(
            max_attempts=5,
            strategy=RetryStrategy.EXPONENTIAL_BACKOFF,
            base_delay=1.0,
            max_delay=16.0,
            retry_on_exceptions=[ConnectionError, TimeoutError],
        )

        # 频率限制重试配置
        self.retry_configs["rate_limit"] = RetryConfig(
            max_attempts=2,
            strategy=RetryStrategy.FIXED_DELAY,
            base_delay=60.0,  # 1分钟后重试
            retry_on_exceptions=[RateLimitError],
        )

    def add_error_callback(self, callback: Callable[[ErrorRecord], None]):
        """添加错误回调"""
        self.error_callbacks.append(callback)

    def get_circuit_breaker(
        self, key: str, config: Optional[CircuitBreakerConfig] = None
    ) -> CircuitBreaker:
        """获取或创建熔断器"""
        if key not in self.circuit_breakers:
            if config is None:
                config = CircuitBreakerConfig()
            self.circuit_breakers[key] = CircuitBreaker(config)
        return self.circuit_breakers[key]

    def get_retry_executor(self, config_key: str = None) -> RetryExecutor:
        """获取重试执行器"""
        config = self.retry_configs.get(config_key, self.default_retry_config)
        return RetryExecutor(config)

    async def handle_error(
        self,
        exception: Exception,
        context: ErrorContext,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    ) -> str:
        """处理错误"""
        # 收集错误
        error_id = await self.error_collector.collect_error(exception, context, severity)

        # 触发回调
        if self.error_callbacks:
            record = ErrorRecord(
                id=error_id,
                timestamp=datetime.now(),
                error_type=self.error_collector._classify_error(exception),
                severity=severity,
                platform=context.platform,
                operation=context.operation,
                exception_type=type(exception).__name__,
                error_message=str(exception),
                stack_trace=traceback.format_exc(),
                context=context,
            )

            for callback in self.error_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(record)
                    else:
                        callback(record)
                except Exception as e:
                    logger.error(f"错误回调执行失败: {str(e)}")

        return error_id

    async def execute_with_retry(
        self,
        func: Callable,
        config_key: str = None,
        context: Optional[ErrorContext] = None,
        *args,
        **kwargs,
    ):
        """使用重试机制执行函数"""
        executor = self.get_retry_executor(config_key)

        try:
            return await executor.execute(func, *args, **kwargs)
        except Exception as e:
            if context:
                await self.handle_error(e, context)
            raise

    async def execute_with_circuit_breaker(
        self,
        func: Callable,
        breaker_key: str,
        breaker_config: Optional[CircuitBreakerConfig] = None,
        context: Optional[ErrorContext] = None,
        *args,
        **kwargs,
    ):
        """使用熔断器执行函数"""
        breaker = self.get_circuit_breaker(breaker_key, breaker_config)

        try:
            return await breaker.call(func, *args, **kwargs)
        except Exception as e:
            if context:
                await self.handle_error(e, context)
            raise

    async def execute_with_full_protection(
        self,
        func: Callable,
        breaker_key: str,
        retry_config_key: str = None,
        breaker_config: Optional[CircuitBreakerConfig] = None,
        context: Optional[ErrorContext] = None,
        *args,
        **kwargs,
    ):
        """使用完整保护机制（重试+熔断器）执行函数"""

        async def protected_func(*args, **kwargs):
            return await self.execute_with_circuit_breaker(
                func, breaker_key, breaker_config, context, *args, **kwargs
            )

        return await self.execute_with_retry(
            protected_func, retry_config_key, context, *args, **kwargs
        )

    async def get_health_status(self) -> Dict[str, Any]:
        """获取错误处理器健康状态"""
        error_summary = await self.error_collector.get_error_summary()

        # 熔断器状态
        breaker_status = {}
        for key, breaker in self.circuit_breakers.items():
            breaker_status[key] = {
                "state": breaker.state.value,
                "failure_count": breaker.failure_count,
                "success_count": breaker.success_count,
                "last_failure_time": (
                    breaker.last_failure_time.isoformat() if breaker.last_failure_time else None
                ),
            }

        return {
            "error_summary": error_summary,
            "circuit_breakers": breaker_status,
            "retry_configs": list(self.retry_configs.keys()),
            "callbacks_registered": len(self.error_callbacks),
            "timestamp": datetime.now().isoformat(),
        }


# 全局错误处理器实例
_global_error_handler: Optional[ErrorHandler] = None


def get_error_handler() -> ErrorHandler:
    """获取全局错误处理器实例"""
    global _global_error_handler
    if _global_error_handler is None:
        _global_error_handler = ErrorHandler()
    return _global_error_handler


async def handle_error(
    exception: Exception, operation: str, platform: Platform = None, **context_data
) -> str:
    """便捷的错误处理函数"""
    handler = get_error_handler()
    context = ErrorContext(operation=operation, platform=platform, additional_data=context_data)
    return await handler.handle_error(exception, context)


def with_retry(config_key: str = None):
    """重试装饰器"""

    def decorator(func):
        async def wrapper(*args, **kwargs):
            handler = get_error_handler()
            context = ErrorContext(operation=func.__name__)
            return await handler.execute_with_retry(func, config_key, context, *args, **kwargs)

        return wrapper

    return decorator


def with_circuit_breaker(breaker_key: str, config: Optional[CircuitBreakerConfig] = None):
    """熔断器装饰器"""

    def decorator(func):
        async def wrapper(*args, **kwargs):
            handler = get_error_handler()
            context = ErrorContext(operation=func.__name__)
            return await handler.execute_with_circuit_breaker(
                func, breaker_key, config, context, *args, **kwargs
            )

        return wrapper

    return decorator
