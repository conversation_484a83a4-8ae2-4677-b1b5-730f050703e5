"""
TextUp 配置管理器

本模块实现了配置管理器，负责处理YAML配置文件的读取、验证、存储和更新。
支持多层配置结构、环境变量替换、配置验证和默认值处理。
"""

import os
import yaml
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
import asyncio
import aiofiles
from datetime import datetime

from ..models import (
    AppConfig,
    DatabaseConfig,
    LoggingConfig,
    PlatformConfig,
    Platform,
    ValidationResult,
    ValidationError,
)
from ..utils import (
    ConfigManagerProtocol,
    ConfigurationError,
    ConfigNotFoundError,
    ConfigValidationError,
    handle_exception,
)


class ConfigManager(ConfigManagerProtocol):
    """配置管理器实现类"""

    def __init__(
        self,
        config_dir: str = "config",
        default_config_file: str = "config.yaml",
        platform_config_file: str = "platforms.yaml",
    ):
        """
        初始化配置管理器

        Args:
            config_dir: 配置文件目录
            default_config_file: 默认配置文件名
            platform_config_file: 平台配置文件名
        """
        self.config_dir = Path(config_dir)
        self.default_config_file = self.config_dir / default_config_file
        self.platform_config_file = self.config_dir / platform_config_file

        # 确保配置目录存在
        self.config_dir.mkdir(parents=True, exist_ok=True)

        # 缓存配置
        self._app_config: Optional[AppConfig] = None
        self._platform_configs: Dict[Platform, PlatformConfig] = {}
        self._config_cache: Dict[str, Any] = {}
        self._last_loaded: Optional[datetime] = None

    async def load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """
        加载配置

        Args:
            config_path: 配置文件路径（可选）

        Returns:
            Dict[str, Any]: 配置字典
        """
        try:
            if config_path:
                config_file = Path(config_path)
            else:
                config_file = self.default_config_file

            if not config_file.exists():
                # 创建默认配置文件
                await self._create_default_config(config_file)

            # 读取配置文件
            config_data = await self._read_yaml_file(config_file)

            # 环境变量替换
            config_data = self._substitute_env_vars(config_data)

            # 验证配置
            validation = await self._validate_config(config_data)
            if not validation.is_valid:
                raise ConfigValidationError(
                    config_key="root",
                    validation_error="; ".join([e.message for e in validation.errors]),
                )

            # 缓存配置
            self._config_cache = config_data
            self._last_loaded = datetime.now()

            return config_data

        except Exception as e:
            if isinstance(e, (ConfigNotFoundError, ConfigValidationError)):
                raise
            else:
                handle_exception("load_config", e, {"config_path": config_path})

    async def save_config(self, config: Dict[str, Any], config_path: Optional[str] = None) -> bool:
        """
        保存配置

        Args:
            config: 配置字典
            config_path: 配置文件路径（可选）

        Returns:
            bool: 是否成功保存
        """
        try:
            if config_path:
                config_file = Path(config_path)
            else:
                config_file = self.default_config_file

            # 验证配置
            validation = await self._validate_config(config)
            if not validation.is_valid:
                raise ConfigValidationError(
                    config_key="root",
                    validation_error="; ".join([e.message for e in validation.errors]),
                )

            # 确保目录存在
            config_file.parent.mkdir(parents=True, exist_ok=True)

            # 写入配置文件
            await self._write_yaml_file(config_file, config)

            # 更新缓存
            self._config_cache = config
            self._last_loaded = datetime.now()

            return True

        except Exception as e:
            handle_exception("save_config", e, {"config_path": config_path})
            return False

    async def get_app_config(self) -> AppConfig:
        """获取应用配置"""
        if self._app_config is None or self._should_reload():
            config_data = await self.load_config()
            self._app_config = AppConfig(**config_data.get("app", {}))

        return self._app_config

    async def update_app_config(self, **kwargs) -> bool:
        """更新应用配置"""
        try:
            config_data = await self.load_config()
            app_config = config_data.get("app", {})
            app_config.update(kwargs)
            config_data["app"] = app_config

            return await self.save_config(config_data)
        except Exception as e:
            handle_exception("update_app_config", e, kwargs)
            return False

    async def get_platform_config(self, platform: Platform) -> Optional[PlatformConfig]:
        """
        获取平台配置

        Args:
            platform: 平台标识

        Returns:
            Optional[PlatformConfig]: 平台配置或None
        """
        try:
            if platform not in self._platform_configs or self._should_reload():
                await self._load_platform_configs()

            return self._platform_configs.get(platform)

        except Exception as e:
            handle_exception("get_platform_config", e, {"platform": platform.value})
            return None

    async def set_platform_config(self, platform_config: PlatformConfig) -> bool:
        """
        设置平台配置

        Args:
            platform_config: 平台配置

        Returns:
            bool: 是否成功设置
        """
        try:
            # 加载现有配置
            await self._load_platform_configs()

            # 更新配置
            self._platform_configs[platform_config.platform] = platform_config

            # 保存到文件
            return await self._save_platform_configs()

        except Exception as e:
            handle_exception("set_platform_config", e, {"platform": platform_config.platform.value})
            return False

    async def remove_platform_config(self, platform: Platform) -> bool:
        """移除平台配置"""
        try:
            await self._load_platform_configs()

            if platform in self._platform_configs:
                del self._platform_configs[platform]
                return await self._save_platform_configs()

            return True

        except Exception as e:
            handle_exception("remove_platform_config", e, {"platform": platform.value})
            return False

    async def get_all_platform_configs(self) -> Dict[Platform, PlatformConfig]:
        """获取所有平台配置"""
        await self._load_platform_configs()
        return self._platform_configs.copy()

    def _should_reload(self) -> bool:
        """检查是否需要重新加载配置"""
        if self._last_loaded is None:
            return True

        # 检查文件修改时间
        try:
            if self.default_config_file.exists():
                file_mtime = datetime.fromtimestamp(self.default_config_file.stat().st_mtime)
                return file_mtime > self._last_loaded
        except OSError:
            pass

        return False

    async def _read_yaml_file(self, file_path: Path) -> Dict[str, Any]:
        """读取YAML文件"""
        try:
            async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
                content = await f.read()
                return yaml.safe_load(content) or {}
        except yaml.YAMLError as e:
            raise ConfigValidationError(
                config_key=str(file_path), validation_error=f"YAML解析错误: {str(e)}"
            )
        except Exception as e:
            raise ConfigNotFoundError(str(file_path)) from e

    async def _write_yaml_file(self, file_path: Path, data: Dict[str, Any]):
        """写入YAML文件"""
        try:
            yaml_content = yaml.dump(
                data, default_flow_style=False, allow_unicode=True, sort_keys=True, indent=2
            )

            async with aiofiles.open(file_path, "w", encoding="utf-8") as f:
                await f.write(yaml_content)
        except Exception as e:
            raise ConfigurationError(f"写入配置文件失败: {str(e)}") from e

    def _substitute_env_vars(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """环境变量替换"""

        def substitute_value(value):
            if isinstance(value, str) and value.startswith("${") and value.endswith("}"):
                env_var = value[2:-1]
                # 支持默认值语法: ${VAR:default}
                if ":" in env_var:
                    var_name, default_value = env_var.split(":", 1)
                    return os.getenv(var_name, default_value)
                else:
                    return os.getenv(env_var, value)
            elif isinstance(value, dict):
                return {k: substitute_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [substitute_value(v) for v in value]
            else:
                return value

        return substitute_value(config)

    async def _validate_config(self, config: Dict[str, Any]) -> ValidationResult:
        """验证配置"""
        errors = []

        try:
            # 验证应用配置
            app_config = config.get("app", {})
            try:
                AppConfig(**app_config)
            except Exception as e:
                errors.append(
                    ValidationError(
                        field="app", message=f"应用配置验证失败: {str(e)}", value=app_config
                    )
                )

            # 验证数据库配置
            db_config = app_config.get("database", {})
            if db_config:
                try:
                    DatabaseConfig(**db_config)
                except Exception as e:
                    errors.append(
                        ValidationError(
                            field="app.database",
                            message=f"数据库配置验证失败: {str(e)}",
                            value=db_config,
                        )
                    )

            # 验证日志配置
            log_config = app_config.get("logging", {})
            if log_config:
                try:
                    LoggingConfig(**log_config)
                except Exception as e:
                    errors.append(
                        ValidationError(
                            field="app.logging",
                            message=f"日志配置验证失败: {str(e)}",
                            value=log_config,
                        )
                    )

            return ValidationResult(is_valid=len(errors) == 0, errors=errors)

        except Exception as e:
            errors.append(
                ValidationError(
                    field="validation",
                    message=f"配置验证过程中发生错误: {str(e)}",
                    error_code="CONFIG_VALIDATION_ERROR",
                )
            )
            return ValidationResult(is_valid=False, errors=errors)

    async def _create_default_config(self, config_file: Path):
        """创建默认配置文件"""
        default_config = {
            "app": {
                "app_name": "TextUp",
                "version": "1.0.0",
                "debug": False,
                "database": {"type": "sqlite", "url": "sqlite:///textup.db", "echo": False},
                "logging": {
                    "level": "INFO",
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                    "file_path": None,
                },
            },
            "content": {
                "max_file_size": 10485760,  # 10MB
                "supported_extensions": [".md", ".markdown", ".txt", ".html", ".htm"],
                "default_format": "markdown",
            },
            "publishing": {
                "concurrent_tasks": 3,
                "retry_attempts": 3,
                "retry_delay": 1.0,
                "timeout": 30,
            },
        }

        await self._write_yaml_file(config_file, default_config)

    async def _load_platform_configs(self):
        """加载平台配置"""
        try:
            if not self.platform_config_file.exists():
                # 创建空的平台配置文件
                await self._write_yaml_file(self.platform_config_file, {"platforms": {}})
                return

            config_data = await self._read_yaml_file(self.platform_config_file)
            platforms_data = config_data.get("platforms", {})

            self._platform_configs = {}
            for platform_name, platform_data in platforms_data.items():
                try:
                    platform = Platform(platform_name)
                    platform_config = PlatformConfig(platform=platform, **platform_data)
                    self._platform_configs[platform] = platform_config
                except (ValueError, TypeError) as e:
                    # 忽略无效的平台配置
                    print(f"忽略无效的平台配置 {platform_name}: {e}")

        except Exception as e:
            # 如果加载失败，使用空配置
            self._platform_configs = {}
            handle_exception("load_platform_configs", e, reraise=False)

    async def _save_platform_configs(self) -> bool:
        """保存平台配置"""
        try:
            platforms_data = {}
            for platform, config in self._platform_configs.items():
                platforms_data[platform.value] = config.model_dump(exclude={"id"})

            config_data = {"platforms": platforms_data}
            await self._write_yaml_file(self.platform_config_file, config_data)
            return True

        except Exception as e:
            handle_exception("save_platform_configs", e, reraise=False)
            return False

    async def get_config_value(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值（支持点分隔路径）

        Args:
            key_path: 配置键路径，如 "app.database.url"
            default: 默认值

        Returns:
            配置值或默认值
        """
        try:
            config_data = await self.load_config()

            # 按点分隔路径查找值
            keys = key_path.split(".")
            value = config_data

            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default

            return value

        except Exception as e:
            handle_exception("get_config_value", e, {"key_path": key_path})
            return default

    async def set_config_value(self, key_path: str, value: Any) -> bool:
        """
        设置配置值（支持点分隔路径）

        Args:
            key_path: 配置键路径，如 "app.database.url"
            value: 要设置的值

        Returns:
            bool: 是否成功设置
        """
        try:
            config_data = await self.load_config()

            # 按点分隔路径设置值
            keys = key_path.split(".")
            current = config_data

            # 导航到目标位置
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]

            # 设置最终值
            current[keys[-1]] = value

            return await self.save_config(config_data)

        except Exception as e:
            handle_exception("set_config_value", e, {"key_path": key_path, "value": value})
            return False

    def get_config_file_paths(self) -> Dict[str, str]:
        """获取配置文件路径"""
        return {
            "app_config": str(self.default_config_file),
            "platform_config": str(self.platform_config_file),
            "config_dir": str(self.config_dir),
        }

    async def backup_config(self, backup_dir: Optional[str] = None) -> bool:
        """备份配置文件"""
        try:
            if backup_dir:
                backup_path = Path(backup_dir)
            else:
                backup_path = self.config_dir / "backups"

            backup_path.mkdir(parents=True, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 备份主配置文件
            if self.default_config_file.exists():
                backup_file = backup_path / f"config_{timestamp}.yaml"
                content = await self._read_yaml_file(self.default_config_file)
                await self._write_yaml_file(backup_file, content)

            # 备份平台配置文件
            if self.platform_config_file.exists():
                backup_file = backup_path / f"platforms_{timestamp}.yaml"
                content = await self._read_yaml_file(self.platform_config_file)
                await self._write_yaml_file(backup_file, content)

            return True

        except Exception as e:
            handle_exception("backup_config", e, {"backup_dir": backup_dir})
            return False
