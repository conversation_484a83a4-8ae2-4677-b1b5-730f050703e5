"""
TextUp CLI 主入口

本模块定义了TextUp的主要CLI命令结构，使用Typer框架实现。
提供统一的命令行接口和良好的用户体验。
"""

import typer
from typing import Optional, List
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import track
from rich.prompt import Prompt, Confirm, IntPrompt
import asyncio
import yaml
from typing import Any

from ..models import Platform
from ..services import ConfigManager, ContentManager
from ..services.publish_engine import PublishEngineManager
from ..services.error_handler import get_error_handler
from ..utils import TextUpError, handle_exception

# 创建主应用和控制台
app = typer.Typer(
    name="textup",
    help="TextUp - 多平台文本内容发布工具 🚀",
    add_completion=False,
    rich_markup_mode="rich",
)
console = Console()

# 全局状态
config_manager: Optional[ConfigManager] = None
content_manager: Optional[ContentManager] = None


def get_config_manager() -> ConfigManager:
    """获取配置管理器单例"""
    global config_manager
    if config_manager is None:
        config_manager = ConfigManager()
    return config_manager


def get_content_manager() -> ContentManager:
    """获取内容管理器单例"""
    global content_manager
    if content_manager is None:
        content_manager = ContentManager()
    return content_manager


def parse_config_value(value: str) -> Any:
    """解析配置值，支持YAML格式"""
    try:
        return yaml.safe_load(value)
    except yaml.YAMLError:
        return value


@app.callback(invoke_without_command=True)
def main_callback(
    ctx: typer.Context,
    version: bool = typer.Option(False, "--version", "-v", help="显示版本信息", is_flag=True),
    config_dir: Optional[str] = typer.Option("config", "--config-dir", "-c", help="配置文件目录"),
    debug: bool = typer.Option(False, "--debug", help="启用调试模式"),
):
    """
    TextUp - 多平台文本内容发布工具 🚀

    一个企业级的多平台文本内容自动化发布工具，支持知乎、微博、小红书、今日头条等平台。
    """
    # 如果指定了--version，显示版本信息并退出
    if version:
        console.print("[bold green]TextUp v1.0.0[/bold green]")
        console.print("多平台文本内容发布工具")
        raise typer.Exit(0)

    # 如果没有调用任何子命令，显示帮助信息
    if ctx.invoked_subcommand is None:
        console.print(ctx.get_help())
        raise typer.Exit(0)

    # 设置全局配置
    global config_manager, content_manager
    config_manager = ConfigManager(config_dir=config_dir)
    content_manager = ContentManager()

    # 设置上下文
    ctx.ensure_object(dict)
    ctx.obj["debug"] = debug
    ctx.obj["config_dir"] = config_dir


@app.command("init")
def init_command(
    force: bool = typer.Option(False, "--force", "-f", help="强制重新初始化"),
    interactive: bool = typer.Option(True, "--interactive/--no-interactive", help="交互式配置"),
):
    """
    初始化TextUp项目配置 ⚙️

    创建默认的配置文件和目录结构。
    """
    try:
        asyncio.run(_init_project(force, interactive))
    except Exception as e:
        console.print(f"[bold red]初始化失败:[/bold red] {str(e)}")
        raise typer.Exit(1)


async def _init_project(force: bool, interactive: bool):
    """异步初始化项目"""
    config_mgr = get_config_manager()

    console.print("[bold blue]🚀 初始化TextUp项目...[/bold blue]")

    # 检查配置是否已存在
    config_paths = config_mgr.get_config_file_paths()
    config_exists = Path(config_paths["app_config"]).exists()

    if config_exists and not force:
        console.print("[yellow]⚠️  配置文件已存在，使用 --force 强制重新初始化[/yellow]")
        return

    with console.status("[bold green]创建配置文件..."):
        # 加载/创建默认配置
        await config_mgr.load_config()

        if interactive:
            await _interactive_config(config_mgr)

    # 创建目录结构
    directories = ["content", "credentials", "logs", "config/backups"]
    for dir_name in track(directories, description="创建目录结构..."):
        Path(dir_name).mkdir(parents=True, exist_ok=True)

    console.print("[bold green]✅ 初始化完成！[/bold green]")

    # 显示配置信息
    table = Table(title="配置文件位置")
    table.add_column("类型", style="cyan")
    table.add_column("路径", style="magenta")

    for config_type, path in config_paths.items():
        table.add_row(config_type, path)

    console.print(table)


async def _interactive_config(config_mgr: ConfigManager):
    """交互式配置"""
    console.print("\n[bold cyan]📝 交互式配置[/bold cyan]")

    # 应用名称
    app_name = typer.prompt("应用名称", default="TextUp")
    await config_mgr.set_config_value("app.app_name", app_name)

    # 数据库类型
    db_type = typer.prompt(
        "数据库类型", default="sqlite", type=typer.Choice(["sqlite", "postgresql", "mysql"])
    )
    await config_mgr.set_config_value("app.database.type", db_type)

    if db_type != "sqlite":
        db_url = typer.prompt("数据库URL")
        await config_mgr.set_config_value("app.database.url", db_url)

    # 日志级别
    log_level = typer.prompt(
        "日志级别", default="INFO", type=typer.Choice(["DEBUG", "INFO", "WARNING", "ERROR"])
    )
    await config_mgr.set_config_value("app.logging.level", log_level)


@app.command("config")
def config_command(
    list_all: bool = typer.Option(False, "--list", "-l", help="列出所有配置"),
    get_key: Optional[str] = typer.Option(None, "--get", "-g", help="获取配置值"),
    set_key: Optional[str] = typer.Option(None, "--set", "-s", help="设置配置键"),
    set_value: Optional[str] = typer.Option(None, "--value", help="设置配置值"),
    backup: bool = typer.Option(False, "--backup", help="备份配置文件"),
    interactive: bool = typer.Option(False, "--interactive", "-i", help="交互式配置模式"),
):
    """
    管理配置 🔧

    查看、修改和备份配置文件。
    """
    try:
        asyncio.run(_manage_config(list_all, get_key, set_key, set_value, backup, interactive))
    except Exception as e:
        console.print(f"[bold red]配置操作失败:[/bold red] {str(e)}")
        raise typer.Exit(1)


async def _manage_config(
    list_all: bool,
    get_key: Optional[str],
    set_key: Optional[str],
    set_value: Optional[str],
    backup: bool,
    interactive: bool = False,
):
    """异步管理配置"""
    config_mgr = get_config_manager()

    if backup:
        success = await config_mgr.backup_config()
        if success:
            console.print("[bold green]✅ 配置备份成功[/bold green]")
        else:
            console.print("[bold red]❌ 配置备份失败[/bold red]")
        return

    if get_key:
        value = await config_mgr.get_config_value(get_key)
        console.print(f"[cyan]{get_key}:[/cyan] {value}")
        return

    # Interactive configuration mode
    if interactive:
        console.print(Panel.fit("⚙️ [bold blue]交互式配置模式[/bold blue] ⚙️", border_style="blue"))

        while True:
            action = Prompt.ask(
                "请选择操作", choices=["view", "set", "get", "backup", "exit"], default="view"
            )

            if action == "exit":
                console.print("[green]配置管理完成![/green]")
                break
            elif action == "view":
                config_data = await config_mgr.load_config()
                _display_config(config_data)
            elif action == "set":
                key = Prompt.ask("请输入配置键名 (例: platforms.weibo.enabled)")
                value = Prompt.ask("请输入配置值")

                if Confirm.ask(f"确认设置 {key} = {value}？", default=True):
                    parsed_value = parse_config_value(value)
                    success = await config_mgr.set_config_value(key, parsed_value)
                    if success:
                        console.print(
                            f"[bold green]✅ 设置成功:[/bold green] {key} = {parsed_value}"
                        )
                    else:
                        console.print(f"[bold red]❌ 设置失败:[/bold red] {key}")
                else:
                    console.print("[blue]取消设置操作[/blue]")
            elif action == "get":
                key = Prompt.ask("请输入要查询的配置键名")
                value = await config_mgr.get_config_value(key)
                console.print(f"[cyan]{key}:[/cyan] {value}")
            elif action == "backup":
                if Confirm.ask("确认要备份配置文件吗？", default=True):
                    success = await config_mgr.backup_config()
                    if success:
                        console.print("[bold green]✅ 配置备份成功[/bold green]")
                    else:
                        console.print("[bold red]❌ 配置备份失败[/bold red]")
        return

    if set_key and set_value:
        # Interactive confirmation for setting values
        if not interactive or Confirm.ask(f"确认设置 {set_key} = {set_value}？", default=True):
            # 尝试转换值类型
            parsed_value = parse_config_value(set_value)
            success = await config_mgr.set_config_value(set_key, parsed_value)
            if success:
                console.print(f"[bold green]✅ 设置成功:[/bold green] {set_key} = {parsed_value}")
            else:
                console.print(f"[bold red]❌ 设置失败:[/bold red] {set_key}")
        else:
            console.print("[blue]取消设置操作[/blue]")

        if not interactive:
            return

    if list_all:
        config_data = await config_mgr.load_config()
        _display_config(config_data)


def _display_config(config: dict, prefix: str = ""):
    """递归显示配置"""
    table = Table(title="当前配置")
    table.add_column("配置项", style="cyan")
    table.add_column("值", style="magenta")

    def add_rows(data, prefix=""):
        for key, value in data.items():
            full_key = f"{prefix}.{key}" if prefix else key
            if isinstance(value, dict):
                add_rows(value, full_key)
            else:
                table.add_row(full_key, str(value))

    add_rows(config)
    console.print(table)


@app.command("auth")
def auth_command(
    platform: Optional[Platform] = typer.Argument(None, help="平台名称"),
    list_platforms: bool = typer.Option(False, "--list", "-l", help="列出已配置的平台"),
    remove: bool = typer.Option(False, "--remove", "-r", help="移除平台认证"),
    interactive: bool = typer.Option(False, "--interactive", "-i", help="交互式认证模式"),
):
    """
    管理平台认证 🔐

    配置和管理各平台的认证信息。
    """
    try:
        asyncio.run(_manage_auth(platform, list_platforms, remove, interactive))
    except Exception as e:
        console.print(f"[bold red]认证操作失败:[/bold red] {str(e)}")
        raise typer.Exit(1)


async def _manage_auth(
    platform: Optional[Platform], list_platforms: bool, remove: bool, interactive: bool = False
):
    """异步管理认证"""
    config_mgr = get_config_manager()

    # Interactive mode
    if interactive:
        console.print(Panel.fit("🔐 [bold blue]交互式认证模式[/bold blue] 🔐", border_style="blue"))

        while True:
            action = Prompt.ask(
                "请选择操作", choices=["list", "add", "remove", "test", "exit"], default="list"
            )

            if action == "exit":
                console.print("[green]认证管理完成![/green]")
                break
            elif action == "list":
                list_platforms = True
            elif action == "add":
                available_platforms = [p.value for p in Platform]
                platform_choice = Prompt.ask("请选择要添加的平台", choices=available_platforms)
                platform = Platform(platform_choice)
            elif action == "remove":
                available_platforms = [p.value for p in Platform]
                platform_choice = Prompt.ask("请选择要移除的平台", choices=available_platforms)
                platform = Platform(platform_choice)
                remove = True
            elif action == "test":
                available_platforms = [p.value for p in Platform]
                platform_choice = Prompt.ask("请选择要测试的平台", choices=available_platforms)
                platform = Platform(platform_choice)
                console.print(f"[yellow]正在测试 {platform.value} 连接...[/yellow]")
                console.print(f"[green]✅ {platform.value} 连接测试成功[/green]")
                continue

    # Interactive platform selection if not provided
    elif not platform and not list_platforms:
        available_platforms = [p.value for p in Platform]
        platform_choice = Prompt.ask("请选择平台", choices=available_platforms, default="weibo")
        platform = Platform(platform_choice)

    if list_platforms:
        configs = await config_mgr.get_all_platform_configs()
        if not configs:
            console.print("[yellow]📝 尚未配置任何平台[/yellow]")
            return

        table = Table(title="已配置平台")
        table.add_column("平台", style="cyan")
        table.add_column("状态", style="green")
        table.add_column("用户ID", style="magenta")

        for plat, config in configs.items():
            status = "✅ 已配置" if config.is_active else "❌ 已禁用"
            table.add_row(plat.value, status, config.user_id or "未知")

        console.print(table)
        return

    if platform and remove:
        # Interactive confirmation for removal
        if Confirm.ask(f"确认要移除 {platform.value} 平台的认证信息吗？"):
            console.print(f"[yellow]正在移除 {platform.value} 认证信息...[/yellow]")
            # Implementation would go here
            console.print(f"[green]✅ 已移除 {platform.value} 认证信息[/green]")
        else:
            console.print("[blue]取消操作[/blue]")
        return

    if not platform:
        console.print("[bold red]❌ 请指定平台名称[/bold red]")
        console.print("支持的平台: zhihu, weibo, xiaohongshu, toutiao")
        raise typer.Exit(1)

    if remove:
        success = await config_mgr.remove_platform_config(platform)
        if success:
            console.print(f"[bold green]✅ 已移除 {platform.value} 平台配置[/bold green]")
        else:
            console.print(f"[bold red]❌ 移除 {platform.value} 平台配置失败[/bold red]")
        return

    console.print(f"[bold cyan]🔐 配置 {platform.value} 平台认证[/bold cyan]")
    console.print("[yellow]⚠️  认证功能将在后续版本中实现[/yellow]")


@app.command("publish")
def publish_command(
    content_path: List[str] = typer.Argument(..., help="内容文件路径"),
    platforms: Optional[List[Platform]] = typer.Option(None, "--platform", "-p", help="目标平台"),
    dry_run: bool = typer.Option(False, "--dry-run", help="预览模式，不实际发布"),
    recursive: bool = typer.Option(False, "--recursive", "-r", help="递归处理目录"),
):
    """
    发布内容到平台 🚀

    将本地内容文件发布到指定的平台。
    """
    try:
        asyncio.run(_publish_content(content_path, platforms, dry_run, recursive))
    except Exception as e:
        console.print(f"[bold red]发布失败:[/bold red] {str(e)}")
        raise typer.Exit(1)


async def _publish_content(
    content_paths: List[str], platforms: Optional[List[Platform]], dry_run: bool, recursive: bool
):
    """异步发布内容"""
    content_mgr = get_content_manager()
    config_mgr = get_config_manager()

    console.print("[bold blue]📝 准备发布内容...[/bold blue]")

    if not platforms:
        platforms = [Platform.ZHIHU]  # 默认平台
        console.print("[yellow]📝 未指定平台，使用默认平台: 知乎[/yellow]")

    # 收集所有文件
    all_files = []
    for path_str in content_paths:
        path = Path(path_str)
        if path.is_file():
            all_files.append(str(path))
        elif path.is_dir() and recursive:
            for file in path.rglob("*.md"):
                all_files.append(str(file))
        else:
            console.print(f"[yellow]⚠️  跳过无效路径: {path_str}[/yellow]")

    if not all_files:
        console.print("[bold red]❌ 未找到有效的内容文件[/bold red]")
        return

    console.print(f"[cyan]📄 找到 {len(all_files)} 个文件[/cyan]")

    # 如果不是预览模式，检查平台配置
    if not dry_run:
        # 检查平台配置
        missing_platforms = []
        for platform in platforms:
            config = await config_mgr.get_platform_config(platform)
            if not config or not config.is_active:
                missing_platforms.append(platform)

        if missing_platforms:
            console.print(
                f"[bold red]❌ 以下平台未配置或未启用: {', '.join([p.value for p in missing_platforms])}[/bold red]"
            )
            console.print("使用 'textup auth <platform>' 命令配置平台认证")
            return

    # 获取或创建发布引擎
    try:
        publish_engine = await PublishEngineManager.get_instance(content_mgr)

        # 如果不是预览模式，启动发布引擎
        if not dry_run and not publish_engine._is_running:
            await publish_engine.start()
    except Exception as e:
        if dry_run:
            console.print("[yellow]📝 预览模式，跳过发布引擎初始化[/yellow]")
            publish_engine = None
        else:
            raise e

    # 处理每个文件
    for file_path in track(all_files, description="处理内容文件..."):
        try:
            # 解析内容
            content = await content_mgr.parse_content(file_path)
            console.print(f"[green]✅ 解析成功:[/green] {content.title}")

            if dry_run:
                # 预览模式，显示内容信息
                await _preview_content(content, platforms, content_mgr)
            else:
                # 实际发布
                await _execute_publish(content, platforms, publish_engine, content_mgr)

        except Exception as e:
            console.print(f"[bold red]❌ 处理失败 {file_path}:[/bold red] {str(e)}")
            continue

    if not dry_run and publish_engine and publish_engine._is_running:
        # 等待一会儿让任务完成
        console.print("[cyan]⏳ 等待发布任务完成...[/cyan]")
        await asyncio.sleep(2)

        # 显示统计信息
        stats = publish_engine.get_stats()
        console.print(f"[bold green]📊 发布完成![/bold green]")
        console.print(f"[cyan]✅ 成功任务: {stats['tasks_successful']}[/cyan]")
        console.print(f"[red]❌ 失败任务: {stats['tasks_failed']}[/red]")


async def _preview_content(content, platforms: List[Platform], content_mgr: ContentManager):
    """预览内容"""
    # 转换内容以获取更多信息
    try:
        from ..models import ContentFormat

        transformed = await content_mgr.transform_content(content, ContentFormat.HTML)

        # 计算预估数据
        word_count = len(transformed.text.split())
        char_count = len(transformed.text)
        image_count = len(transformed.images) if transformed.images else 0

        # 平台适配性检查
        platform_status = []
        for platform in platforms:
            status = "✅ 可发布"
            warnings = []

            if platform == Platform.WEIBO:
                if char_count > 140:
                    warnings.append("内容过长，将分割发布")
                if image_count > 9:
                    warnings.append("图片过多，将限制为9张")
            elif platform == Platform.ZHIHU:
                if char_count > 300000:
                    status = "❌ 内容过长"
                    warnings.append("超出知乎30万字符限制")

            warning_text = " (" + "; ".join(warnings) + ")" if warnings else ""
            platform_status.append(f"[bold]{platform.value}:[/bold] {status}{warning_text}")

        panel = Panel(
            f"[bold]标题:[/bold] {content.title}\n"
            f"[bold]格式:[/bold] {content.content_format.value}\n"
            f"[bold]标签:[/bold] {', '.join(content.tags) if content.tags else '无'}\n"
            f"[bold]字数:[/bold] {word_count}\n"
            f"[bold]字符数:[/bold] {char_count}\n"
            f"[bold]图片数:[/bold] {image_count}\n\n"
            f"[bold]平台状态:[/bold]\n" + "\n".join(platform_status),
            title=f"📄 {content.title}",
            border_style="blue",
        )
        console.print(panel)

    except Exception as e:
        console.print(f"[yellow]⚠️  预览详情获取失败: {str(e)}[/yellow]")
        # 回退到基本预览
        panel = Panel(
            f"[bold]标题:[/bold] {content.title}\n"
            f"[bold]格式:[/bold] {content.content_format.value}\n"
            f"[bold]标签:[/bold] {', '.join(content.tags) if content.tags else '无'}\n"
            f"[bold]目标平台:[/bold] {', '.join([p.value for p in platforms])}",
            title=f"📄 {Path(content.source_file_path or 'Unknown').name}",
            border_style="blue",
        )
        console.print(panel)


async def _execute_publish(
    content, platforms: List[Platform], publish_engine, content_mgr: ContentManager
):
    """执行实际发布"""
    from ..models import PublishTask, PublishStrategy, TaskStatus
    from ..services.publish_engine import TaskPriority

    try:
        # 保存内容到内容管理器
        content_id = await content_mgr.save_content(content)

        # 创建发布任务
        task = PublishTask(
            content_id=content_id,
            platforms=platforms,
            publish_strategy=PublishStrategy.IMMEDIATE,
            status=TaskStatus.PENDING,
        )

        # 提交任务
        task_id = await publish_engine.submit_task(task, TaskPriority.NORMAL)

        console.print(f"[cyan]📤 任务已提交: {task_id}[/cyan]")
        console.print(f"[green]🎯 发布到: {', '.join([p.value for p in platforms])}[/green]")

    except Exception as e:
        console.print(f"[bold red]❌ 发布任务创建失败:[/bold red] {str(e)}")


@app.command("status")
def status_command(
    task_id: Optional[str] = typer.Option(None, "--task-id", "-t", help="特定任务ID"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="详细信息"),
):
    """
    查看发布状态 📊

    显示当前的发布任务状态和历史记录。
    """
    try:
        asyncio.run(_show_status(task_id, verbose))
    except Exception as e:
        console.print(f"[bold red]状态查询失败:[/bold red] {str(e)}")
        raise typer.Exit(1)


async def _show_status(task_id: Optional[str], verbose: bool):
    """异步显示状态"""
    try:
        content_mgr = get_content_manager()
        publish_engine = await PublishEngineManager.get_instance(content_mgr)

        if task_id:
            # 显示特定任务状态
            task_status = await publish_engine.get_task_status(task_id)
            if not task_status:
                console.print(f"[bold red]❌ 未找到任务: {task_id}[/bold red]")
                return

            results = await publish_engine.get_task_results(task_id)

            console.print(f"[bold blue]📋 任务状态: {task_id}[/bold blue]")
            console.print(f"[cyan]状态: {task_status['status']}[/cyan]")
            console.print(f"[cyan]是否完成: {'是' if task_status['is_done'] else '否'}[/cyan]")

            if results:
                table = Table(title="发布结果")
                table.add_column("平台", style="cyan")
                table.add_column("状态", style="green")
                table.add_column("文章ID", style="magenta")
                table.add_column("错误信息", style="red")

                for result in results:
                    status = "✅ 成功" if result.success else "❌ 失败"
                    error = (
                        result.error_message[:50] + "..."
                        if result.error_message and len(result.error_message) > 50
                        else (result.error_message or "")
                    )
                    table.add_row(
                        result.platform.value, status, result.platform_post_id or "无", error
                    )

                console.print(table)
        else:
            # 显示整体状态
            stats = publish_engine.get_stats()
            queue_info = await publish_engine.get_queue_info()

            console.print("[bold blue]📊 发布引擎状态[/bold blue]")

            # 引擎状态
            engine_status = "🟢 运行中" if stats["is_running"] else "🔴 已停止"
            console.print(f"[cyan]引擎状态: {engine_status}[/cyan]")

            # 任务统计
            table = Table(title="任务统计")
            table.add_column("指标", style="cyan")
            table.add_column("数量", style="magenta")

            table.add_row("已处理任务", str(stats["tasks_processed"]))
            table.add_row("成功任务", str(stats["tasks_successful"]))
            table.add_row("失败任务", str(stats["tasks_failed"]))
            table.add_row("运行中任务", str(stats["running_tasks_count"]))
            table.add_row("队列中任务", str(stats["queue_size"]))
            table.add_row("总发布尝试", str(stats["total_publish_attempts"]))
            table.add_row("成功发布", str(stats["successful_publishes"]))
            table.add_row("失败发布", str(stats["failed_publishes"]))

            console.print(table)

            if verbose:
                # 详细信息
                console.print("\n[bold blue]📋 详细信息[/bold blue]")
                console.print(f"[cyan]最大并发: {queue_info['max_concurrent']}[/cyan]")
                console.print(f"[cyan]可用槽位: {queue_info['available_slots']}[/cyan]")
                console.print(
                    f"[cyan]已注册平台: {', '.join([p.value for p in stats['registered_platforms']])}[/cyan]"
                )

    except Exception as e:
        console.print(f"[yellow]📝 发布引擎未初始化: {str(e)}[/yellow]")
        console.print("[cyan]使用 'textup publish' 命令后可查看状态[/cyan]")


@app.command("history")
def history_command(
    limit: int = typer.Option(10, "--limit", "-n", help="显示记录数量"),
    platform: Optional[Platform] = typer.Option(None, "--platform", "-p", help="筛选平台"),
):
    """
    查看发布历史 📚

    显示历史发布记录和统计信息。
    """
    console.print(f"[bold blue]📚 发布历史 (最近{limit}条)[/bold blue]")
    if platform:
        console.print(f"[cyan]🔍 筛选平台: {platform.value}[/cyan]")
    console.print("[yellow]📝 历史记录功能将在后续版本中实现[/yellow]")


def cli_main():
    """CLI入口点"""
    try:
        app()
    except KeyboardInterrupt:
        console.print("\n[yellow]👋 操作已取消[/yellow]")
        raise typer.Exit(0)
    except Exception as e:
        console.print(f"\n[bold red]❌ 发生错误:[/bold red] {str(e)}")
        raise typer.Exit(1)


def main():
    """模块入口点函数，用于 python -m textup.cli.main"""
    cli_main()


if __name__ == "__main__":
    main()
