"""
知乎平台适配器（兼容性模块）

由于知乎没有公开的HTTP API，本模块现在直接使用Playwright实现。
此文件保持向后兼容性，将ZhihuPlaywrightAdapter重新导出为ZhihuAdapter。

推荐直接使用ZhihuPlaywrightAdapter以获得更好的类型提示和文档。
"""

import warnings
from .zhihu_playwright import ZhihuPlaywrightAdapter

# 为了向后兼容，将Playwright适配器作为默认的知乎适配器
ZhihuAdapter = ZhihuPlaywrightAdapter

# 发出弃用警告，提醒用户直接使用Playwright适配器
def _issue_deprecation_warning():
    warnings.warn(
        "从 zhihu.py 导入 ZhihuAdapter 已弃用。"
        "请直接使用 ZhihuPlaywrightAdapter from zhihu_playwright，"
        "因为知乎没有公开的HTTP API，只支持Playwright自动化方式。",
        DeprecationWarning,
        stacklevel=3
    )

# 在导入时发出警告
_issue_deprecation_warning()

__all__ = ['ZhihuAdapter']
