"""
今日头条(Toutiao)适配器模块

今日头条是字节跳动旗下的内容平台，支持文章发布、多媒体内容等功能。
"""

import asyncio
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from urllib.parse import urlencode, quote

import aiohttp
from pydantic import ValidationError

from .base import BaseAdapter
from ..models import (
    Content,
    TransformedContent,
    PublishResult,
    AuthResult,
    ValidationResult,
    ValidationError as ModelValidationError,
    Platform,
    ContentFormat,
    ToutiaoCredentials,
    ToutiaoPublishOptions,
)
from ..utils.exceptions import (
    PlatformAPIError,
    AuthenticationError,
    ContentValidationError,
    RateLimitError,
    InvalidCredentialsError,
)


class ToutiaoAdapter(BaseAdapter):
    """今日头条适配器

    支持今日头条平台的内容发布功能，包括：
    - OAuth2.0 认证流程
    - 文章发布
    - 内容格式转换
    - 错误处理和重试
    """

    def __init__(self, **kwargs):
        super().__init__(
            timeout=30,
            max_retries=3,
            retry_delay=2.0,
            rate_limit_calls=100,  # 今日头条API限制：100次/小时
            rate_limit_period=3600,
            **kwargs
        )

    @property
    def platform(self) -> str:
        """平台标识"""
        return Platform.TOUTIAO.value

    @property
    def base_url(self) -> str:
        """API基础URL"""
        return "https://developer.toutiao.com/api"

    @property
    def oauth_base_url(self) -> str:
        """OAuth认证基础URL"""
        return "https://developer.toutiao.com/oauth"

    @property
    def required_credentials(self) -> List[str]:
        """必需的认证凭据字段"""
        return ["app_id", "secret", "redirect_uri"]

    def _validate_credentials(self, credentials: Dict[str, Any]) -> ValidationResult:
        """验证认证凭据"""
        errors = []

        # 检查必需字段
        required_fields = ["app_id", "secret", "redirect_uri"]
        for field in required_fields:
            if field not in credentials:
                errors.append(ModelValidationError(
                    field=field,
                    message=f"字段不能为空: {field}",
                    value="",
                    error_code="MISSING_FIELD",
                    level="error"
                ))
            elif not credentials[field] or not credentials[field].strip():
                errors.append(ModelValidationError(
                    field=field,
                    message=f"字段不能为空: {field}",
                    value=credentials[field],
                    error_code="EMPTY_FIELD",
                    level="error"
                ))

        if not errors:
            # 验证app_id格式 (通常是数字)
            app_id = credentials.get("app_id", "")
            if not re.match(r'^\d+$', app_id):
                errors.append(ModelValidationError(
                    field="app_id",
                    message="app_id格式不正确，应为纯数字",
                    value=app_id,
                    error_code="INVALID_FORMAT",
                    level="error"
                ))

            # 验证secret格式 (通常是32位字符串)
            secret = credentials.get("secret", "")
            if len(secret) < 32:
                errors.append(ModelValidationError(
                    field="secret",
                    message="secret格式不正确，长度不足32位",
                    value=secret,
                    error_code="INVALID_LENGTH",
                    level="error"
                ))

            # 验证redirect_uri格式
            redirect_uri = credentials.get("redirect_uri", "")
            if not redirect_uri.startswith(("http://", "https://")):
                errors.append(ModelValidationError(
                    field="redirect_uri",
                    message="redirect_uri格式不正确，必须是有效的URL",
                    value=redirect_uri,
                    error_code="INVALID_URL",
                    level="error"
                ))

        return ValidationResult(is_valid=len(errors) == 0, errors=errors)

    def generate_auth_url(
        self,
        credentials: Dict[str, Any],
        state: Optional[str] = None
    ) -> str:
        """生成OAuth认证URL"""
        import secrets

        if not state:
            state = secrets.token_urlsafe(32)

        params = {
            "client_id": credentials["app_id"],
            "redirect_uri": credentials["redirect_uri"],
            "state": state,
            "response_type": "code",
            "scope": "article.publish"  # 今日头条文章发布权限
        }

        return f"{self.oauth_base_url}/authorize/?{urlencode(params)}"

    async def exchange_code_for_token(
        self,
        credentials: Dict[str, Any],
        authorization_code: str
    ) -> Dict[str, Any]:
        """交换访问令牌"""
        token_url = f"{self.oauth_base_url}/access_token/"

        data = {
            "client_id": credentials["app_id"],
            "client_secret": credentials["secret"],
            "code": authorization_code,
            "grant_type": "authorization_code",
            "redirect_uri": credentials["redirect_uri"]
        }

        try:
            response = await self._make_request("POST", token_url, data=data)

            if "access_token" in response:
                return response
            else:
                error_msg = response.get("message", "获取访问令牌失败")
                raise PlatformAPIError(f"获取访问令牌失败: {error_msg}", self.platform)

        except Exception as e:
            if isinstance(e, PlatformAPIError):
                raise
            raise PlatformAPIError(f"令牌交换请求失败: {str(e)}", self.platform) from e

    async def refresh_access_token(
        self,
        credentials: Dict[str, Any],
        refresh_token: str
    ) -> Dict[str, Any]:
        """刷新访问令牌"""
        refresh_url = f"{self.oauth_base_url}/refresh_token/"

        data = {
            "client_id": credentials["app_id"],
            "client_secret": credentials["secret"],
            "refresh_token": refresh_token,
            "grant_type": "refresh_token"
        }

        try:
            response = await self._make_request("POST", refresh_url, data=data)

            if "access_token" in response:
                return response
            else:
                error_msg = response.get("message", "刷新令牌失败")
                raise PlatformAPIError(f"刷新令牌失败: {error_msg}", self.platform)

        except Exception as e:
            if isinstance(e, PlatformAPIError):
                raise
            raise PlatformAPIError(f"刷新令牌请求失败: {str(e)}", self.platform) from e

    def _get_auth_headers(self, access_token: Optional[str] = None) -> Dict[str, str]:
        """获取认证请求头"""
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "TextUp/1.0.0"
        }

        if access_token:
            headers["access-token"] = access_token

        return headers

    async def _authenticate_impl(
        self,
        credentials: Dict[str, Any]
    ) -> AuthResult:
        """认证实现"""
        try:
            # 验证凭据
            validation_result = self._validate_credentials(credentials)
            if not validation_result.is_valid:
                return AuthResult(
                    success=False,
                    access_token=None,
                    expires_at=None,
                    error_message=f"凭据验证失败: {', '.join([e.message for e in validation_result.errors])}"
                )

            # 检查是否已有有效令牌
            if "access_token" in credentials and credentials["access_token"]:
                # 验证令牌有效性
                if await self._verify_token(credentials["access_token"]):
                    expires_at = None
                    if "expires_at" in credentials:
                        expires_at = datetime.fromisoformat(credentials["expires_at"])

                    return AuthResult(
                        success=True,
                        access_token=credentials["access_token"],
                        expires_at=expires_at
                    )

            # 需要重新认证
            return AuthResult(
                success=False,
                access_token=None,
                expires_at=None,
                error_message="需要重新进行OAuth认证"
            )

        except Exception as e:
            return AuthResult(
                success=False,
                access_token=None,
                expires_at=None,
                error_message=f"认证过程出错: {str(e)}"
            )

    async def _verify_token(self, access_token: str) -> bool:
        """验证令牌有效性"""
        try:
            headers = self._get_auth_headers(access_token)
            # 调用用户信息接口验证令牌
            await self._make_request(
                "GET",
                f"{self.base_url}/user/info/",
                headers=headers
            )
            return True
        except:
            return False

    def _validate_format_impl(self, content: TransformedContent) -> ValidationResult:
        """验证内容格式"""
        errors = []

        # 标题验证
        if not content.title or len(content.title.strip()) == 0:
            errors.append(ModelValidationError(
                field="title",
                message="标题不能为空",
                value=content.title,
                error_code="EMPTY_TITLE",
                level="error"
            ))
        elif len(content.title) > 100:
            errors.append(ModelValidationError(
                field="title",
                message="标题长度不能超过100个字符",
                value=content.title,
                error_code="TITLE_TOO_LONG",
                level="error"
            ))

        # 内容验证
        if not content.content or len(content.content.strip()) == 0:
            errors.append(ModelValidationError(
                field="content",
                message="内容不能为空",
                value=content.content,
                error_code="EMPTY_CONTENT",
                level="error"
            ))
        elif len(content.content) < 100:
            errors.append(ModelValidationError(
                field="content",
                message="文章内容不能少于100个字符",
                value=content.content,
                error_code="CONTENT_TOO_SHORT",
                level="warning"
            ))
        elif len(content.content) > 50000:
            errors.append(ModelValidationError(
                field="content",
                message="文章内容不能超过50000个字符",
                value=content.content,
                error_code="CONTENT_TOO_LONG",
                level="error"
            ))

        # 格式验证
        if content.format not in [ContentFormat.MARKDOWN, ContentFormat.HTML, ContentFormat.TEXT]:
            errors.append(ModelValidationError(
                field="format",
                message=f"不支持的内容格式: {content.format}",
                value=content.format,
                error_code="UNSUPPORTED_FORMAT",
                level="error"
            ))

        return ValidationResult(is_valid=len(errors) == 0, errors=errors)

    async def transform_content(
        self,
        content: Content,
        platform_options: Optional[Dict[str, Any]] = None
    ) -> TransformedContent:
        """转换内容格式"""
        # 今日头条支持HTML格式
        if content.format == ContentFormat.MARKDOWN:
            # 将Markdown转换为HTML
            html_content = self._markdown_to_html(content.content)
            return TransformedContent(
                title=content.title,
                content=html_content,
                format=ContentFormat.HTML
            )
        elif content.format == ContentFormat.TEXT:
            # 纯文本转为简单HTML
            html_content = content.content.replace('\n', '<br>\n')
            return TransformedContent(
                title=content.title,
                content=html_content,
                format=ContentFormat.HTML
            )
        else:
            # HTML格式直接使用
            return TransformedContent(
                title=content.title,
                content=content.content,
                format=ContentFormat.HTML
            )

    def _markdown_to_html(self, markdown_text: str) -> str:
        """简单的Markdown到HTML转换"""
        # 这里实现基本的Markdown转换
        html = markdown_text

        # 标题转换
        html = re.sub(r'^# (.*$)', r'<h1>\1</h1>', html, flags=re.MULTILINE)
        html = re.sub(r'^## (.*$)', r'<h2>\1</h2>', html, flags=re.MULTILINE)
        html = re.sub(r'^### (.*$)', r'<h3>\1</h3>', html, flags=re.MULTILINE)

        # 粗体和斜体
        html = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', html)
        html = re.sub(r'\*(.*?)\*', r'<em>\1</em>', html)

        # 链接
        html = re.sub(r'\[([^\]]+)\]\(([^)]+)\)', r'<a href="\2">\1</a>', html)

        # 代码块
        html = re.sub(r'```([^`]+)```', r'<pre><code>\1</code></pre>', html, flags=re.DOTALL)
        html = re.sub(r'`([^`]+)`', r'<code>\1</code>', html)

        # 段落
        paragraphs = html.split('\n\n')
        html_paragraphs = []
        for p in paragraphs:
            if p.strip() and not p.strip().startswith('<'):
                html_paragraphs.append(f'<p>{p.strip()}</p>')
            else:
                html_paragraphs.append(p)

        return '\n\n'.join(html_paragraphs)

    async def _publish_impl(self, content: TransformedContent) -> Dict[str, Any]:
        """发布实现"""
        # 这里需要实际的访问令牌
        access_token = getattr(self, '_current_access_token', None)
        if not access_token:
            raise AuthenticationError("未找到有效的访问令牌，请先进行认证", self.platform)

        publish_url = f"{self.base_url}/media/article/publish/"
        headers = self._get_auth_headers(access_token)

        # 准备发布数据
        publish_data = {
            "title": content.title,
            "content": content.content,
            "content_type": "html",
            "original_type": 1,  # 原创
            "category": "其他",  # 默认分类
            "tag": []
        }

        try:
            response = await self._make_request(
                "POST",
                publish_url,
                headers=headers,
                json=publish_data
            )

            if response.get("err_no") == 0:
                # 发布成功
                data = response.get("data", {})
                return {
                    "id": data.get("article_id"),
                    "title": content.title,
                    "url": data.get("article_url"),
                    "status": "published",
                    "platform": self.platform,
                    "published_at": datetime.now().isoformat()
                }
            else:
                # 发布失败
                error_msg = response.get("err_msg", "发布失败")
                raise PlatformAPIError(f"发布失败: {error_msg}", self.platform)

        except aiohttp.ClientError as e:
            raise PlatformAPIError(f"网络请求失败: {str(e)}", self.platform) from e
        except Exception as e:
            if isinstance(e, (PlatformAPIError, AuthenticationError)):
                raise
            raise PlatformAPIError(f"发布过程出错: {str(e)}", self.platform) from e

    async def _handle_api_error(self, response: Dict[str, Any]) -> None:
        """处理API错误响应"""
        err_no = response.get("err_no", 0)
        err_msg = response.get("err_msg", "未知错误")

        if err_no == 40001:
            raise InvalidCredentialsError("访问令牌无效", self.platform)
        elif err_no == 40003:
            raise AuthenticationError("访问令牌已过期", self.platform)
        elif err_no == 40006:
            raise RateLimitError("API调用频率超限", self.platform)
        elif err_no == 40007:
            raise ContentValidationError(f"内容审核失败: {err_msg}")
        elif err_no == 40011:
            raise ContentValidationError("内容包含敏感信息")
        elif err_no != 0:
            raise PlatformAPIError(f"API错误 ({err_no}): {err_msg}", self.platform)

    def _extract_error_info(self, error_response: Dict[str, Any]) -> str:
        """提取错误信息"""
        if "err_msg" in error_response:
            return error_response["err_msg"]
        elif "message" in error_response:
            return error_response["message"]
        elif "error_description" in error_response:
            return error_response["error_description"]
        else:
            return "未知错误"

    async def get_article_list(
        self,
        access_token: str,
        page: int = 1,
        size: int = 10
    ) -> Dict[str, Any]:
        """获取文章列表"""
        headers = self._get_auth_headers(access_token)
        params = {
            "page": page,
            "size": size
        }

        try:
            response = await self._make_request(
                "GET",
                f"{self.base_url}/media/article/list/",
                headers=headers,
                params=params
            )
            return response
        except Exception as e:
            raise PlatformAPIError(f"获取文章列表失败: {str(e)}", self.platform) from e

    async def delete_article(self, access_token: str, article_id: str) -> bool:
        """删除文章"""
        headers = self._get_auth_headers(access_token)
        data = {"article_id": article_id}

        try:
            response = await self._make_request(
                "POST",
                f"{self.base_url}/media/article/delete/",
                headers=headers,
                json=data
            )
            return response.get("err_no") == 0
        except Exception as e:
            raise PlatformAPIError(f"删除文章失败: {str(e)}", self.platform) from e

    def __str__(self) -> str:
        return f"ToutiaoAdapter(platform={self.platform})"

    async def _get_publish_status_impl(self, publish_id: str) -> Dict[str, Any]:
        """获取发布状态实现"""
        access_token = getattr(self, '_current_access_token', None)
        if not access_token:
            raise AuthenticationError("未找到有效的访问令牌，请先进行认证", self.platform)

        status_url = f"{self.base_url}/media/article/status/"
        headers = self._get_auth_headers(access_token)
        params = {"article_id": publish_id}

        try:
            response = await self._make_request(
                "GET",
                status_url,
                headers=headers,
                params=params
            )

            if response.get("err_no") == 0:
                data = response.get("data", {})
                return {
                    "id": data.get("article_id"),
                    "status": data.get("status", "unknown"),
                    "title": data.get("title"),
                    "url": data.get("article_url"),
                    "publish_time": data.get("publish_time"),
                    "platform": self.platform
                }
            else:
                error_msg = response.get("err_msg", "获取状态失败")
                raise PlatformAPIError(f"获取发布状态失败: {error_msg}", self.platform)

        except Exception as e:
            if isinstance(e, (PlatformAPIError, AuthenticationError)):
                raise
            raise PlatformAPIError(f"获取发布状态失败: {str(e)}", self.platform) from e

    def __repr__(self) -> str:
        return f"ToutiaoAdapter(platform='{self.platform}', base_url='{self.base_url}')"
