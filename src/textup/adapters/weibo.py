"""
微博平台适配器

本模块实现了微博平台的内容发布功能，包括：
1. 微博API集成
2. 140字限制处理和内容分割
3. 图片上传支持
4. <PERSON>ie认证备用方案

微博API文档参考：
- https://open.weibo.com/wiki/
- https://developers.weibo.com/
"""

import asyncio
import json
import hashlib
import time
import re
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
from urllib.parse import urlencode, quote
import base64

from .base import BaseAdapter
from ..models import (
    Platform,
    TransformedContent,
    PublishResult,
    AuthResult,
    ValidationResult,
    ValidationError,
    ContentFormat,
)
from ..utils import PlatformAPIError, InvalidCredentialsError, RateLimitError


class WeiboAdapter(BaseAdapter):
    """微博平台适配器"""

    def __init__(self, **kwargs):
        super().__init__(
            timeout=30,
            max_retries=3,
            retry_delay=1.5,
            rate_limit_calls=150,  # 微博API限制：150次/小时
            rate_limit_period=3600,
            **kwargs,
        )
        self._app_key: Optional[str] = None
        self._app_secret: Optional[str] = None
        self._redirect_uri: Optional[str] = None

    @property
    def platform(self) -> Platform:
        """返回平台标识"""
        return Platform.WEIBO

    @property
    def base_url(self) -> str:
        """返回微博API基础URL"""
        return "https://api.weibo.com/2"

    @property
    def oauth_base_url(self) -> str:
        """返回OAuth基础URL"""
        return "https://api.weibo.com/oauth2"

    @property
    def required_credentials(self) -> List[str]:
        """返回必需的认证字段"""
        return ["app_key", "app_secret", "redirect_uri"]

    def _get_auth_headers(self) -> Dict[str, str]:
        """获取认证头"""
        headers = {}
        if self._auth_data and self._auth_data.get("access_token"):
            headers["Authorization"] = f"OAuth2 {self._auth_data['access_token']}"
        return headers

    def _validate_credentials(self, credentials: Dict[str, Any]) -> ValidationResult:
        """验证认证凭证格式"""
        errors = []

        # 检查必需字段
        for field in self.required_credentials:
            if field not in credentials:
                errors.append(
                    ValidationError(
                        field=field,
                        message=f"缺少必需字段: {field}",
                        value=None,
                        error_code="MISSING_FIELD",
                    )
                )
            elif not credentials[field]:
                errors.append(
                    ValidationError(
                        field=field,
                        message=f"字段不能为空: {field}",
                        value=credentials[field],
                        error_code="EMPTY_FIELD",
                    )
                )

        # 验证app_key格式（微博的app_key是数字）
        if "app_key" in credentials and credentials["app_key"]:
            app_key = str(credentials["app_key"])
            if not app_key.isdigit():
                errors.append(
                    ValidationError(
                        field="app_key",
                        message="app_key应为纯数字",
                        value=app_key,
                        error_code="INVALID_FORMAT",
                    )
                )

        # 验证redirect_uri格式
        if "redirect_uri" in credentials and credentials["redirect_uri"]:
            redirect_uri = credentials["redirect_uri"]
            if not (redirect_uri.startswith("http://") or redirect_uri.startswith("https://")):
                errors.append(
                    ValidationError(
                        field="redirect_uri",
                        message="redirect_uri必须是有效的HTTP URL",
                        value=redirect_uri,
                        error_code="INVALID_URL",
                    )
                )

        return ValidationResult(is_valid=len(errors) == 0, errors=errors)

    def generate_auth_url(self, credentials: Dict[str, Any]) -> str:
        """
        生成OAuth认证URL

        Args:
            credentials: 认证凭证

        Returns:
            认证URL
        """
        params = {
            "client_id": credentials["app_key"],
            "redirect_uri": credentials["redirect_uri"],
            "response_type": "code",
            "scope": "write",  # 发布权限
        }

        return f"{self.oauth_base_url}/authorize?{urlencode(params)}"

    async def exchange_code_for_token(
        self, credentials: Dict[str, Any], auth_code: str
    ) -> Dict[str, Any]:
        """
        使用授权码换取访问令牌

        Args:
            credentials: 认证凭证
            auth_code: 授权码

        Returns:
            令牌信息
        """
        token_url = f"{self.oauth_base_url}/access_token"

        data = {
            "client_id": credentials["app_key"],
            "client_secret": credentials["app_secret"],
            "grant_type": "authorization_code",
            "code": auth_code,
            "redirect_uri": credentials["redirect_uri"],
        }

        response = await self._make_request(
            method="POST",
            url=token_url,
            data=data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        )

        if "access_token" not in response:
            raise PlatformAPIError(
                platform=self.platform.value,
                api_error="获取访问令牌失败",
                context={"response": response},
            )

        return response

    async def _authenticate_impl(self, credentials: Dict[str, Any]) -> AuthResult:
        """
        具体的认证实现

        Args:
            credentials: 认证凭证

        Returns:
            认证结果
        """
        try:
            # 保存认证配置
            self._app_key = str(credentials["app_key"])
            self._app_secret = credentials["app_secret"]
            self._redirect_uri = credentials["redirect_uri"]

            # 如果已有访问令牌，直接验证
            if "access_token" in credentials:
                access_token = credentials["access_token"]

                # 验证令牌有效性
                user_info = await self._get_user_info(access_token)

                auth_data = {
                    "access_token": access_token,
                    "user_info": user_info,
                    "expires_at": credentials.get("expires_at"),
                }

                return AuthResult(
                    success=True, platform=self.platform, auth_data=auth_data, message="认证成功"
                )

            # 检查是否有Cookie认证信息
            elif "cookies" in credentials:
                # Cookie认证备用方案
                cookies = credentials["cookies"]
                user_info = await self._authenticate_with_cookies(cookies)

                auth_data = {"cookies": cookies, "user_info": user_info, "auth_type": "cookie"}

                return AuthResult(
                    success=True,
                    platform=self.platform,
                    auth_data=auth_data,
                    message="Cookie认证成功",
                )

            # 需要OAuth流程
            auth_url = self.generate_auth_url(credentials)

            return AuthResult(
                success=False,
                platform=self.platform,
                message="需要完成OAuth认证",
                auth_url=auth_url,
            )

        except Exception as e:
            return AuthResult(
                success=False,
                platform=self.platform,
                error_message=f"认证失败: {str(e)}",
                error_details={"exception": type(e).__name__},
            )

    async def _get_user_info(self, access_token: str) -> Dict[str, Any]:
        """
        获取用户信息

        Args:
            access_token: 访问令牌

        Returns:
            用户信息
        """
        url = f"{self.base_url}/users/show.json"
        params = {"access_token": access_token, "uid": "me"}

        response = await self._make_request("GET", url, params=params)
        return response

    async def _authenticate_with_cookies(self, cookies: Dict[str, str]) -> Dict[str, Any]:
        """
        使用Cookie认证（备用方案）

        Args:
            cookies: Cookie字典

        Returns:
            用户信息
        """
        # 这是一个简化的实现，实际需要根据微博的页面结构进行适配
        url = "https://m.weibo.cn/api/config"

        cookie_header = "; ".join([f"{k}={v}" for k, v in cookies.items()])
        headers = {
            "Cookie": cookie_header,
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15",
        }

        response = await self._make_request("GET", url, headers=headers)

        if "data" in response and "login" in response["data"]:
            return response["data"]
        else:
            raise InvalidCredentialsError(platform=self.platform.value, message="Cookie认证失败")

    def _validate_format_impl(self, content: TransformedContent) -> ValidationResult:
        """
        验证内容格式是否符合微博要求

        Args:
            content: 转换后的内容

        Returns:
            验证结果
        """
        errors = []

        # 微博内容长度限制
        text_content = content.text or content.content
        char_count = len(text_content)

        if char_count == 0:
            errors.append(
                ValidationError(
                    field="content",
                    message="内容不能为空",
                    value=text_content,
                    error_code="EMPTY_CONTENT",
                )
            )
        elif char_count > 2000:  # 微博长文限制
            errors.append(
                ValidationError(
                    field="content",
                    message="内容长度不能超过2000字符",
                    value=char_count,
                    error_code="CONTENT_TOO_LONG",
                )
            )

        # 检查图片数量（微博最多9张）
        if content.images and len(content.images) > 9:
            errors.append(
                ValidationError(
                    field="images",
                    message="图片数量不能超过9张",
                    value=len(content.images),
                    error_code="TOO_MANY_IMAGES",
                )
            )

        # 检查敏感内容
        sensitive_patterns = [
            r"(微信|QQ|电话)\s*[:：]?\s*[\d\-\s]+",  # 联系方式
            r"(加群|加我|私信)",  # 引流词汇
        ]

        for pattern in sensitive_patterns:
            if re.search(pattern, text_content, re.IGNORECASE):
                errors.append(
                    ValidationError(
                        field="content",
                        message=f"内容包含可能的违规信息: {pattern}",
                        value=pattern,
                        error_code="SENSITIVE_CONTENT",
                    )
                )

        return ValidationResult(is_valid=len(errors) == 0, errors=errors)

    def _split_long_content(self, content: str, max_length: int = 140) -> List[str]:
        """
        分割长内容为多条微博

        Args:
            content: 原始内容
            max_length: 每条微博的最大长度

        Returns:
            分割后的内容列表
        """
        if len(content) <= max_length:
            return [content]

        parts = []
        sentences = content.split("。")  # 按句号分割
        current_part = ""

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # 如果当前句子本身就超长，需要强制分割
            if len(sentence) > max_length - 10:  # 留出序号空间
                if current_part:
                    parts.append(current_part.rstrip("。") + "。")
                    current_part = ""

                # 强制按字符分割
                while len(sentence) > max_length - 10:
                    parts.append(sentence[: max_length - 10])
                    sentence = sentence[max_length - 10 :]

                if sentence:
                    current_part = sentence + "。"
            else:
                # 检查是否可以加入当前部分
                test_content = current_part + sentence + "。"
                if len(test_content) <= max_length - 10:
                    current_part = test_content
                else:
                    if current_part:
                        parts.append(current_part.rstrip("。") + "。")
                    current_part = sentence + "。"

        if current_part:
            parts.append(current_part.rstrip("。") + "。")

        # 添加序号
        if len(parts) > 1:
            numbered_parts = []
            for i, part in enumerate(parts, 1):
                numbered_parts.append(f"({i}/{len(parts)}) {part}")
            return numbered_parts

        return parts

    def _convert_content_to_weibo_format(self, content: TransformedContent) -> str:
        """
        将内容转换为微博格式

        Args:
            content: 转换后的内容

        Returns:
            微博格式的内容
        """
        # 使用纯文本内容
        text = content.text or content.content

        # 转换Markdown链接为简单格式
        text = re.sub(r"\[([^\]]+)\]\(([^)]+)\)", r"\1 \2", text)

        # 移除多余的空行
        text = re.sub(r"\n{3,}", "\n\n", text)

        # 添加话题标签
        if content.tags:
            hashtags = " ".join([f"#{tag}#" for tag in content.tags[:3]])  # 最多3个话题
            text = f"{text}\n\n{hashtags}"

        return text.strip()

    async def _upload_images(self, image_urls: List[str]) -> List[str]:
        """
        上传图片到微博

        Args:
            image_urls: 图片URL列表

        Returns:
            微博图片ID列表
        """
        upload_url = f"{self.base_url}/statuses/upload_pic.json"
        uploaded_pics = []

        for image_url in image_urls[:9]:  # 最多9张
            try:
                # 这里需要实现图片下载和上传逻辑
                # 简化实现，实际需要下载图片并以multipart形式上传
                pic_data = {"access_token": self._auth_data.get("access_token")}

                # 模拟上传结果
                pic_info = {
                    "pic_id": f"pic_{int(time.time())}",
                    "thumbnail_pic": image_url,
                    "bmiddle_pic": image_url,
                    "original_pic": image_url,
                }
                uploaded_pics.append(pic_info["pic_id"])

            except Exception as e:
                # 图片上传失败时跳过
                continue

        return uploaded_pics

    async def _publish_impl(
        self, content: TransformedContent, options: Dict[str, Any]
    ) -> PublishResult:
        """
        具体的发布实现

        Args:
            content: 转换后的内容
            options: 发布选项

        Returns:
            发布结果
        """
        try:
            # 转换内容格式
            weibo_text = self._convert_content_to_weibo_format(content)

            # 检查是否需要分割长内容
            use_long_text = options.get("use_long_text", False)
            if not use_long_text and len(weibo_text) > 140:
                # 分割为多条微博
                text_parts = self._split_long_content(weibo_text, 140)
                return await self._publish_thread(text_parts, content, options)

            # 处理图片上传
            pic_ids = []
            if content.images:
                pic_ids = await self._upload_images(content.images)

            # 构建发布请求
            publish_data = {
                "status": weibo_text,
                "access_token": self._auth_data.get("access_token"),
            }

            if pic_ids:
                publish_data["pic_id"] = ",".join(pic_ids)

            # 发布API选择
            if use_long_text:
                url = f"{self.base_url}/statuses/share.json"
            else:
                url = f"{self.base_url}/statuses/update.json"

            response = await self._make_request(
                method="POST",
                url=url,
                data=publish_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"},
            )

            # 解析响应
            weibo_id = response.get("id") or response.get("idstr")

            if not weibo_id:
                raise PlatformAPIError(
                    platform=self.platform.value,
                    api_error="发布响应中缺少微博ID",
                    context={"response": response},
                )

            return PublishResult(
                success=True,
                platform=self.platform,
                platform_post_id=str(weibo_id),
                platform_url=f"https://weibo.com/{self._auth_data.get('user_info', {}).get('id', '')}/{weibo_id}",
                message="发布成功",
                publish_time=datetime.now(),
                response_data=response,
            )

        except Exception as e:
            return PublishResult(
                success=False,
                platform=self.platform,
                error_message=f"发布失败: {str(e)}",
                error_details={
                    "exception": type(e).__name__,
                    "content_title": content.title if hasattr(content, "title") else "Unknown",
                },
            )

    async def _publish_thread(
        self, text_parts: List[str], content: TransformedContent, options: Dict[str, Any]
    ) -> PublishResult:
        """
        发布微博串

        Args:
            text_parts: 分割的文本列表
            content: 原始内容
            options: 发布选项

        Returns:
            发布结果
        """
        published_ids = []

        try:
            for i, text_part in enumerate(text_parts):
                publish_data = {
                    "status": text_part,
                    "access_token": self._auth_data.get("access_token"),
                }

                url = f"{self.base_url}/statuses/update.json"
                response = await self._make_request(
                    method="POST",
                    url=url,
                    data=publish_data,
                    headers={"Content-Type": "application/x-www-form-urlencoded"},
                )

                weibo_id = response.get("id") or response.get("idstr")
                if weibo_id:
                    published_ids.append(str(weibo_id))

                # 发布间隔，避免频率限制
                if i < len(text_parts) - 1:
                    await asyncio.sleep(2)

            return PublishResult(
                success=True,
                platform=self.platform,
                platform_post_id=",".join(published_ids),
                platform_url=f"https://weibo.com/{self._auth_data.get('user_info', {}).get('id', '')}/{published_ids[0]}",
                message=f"微博串发布成功，共{len(published_ids)}条",
                publish_time=datetime.now(),
                response_data={"thread_ids": published_ids},
            )

        except Exception as e:
            # 如果部分发布成功，记录已发布的
            if published_ids:
                return PublishResult(
                    success=False,
                    platform=self.platform,
                    platform_post_id=",".join(published_ids),
                    error_message=f"微博串部分发布成功（{len(published_ids)}/{len(text_parts)}），错误: {str(e)}",
                    error_details={"partial_success": True, "published_count": len(published_ids)},
                )
            else:
                return PublishResult(
                    success=False,
                    platform=self.platform,
                    error_message=f"微博串发布失败: {str(e)}",
                    error_details={"exception": type(e).__name__},
                )

    async def _get_publish_status_impl(self, platform_post_id: str) -> Dict[str, Any]:
        """
        获取发布状态

        Args:
            platform_post_id: 平台微博ID

        Returns:
            状态信息
        """
        # 处理微博串的情况
        if "," in platform_post_id:
            post_ids = platform_post_id.split(",")
            main_id = post_ids[0]
        else:
            main_id = platform_post_id

        url = f"{self.base_url}/statuses/show.json"
        params = {"id": main_id, "access_token": self._auth_data.get("access_token")}

        response = await self._make_request("GET", url, params=params)

        return {
            "status": "published",
            "repost_count": response.get("reposts_count", 0),
            "comment_count": response.get("comments_count", 0),
            "like_count": response.get("attitudes_count", 0),
            "publish_time": response.get("created_at"),
            "url": f"https://weibo.com/{response.get('user', {}).get('id', '')}/{main_id}",
            "raw_response": response,
        }

    async def delete_weibo(self, weibo_id: str) -> bool:
        """
        删除微博

        Args:
            weibo_id: 微博ID

        Returns:
            是否删除成功
        """
        url = f"{self.base_url}/statuses/destroy.json"
        data = {"id": weibo_id, "access_token": self._auth_data.get("access_token")}

        try:
            response = await self._make_request("POST", url, data=data)
            return response.get("id") is not None
        except Exception:
            return False
