"""
知乎平台适配器

本模块实现了知乎平台的内容发布功能，包括：
1. OAuth 2.0认证流程
2. 知乎API封装
3. 内容格式转换（Markdown到HTML）
4. 频率控制机制

知乎API文档参考：
- https://www.zhihu.com/api/v4/docs
- https://developer.zhihu.com/
"""

import asyncio
import html
import json
import hashlib
import base64
import secrets
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from urllib.parse import urlencode, parse_qs, urlparse
import re

from .base import BaseAdapter
from ..models import (
    Platform,
    TransformedContent,
    PublishResult,
    AuthResult,
    ValidationResult,
    ValidationError,
    ContentFormat,
)
from ..utils import PlatformAPIError, InvalidCredentialsError, RateLimitError


class ZhihuAdapter(BaseAdapter):
    """知乎平台适配器"""

    def __init__(self, **kwargs):
        super().__init__(
            timeout=30,
            max_retries=3,
            retry_delay=2.0,
            rate_limit_calls=100,  # 知乎API限制：100次/小时
            rate_limit_period=3600,
            **kwargs,
        )
        self._client_id: Optional[str] = None
        self._client_secret: Optional[str] = None
        self._redirect_uri: Optional[str] = None

    @property
    def platform(self) -> Platform:
        """返回平台标识"""
        return Platform.ZHIHU

    @property
    def base_url(self) -> str:
        """返回知乎API基础URL"""
        return "https://www.zhihu.com/api/v4"

    @property
    def oauth_base_url(self) -> str:
        """返回OAuth基础URL"""
        return "https://www.zhihu.com/oauth"

    @property
    def required_credentials(self) -> List[str]:
        """返回必需的认证字段"""
        return ["client_id", "client_secret", "redirect_uri"]

    def _get_auth_headers(self) -> Dict[str, str]:
        """获取认证头"""
        headers = {}
        if self._auth_data and self._auth_data.get("access_token"):
            headers["Authorization"] = f"Bearer {self._auth_data['access_token']}"
        return headers

    def _validate_credentials(self, credentials: Dict[str, Any]) -> ValidationResult:
        """验证认证凭证格式"""
        errors = []

        # 检查必需字段
        for field in self.required_credentials:
            if field not in credentials:
                errors.append(
                    ValidationError(field=field, message=f"缺少必需字段: {field}", value=None)
                )
            elif not credentials[field]:
                errors.append(
                    ValidationError(
                        field=field, message=f"字段不能为空: {field}", value=credentials[field]
                    )
                )

        # 验证client_id格式
        if "client_id" in credentials and credentials["client_id"]:
            client_id = credentials["client_id"]
            if not re.match(r"^[a-f0-9]{32}$", client_id):
                errors.append(
                    ValidationError(
                        field="client_id",
                        message="client_id格式不正确，应为32位十六进制字符",
                        value=client_id,
                    )
                )

        # 验证redirect_uri格式
        if "redirect_uri" in credentials and credentials["redirect_uri"]:
            redirect_uri = credentials["redirect_uri"]
            parsed = urlparse(redirect_uri)
            if not parsed.scheme or not parsed.netloc:
                errors.append(
                    ValidationError(
                        field="redirect_uri",
                        message="redirect_uri格式不正确，必须是有效的URL",
                        value=redirect_uri,
                    )
                )

        return ValidationResult(is_valid=len(errors) == 0, errors=errors)

    def generate_auth_url(self, credentials: Dict[str, Any], state: Optional[str] = None) -> str:
        """
        生成OAuth认证URL

        Args:
            credentials: 认证凭证
            state: 状态参数，用于防CSRF攻击

        Returns:
            认证URL
        """
        if not state:
            state = secrets.token_urlsafe(32)

        params = {
            "response_type": "code",
            "client_id": credentials["client_id"],
            "redirect_uri": credentials["redirect_uri"],
            "scope": "read_user write_article",  # 读取用户信息、发布文章权限
            "state": state,
        }

        return f"{self.oauth_base_url}/authorize?{urlencode(params)}"

    async def exchange_code_for_token(
        self, credentials: Dict[str, Any], auth_code: str
    ) -> Dict[str, Any]:
        """
        使用授权码换取访问令牌

        Args:
            credentials: 认证凭证
            auth_code: 授权码

        Returns:
            令牌信息

        Raises:
            PlatformAPIError: API调用失败
        """
        token_url = f"{self.oauth_base_url}/access_token"

        data = {
            "grant_type": "authorization_code",
            "code": auth_code,
            "client_id": credentials["client_id"],
            "client_secret": credentials["client_secret"],
            "redirect_uri": credentials["redirect_uri"],
        }

        response = await self._make_request(
            method="POST",
            url=token_url,
            data=data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        )

        if "access_token" not in response:
            raise PlatformAPIError(
                platform=self.platform.value,
                api_error="获取访问令牌失败",
                context={"response": response},
            )

        return response

    async def refresh_token(
        self, refresh_token: str, credentials: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        刷新访问令牌

        Args:
            refresh_token: 刷新令牌
            credentials: 认证凭证

        Returns:
            新的令牌信息
        """
        token_url = f"{self.oauth_base_url}/access_token"

        data = {
            "grant_type": "refresh_token",
            "refresh_token": refresh_token,
            "client_id": credentials["client_id"],
            "client_secret": credentials["client_secret"],
        }

        response = await self._make_request(
            method="POST",
            url=token_url,
            data=data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        )

        return response

    async def _authenticate_impl(self, credentials: Dict[str, Any]) -> AuthResult:
        """
        具体的认证实现

        Args:
            credentials: 认证凭证，可能包含access_token或需要OAuth流程

        Returns:
            认证结果
        """
        try:
            # 保存认证配置
            self._client_id = credentials["client_id"]
            self._client_secret = credentials["client_secret"]
            self._redirect_uri = credentials["redirect_uri"]

            # 如果已有访问令牌，直接验证
            if "access_token" in credentials:
                access_token = credentials["access_token"]

                # 验证令牌有效性
                user_info = await self._get_user_info(access_token)

                auth_data = {
                    "access_token": access_token,
                    "refresh_token": credentials.get("refresh_token"),
                    "user_info": user_info,
                    "expires_at": credentials.get("expires_at"),
                }

                return AuthResult(
                    success=True, platform=self.platform, auth_data=auth_data, message="认证成功"
                )

            # 需要OAuth流程
            auth_url = self.generate_auth_url(credentials)

            return AuthResult(
                success=False,
                platform=self.platform,
                message="需要完成OAuth认证",
                auth_url=auth_url,
            )

        except Exception as e:
            return AuthResult(
                success=False,
                platform=self.platform,
                error_message=f"认证失败: {str(e)}",
                error_details={"exception": type(e).__name__},
            )

    async def _get_user_info(self, access_token: str) -> Dict[str, Any]:
        """
        获取用户信息

        Args:
            access_token: 访问令牌

        Returns:
            用户信息
        """
        url = f"{self.base_url}/me"

        # 临时设置token用于请求
        old_auth_data = self._auth_data
        self._auth_data = {"access_token": access_token}

        try:
            response = await self._make_request("GET", url)
            return response
        finally:
            self._auth_data = old_auth_data

    def _validate_format_impl(self, content: TransformedContent) -> ValidationResult:
        """
        验证内容格式是否符合知乎要求

        Args:
            content: 转换后的内容

        Returns:
            验证结果
        """
        errors = []

        # 标题验证
        if not content.title or len(content.title.strip()) == 0:
            errors.append(
                ValidationError(field="title", message="标题不能为空", value=content.title)
            )
        elif len(content.title) > 80:
            errors.append(
                ValidationError(
                    field="title", message="标题长度不能超过80字符", value=len(content.title)
                )
            )

        # 内容验证
        if not content.content or len(content.content.strip()) == 0:
            errors.append(
                ValidationError(field="content", message="内容不能为空", value=content.content)
            )
        elif len(content.content) > 300000:  # 知乎文章字数限制
            errors.append(
                ValidationError(
                    field="content",
                    message="内容长度不能超过300000字符",
                    value=len(content.content),
                )
            )

        # HTML内容验证
        if not content.html or len(content.html.strip()) == 0:
            errors.append(
                ValidationError(field="html", message="HTML格式内容不能为空", value=content.html)
            )

        # 检查敏感词汇（示例）
        sensitive_words = ["广告", "推广", "违法"]  # 实际应该从配置文件加载
        for word in sensitive_words:
            if word in content.title or word in content.content:
                errors.append(
                    ValidationError(
                        field="content", message=f"内容包含敏感词汇: {word}", value=word
                    )
                )

        return ValidationResult(is_valid=len(errors) == 0, errors=errors)

    def _convert_markdown_to_zhihu_format(self, content: TransformedContent) -> str:
        """
        将Markdown转换为知乎支持的格式

        Args:
            content: 转换后的内容

        Returns:
            知乎格式的内容
        """
        html_content = content.html

        # 知乎支持的HTML标签有限，需要进行转换
        # 1. 转换标题
        html_content = re.sub(r"<h([1-6])>(.*?)</h[1-6]>", r"<p><b>\2</b></p>", html_content)

        # 2. 转换代码块
        html_content = re.sub(
            r'<pre><code(?:\s+class="([^"]*)")?>(.*?)</code></pre>',
            r"<pre>\2</pre>",
            html_content,
            flags=re.DOTALL,
        )

        # 3. 转换行内代码
        html_content = re.sub(r"<code>(.*?)</code>", r"<tt>\1</tt>", html_content)

        # 4. 转换引用
        html_content = re.sub(
            r"<blockquote>(.*?)</blockquote>", r"<i>\1</i>", html_content, flags=re.DOTALL
        )

        # 5. 处理图片（知乎需要先上传图片）
        images = re.findall(r'<img[^>]+src=["\']([^"\'>]+)["\'][^>]*>', html_content)
        for img_url in images:
            # 这里应该调用图片上传API，然后替换URL
            # 目前先保持原样
            pass

        # 6. 清理不支持的标签
        allowed_tags = [
            "p",
            "br",
            "b",
            "i",
            "u",
            "strong",
            "em",
            "ul",
            "ol",
            "li",
            "a",
            "img",
            "pre",
            "tt",
        ]
        # 这里应该实现HTML标签清理逻辑

        return html_content

    async def _upload_image(self, image_url: str) -> str:
        """
        上传图片到知乎

        Args:
            image_url: 图片URL或本地路径

        Returns:
            知乎图片URL
        """
        # 这里需要实现图片上传逻辑
        # 知乎的图片上传API需要先获取上传凭证
        upload_url = f"{self.base_url}/images"

        # 简化实现，实际需要处理图片下载、格式转换等
        return image_url

    async def _publish_impl(
        self, content: TransformedContent, options: Dict[str, Any]
    ) -> PublishResult:
        """
        具体的发布实现

        Args:
            content: 转换后的内容
            options: 发布选项

        Returns:
            发布结果
        """
        try:
            # 转换内容格式
            zhihu_content = self._convert_markdown_to_zhihu_format(content)

            # 构建发布请求
            publish_data = {
                "title": content.title,
                "content": zhihu_content,
                "content_type": "article",  # 文章类型
                "excerpt": options.get("excerpt", content.title),  # 摘要
                "topics": options.get("topics", []),  # 话题
                "is_original": options.get("is_original", True),  # 是否原创
                "column_id": options.get("column_id"),  # 专栏ID（可选）
            }

            # 发布文章
            url = f"{self.base_url}/articles"
            response = await self._make_request(method="POST", url=url, json_data=publish_data)

            # 解析响应
            article_id = response.get("id")
            article_url = response.get("url")

            if not article_id:
                raise PlatformAPIError(
                    platform=self.platform.value,
                    api_error="发布响应中缺少文章ID",
                    context={"response": response},
                )

            return PublishResult(
                success=True,
                platform=self.platform,
                platform_post_id=str(article_id),
                platform_url=article_url,
                message="发布成功",
                publish_time=datetime.now(),
                response_data=response,
            )

        except Exception as e:
            return PublishResult(
                success=False,
                platform=self.platform,
                error_message=f"发布失败: {str(e)}",
                error_details={"exception": type(e).__name__, "content_title": content.title},
            )

    async def _get_publish_status_impl(self, platform_post_id: str) -> Dict[str, Any]:
        """
        获取发布状态

        Args:
            platform_post_id: 平台文章ID

        Returns:
            状态信息
        """
        url = f"{self.base_url}/articles/{platform_post_id}"

        response = await self._make_request("GET", url)

        return {
            "status": "published" if response.get("state") == "published" else "draft",
            "view_count": response.get("voteup_count", 0),
            "comment_count": response.get("comment_count", 0),
            "like_count": response.get("voteup_count", 0),
            "publish_time": response.get("created_time"),
            "update_time": response.get("updated_time"),
            "url": response.get("url"),
            "raw_response": response,
        }

    async def get_article_analytics(self, article_id: str) -> Dict[str, Any]:
        """
        获取文章分析数据

        Args:
            article_id: 文章ID

        Returns:
            分析数据
        """
        url = f"{self.base_url}/articles/{article_id}/analytics"

        try:
            response = await self._make_request("GET", url)
            return response
        except Exception as e:
            return {"error": str(e), "analytics_available": False}

    async def get_user_columns(self) -> List[Dict[str, Any]]:
        """
        获取用户的专栏列表

        Returns:
            专栏列表
        """
        if not self._auth_data or not self._auth_data.get("user_info"):
            return []

        user_id = self._auth_data["user_info"].get("id")
        if not user_id:
            return []

        url = f"{self.base_url}/people/{user_id}/columns"

        try:
            response = await self._make_request("GET", url)
            return response.get("data", [])
        except Exception:
            return []
