"""
TextUp 平台适配器模块

本模块包含了各个平台的适配器实现，基于适配器模式设计。
每个平台适配器都继承自BaseAdapter基类，实现统一的发布接口。

支持的平台：
- 知乎 (<PERSON><PERSON><PERSON>) - 支持API和Playwright两种方式
- 微博 (Weibo)
- 小红书 (Xiaohongshu) - 开发中
- 今日头条 (Toutiao)

适配器类型：
- API适配器：基于官方API实现（如果可用）
- Playwright适配器：基于Web自动化实现，适用于无公开API的平台
"""

from .base import BaseAdapter
from .zhihu import ZhihuAdapter
from .zhihu_playwright import ZhihuPlaywrightAdapter
from .weibo import WeiboAdapter
from .toutiao import ToutiaoAdapter
from .factory import (
    AdapterFactory,
    AdapterType,
    create_adapter,
    get_recommended_adapter_type,
)

__all__ = [
    "BaseAdapter",
    "ZhihuAdapter",
    "ZhihuPlaywrightAdapter",
    "WeiboAdapter",
    "ToutiaoAdapter",
    "AdapterFactory",
    "AdapterType",
    "create_adapter",
    "get_recommended_adapter_type",
]
