"""
知乎平台Playwright适配器

本模块使用Playwright实现知乎平台的自动化发布功能，包括：
1. Web自动化登录
2. 内容发布
3. 反检测机制
4. 会话管理

注意：由于知乎没有公开的发布API，本适配器使用Web自动化技术实现发布功能。
使用时请遵守知乎平台规则，避免过度频繁操作导致账号风险。
"""

import asyncio
import json
import random
import time
import hashlib
import re
from pathlib import Path
from typing import Dict, Any, Optional, List

try:
    from playwright.async_api import async_playwright, Browser, BrowserContext, Page, Playwright
    HAS_PLAYWRIGHT = True
except ImportError:
    HAS_PLAYWRIGHT = False
    async_playwright = None
    Browser = None
    BrowserContext = None
    Page = None
    Playwright = None

try:
    from bs4 import BeautifulSoup
    HAS_BS4 = True
except ImportError:
    HAS_BS4 = False
    BeautifulSoup = None

try:
    import markdown2
    HAS_MARKDOWN2 = True
except ImportError:
    HAS_MARKDOWN2 = False
    markdown2 = None

from .base import BaseAdapter
from ..models import (
    Platform,
    TransformedContent,
    PublishResult,
    AuthResult,
    ValidationResult,
    ValidationError,
    ContentFormat,
)
from ..utils import TextUpError


class BrowserManager:
    """浏览器管理器，包含反检测机制"""

    def __init__(self, headless: bool = True, debug: bool = False):
        self.headless = headless
        self.debug = debug
        self.playwright: Optional[Playwright] = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None

    async def init_browser(self) -> Page:
        """初始化浏览器实例"""
        if not HAS_PLAYWRIGHT:
            raise TextUpError("Playwright未安装，请运行: pip install playwright")

        self.playwright = await async_playwright().start()

        # 反检测启动参数
        args = [
            '--disable-blink-features=AutomationControlled',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu',
            '--hide-scrollbars',
            '--mute-audio',
            '--no-default-browser-check',
            '--disable-background-timer-throttling',
            '--disable-renderer-backgrounding',
            '--disable-backgrounding-occluded-windows',
        ]

        self.browser = await self.playwright.chromium.launch(
            headless=self.headless,
            args=args,
            slow_mo=50 if self.debug else 0,
        )

        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent=self._get_random_user_agent(),
            java_script_enabled=True,
            ignore_https_errors=True,
        )

        # 注入反检测脚本
        if self.context:
            await self._inject_stealth_scripts()

        self.page = await self.context.new_page()

        if self.debug:
            self.page.on("console", lambda msg: print(f"Console: {msg.text}"))

        return self.page

    def _get_random_user_agent(self) -> str:
        """获取随机User-Agent"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        ]
        return random.choice(user_agents)

    async def _inject_stealth_scripts(self):
        """注入反检测脚本"""
        stealth_script = """
        // 移除webdriver属性
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });

        // 修改chrome属性
        window.chrome = {
            runtime: {},
            loadTimes: function() {},
            csi: function() {},
            app: {}
        };

        // 修改插件信息
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5]
        });

        // 修改语言
        Object.defineProperty(navigator, 'languages', {
            get: () => ['zh-CN', 'zh', 'en-US', 'en']
        });
        """
        await self.context.add_init_script(stealth_script)

    async def close(self):
        """关闭浏览器"""
        if self.page:
            await self.page.close()
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()


class AuthManager:
    """认证管理器"""

    def __init__(self, page: Page, credentials_dir: str = "credentials"):
        self.page = page
        self.credentials_dir = Path(credentials_dir)
        self.credentials_dir.mkdir(exist_ok=True)

    async def login(self, username: str, password: str) -> bool:
        """执行登录流程"""
        try:
            # 检查是否已有有效session
            if await self._load_session(username):
                if await self._verify_login_status():
                    return True

            # 执行登录流程
            await self.page.goto("https://www.zhihu.com/signin")
            await self.page.wait_for_load_state("networkidle")

            # 选择密码登录
            password_tab = self.page.locator("div.SignFlow-tab:has-text('密码登录')")
            if await password_tab.count() > 0:
                await password_tab.click()
                await asyncio.sleep(1)

            # 输入用户名
            username_input = self.page.locator('input[name="username"]')
            await username_input.fill(username)
            await self._human_delay()

            # 输入密码
            password_input = self.page.locator('input[name="password"]')
            await password_input.fill(password)
            await self._human_delay()

            # 点击登录按钮
            login_button = self.page.locator('button.SignFlow-submitButton')
            await login_button.click()

            # 等待登录结果
            await asyncio.sleep(3)

            # 处理验证码等
            await self._handle_verification()

            # 验证登录状态
            if await self._verify_login_status():
                await self._save_session(username)
                return True

            return False

        except Exception as e:
            print(f"登录失败: {e}")
            return False

    async def _handle_verification(self):
        """处理各种验证"""
        # 检查验证码
        captcha = self.page.locator("img.Captcha-englishImage")
        if await captcha.count() > 0:
            print("需要验证码，请手动处理")
            await asyncio.sleep(10)  # 等待手动处理

        # 检查手机验证
        phone_verify = self.page.locator("text*=手机验证")
        if await phone_verify.count() > 0:
            print("需要手机验证，请手动处理")
            await asyncio.sleep(30)  # 等待手动处理

    async def _verify_login_status(self) -> bool:
        """验证登录状态"""
        try:
            await self.page.goto("https://www.zhihu.com/")
            await self.page.wait_for_load_state("networkidle")

            # 检查用户头像
            user_avatar = self.page.locator("button.AppHeader-profileButton")
            return await user_avatar.count() > 0

        except Exception:
            return False

    async def _save_session(self, username: str):
        """保存会话"""
        try:
            cookies = await self.page.context.cookies()
            session_data = {
                'cookies': cookies,
                'timestamp': int(time.time()),
                'username': username
            }

            session_file = self.credentials_dir / f"{self._hash_username(username)}_session.json"
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2)
        except Exception as e:
            print(f"保存会话失败: {e}")

    async def _load_session(self, username: str) -> bool:
        """加载会话"""
        try:
            session_file = self.credentials_dir / f"{self._hash_username(username)}_session.json"
            if not session_file.exists():
                return False

            with open(session_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)

            # 检查是否过期（7天）
            if time.time() - session_data.get('timestamp', 0) > 7 * 24 * 3600:
                return False

            await self.page.context.add_cookies(session_data['cookies'])
            return True

        except Exception:
            return False

    def _hash_username(self, username: str) -> str:
        """哈希用户名"""
        return hashlib.md5(username.encode()).hexdigest()

    async def _human_delay(self):
        """人性化延迟"""
        await asyncio.sleep(random.uniform(0.5, 2.0))


class ContentPublisher:
    """内容发布器"""

    def __init__(self, page: Page):
        self.page = page

    async def publish_article(self, content_data: Dict[str, Any]) -> Dict[str, Any]:
        """发布文章"""
        try:
            # 导航到编辑器
            if not await self._navigate_to_editor():
                return {"success": False, "error": "无法访问编辑器"}

            # 填写标题
            if not await self._fill_title(content_data.get("title", "")):
                return {"success": False, "error": "标题填写失败"}

            # 填写内容
            if not await self._fill_content(content_data.get("content", "")):
                return {"success": False, "error": "内容填写失败"}

            # 添加标签
            if content_data.get("tags"):
                await self._add_tags(content_data["tags"])

            # 执行发布
            return await self._execute_publish()

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _navigate_to_editor(self) -> bool:
        """导航到编辑器"""
        try:
            await self.page.goto("https://zhuanlan.zhihu.com/write")
            await self.page.wait_for_load_state("networkidle")

            # 等待编辑器加载
            editor = self.page.locator("div.WriteIndex")
            await editor.wait_for(timeout=10000)
            return True

        except Exception:
            return False

    async def _fill_title(self, title: str) -> bool:
        """填写标题"""
        try:
            title_selectors = [
                "textarea.WriteIndex-title",
                "input[placeholder*='标题']",
                "textarea[placeholder*='标题']"
            ]

            for selector in title_selectors:
                title_input = self.page.locator(selector)
                if await title_input.count() > 0:
                    await title_input.click()
                    await title_input.fill("")

                    # 模拟打字
                    for char in title:
                        await title_input.type(char)
                        await asyncio.sleep(random.uniform(0.05, 0.15))

                    return True

            return False

        except Exception:
            return False

    async def _fill_content(self, content: str) -> bool:
        """填写内容"""
        try:
            # 转换Markdown到HTML
            html_content = self._markdown_to_html(content)

            editor_selectors = [
                "div[data-contents='true']",
                "div.DraftEditor-root .DraftEditor-editorContainer"
            ]

            for selector in editor_selectors:
                editor = self.page.locator(selector)
                if await editor.count() > 0:
                    await editor.click()

                    # 使用JavaScript设置内容
                    success = await self.page.evaluate("""
                        (args) => {
                            const [selector, content] = args;
                            const editor = document.querySelector(selector);
                            if (editor) {
                                editor.innerHTML = content;
                                const event = new Event('input', { bubbles: true });
                                editor.dispatchEvent(event);
                                return true;
                            }
                            return false;
                        }
                    """, [selector, html_content])

                    if success:
                        await asyncio.sleep(1)
                        return True

            return False

        except Exception:
            return False

    def _markdown_to_html(self, markdown_text: str) -> str:
        """Markdown转HTML"""
        try:
            if not HAS_MARKDOWN2:
                return markdown_text.replace('\n', '<br>')

            html = markdown2.markdown(markdown_text, extras=[
                'code-friendly',
                'fenced-code-blocks',
                'tables',
                'break-on-newline'
            ])

            # 优化HTML格式
            if HAS_BS4:
                soup = BeautifulSoup(html, 'html.parser')

                # 处理代码块
                for pre in soup.find_all('pre'):
                    pre['class'] = 'highlight'

                # 处理引用
                for blockquote in soup.find_all('blockquote'):
                    blockquote['class'] = 'RichText-blockquote'

                return str(soup)

            return html

        except Exception:
            return markdown_text

    async def _add_tags(self, tags: List[str]):
        """添加标签"""
        try:
            for tag in tags[:5]:  # 最多5个标签
                tag_input = self.page.locator("input[placeholder*='话题']")
                if await tag_input.count() > 0:
                    await tag_input.click()
                    await tag_input.fill(tag)
                    await asyncio.sleep(1)

                    # 选择第一个建议
                    suggestion = self.page.locator("div.TopicSuggest-item").first
                    if await suggestion.count() > 0:
                        await suggestion.click()
                    else:
                        await tag_input.press("Enter")

                    await asyncio.sleep(0.5)
        except Exception:
            pass

    async def _execute_publish(self) -> Dict[str, Any]:
        """执行发布"""
        try:
            publish_selectors = [
                "button.Button--primary:has-text('发布')",
                "button:has-text('发布文章')"
            ]

            for selector in publish_selectors:
                button = self.page.locator(selector)
                if await button.count() > 0 and await button.is_enabled():
                    await button.click()
                    await asyncio.sleep(3)

                    # 检查发布结果
                    if await self.page.locator("text*=发布成功").count() > 0:
                        article_url = await self._get_article_url()
                        return {
                            "success": True,
                            "url": article_url,
                            "message": "发布成功"
                        }

            return {"success": False, "error": "发布按钮不可用"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _get_article_url(self) -> Optional[str]:
        """获取文章URL"""
        try:
            current_url = self.page.url
            if "/p/" in current_url:
                return current_url

            # 等待重定向
            await asyncio.sleep(2)
            return self.page.url if "/p/" in self.page.url else None

        except Exception:
            return None


class AntiDetectionSystem:
    """反检测系统"""

    def __init__(self, page: Page):
        self.page = page

    async def simulate_human_behavior(self):
        """模拟人类行为"""
        # 随机鼠标移动
        await self._random_mouse_movement()

        # 随机滚动
        await self._random_scrolling()

        # 随机停留
        await self._random_pause()

    async def _random_mouse_movement(self):
        """随机鼠标移动"""
        try:
            # 获取页面尺寸
            viewport = await self.page.evaluate("() => ({ width: window.innerWidth, height: window.innerHeight })")

            # 生成随机坐标
            x = random.randint(100, viewport['width'] - 100)
            y = random.randint(100, viewport['height'] - 100)

            # 移动鼠标
            await self.page.mouse.move(x, y)
            await asyncio.sleep(random.uniform(0.1, 0.5))

        except Exception:
            pass

    async def _random_scrolling(self):
        """随机滚动"""
        try:
            # 随机滚动距离
            scroll_distance = random.randint(-300, 300)

            await self.page.evaluate(f"window.scrollBy(0, {scroll_distance})")
            await asyncio.sleep(random.uniform(0.5, 1.5))

        except Exception:
            pass

    async def _random_pause(self):
        """随机停留"""
        await asyncio.sleep(random.uniform(1.0, 3.0))

    async def human_like_delay(self, min_delay: float = 0.5, max_delay: float = 2.0):
        """人性化延迟"""
        delay = random.uniform(min_delay, max_delay)
        await asyncio.sleep(delay)

    async def random_action_sequence(self):
        """随机动作序列"""
        actions = [
            self._random_mouse_movement,
            self._random_scrolling,
            self._random_pause
        ]

        # 随机执行1-3个动作
        num_actions = random.randint(1, 3)
        selected_actions = random.sample(actions, num_actions)

        for action in selected_actions:
            await action()


class ZhihuPlaywrightAdapter(BaseAdapter):
    """知乎Playwright适配器"""

    def __init__(self, **kwargs):
        super().__init__(
            timeout=60,  # 增加超时时间
            max_retries=2,
            retry_delay=5.0,
            rate_limit_calls=10,  # 严格限制频率
            rate_limit_period=3600,
            **kwargs,
        )

        self.browser_manager: Optional[BrowserManager] = None
        self.auth_manager: Optional[AuthManager] = None
        self.content_publisher: Optional[ContentPublisher] = None
        self.anti_detection: Optional[AntiDetectionSystem] = None
        self._page: Optional[Page] = None

    @property
    def platform(self) -> Platform:
        """返回平台标识"""
        return Platform.ZHIHU

    @property
    def base_url(self) -> str:
        """返回知乎基础URL"""
        return "https://www.zhihu.com"

    @property
    def required_credentials(self) -> List[str]:
        """返回必需的认证字段"""
        return ["username", "password"]

    def _get_auth_headers(self) -> Dict[str, str]:
        """获取认证头（Playwright不需要）"""
        return {}

    def _validate_credentials(self, credentials: Dict[str, Any]) -> ValidationResult:
        """验证认证凭证"""
        errors = []

        for field in self.required_credentials:
            if field not in credentials or not credentials[field]:
                errors.append(
                    ValidationError(
                        field=field,
                        message=f"缺少必需字段: {field}",
                        value=credentials.get(field),
                        error_code="missing_field"
                    )
                )

        # 验证用户名格式
        username = credentials.get("username", "")
        if username and not (re.match(r'^[\w\.-]+@[\w\.-]+\.\w+$', username) or
                           re.match(r'^1[3-9]\d{9}$', username)):
            errors.append(
                ValidationError(
                    field="username",
                    message="用户名格式不正确，应为邮箱或手机号",
                    value=username,
                    error_code="invalid_format"
                )
            )

        return ValidationResult(is_valid=len(errors) == 0, errors=errors)

    async def _authenticate_impl(self, credentials: Dict[str, Any]) -> AuthResult:
        """认证实现"""
        try:
            # 检查Playwright可用性
            if not HAS_PLAYWRIGHT:
                return AuthResult(
                    success=False,
                    platform=self.platform,
                    user_id=None,
                    username=None,
                    auth_data=None,
                    expires_at=None,
                    auth_url=None,
                    error_message="Playwright未安装，请运行: pip install playwright && playwright install chromium",
                    error_details={"missing_dependency": "playwright"}
                )

            # 初始化浏览器
            self.browser_manager = BrowserManager(
                headless=credentials.get("headless", True),
                debug=credentials.get("debug", False)
            )

            self._page = await self.browser_manager.init_browser()

            # 初始化认证管理器
            self.auth_manager = AuthManager(self._page)

            # 执行登录
            success = await self.auth_manager.login(
                credentials["username"],
                credentials["password"]
            )

            if success:
                self.content_publisher = ContentPublisher(self._page)
                self.anti_detection = AntiDetectionSystem(self._page)
                return AuthResult(
                    success=True,
                    platform=self.platform,
                    user_id=credentials["username"],
                    username=credentials["username"],
                    auth_data={"username": credentials["username"]},
                    expires_at=None,
                    auth_url=None,
                    error_message=None,
                    error_details=None
                )
            else:
                return AuthResult(
                    success=False,
                    platform=self.platform,
                    user_id=None,
                    username=None,
                    auth_data=None,
                    expires_at=None,
                    auth_url=None,
                    error_message="登录失败，请检查用户名和密码",
                    error_details=None
                )

        except Exception as e:
            return AuthResult(
                success=False,
                platform=self.platform,
                user_id=None,
                username=None,
                auth_data=None,
                expires_at=None,
                auth_url=None,
                error_message=f"认证过程发生错误: {str(e)}",
                error_details=None
            )

    def _validate_format_impl(self, content: TransformedContent) -> ValidationResult:
        """验证内容格式"""
        errors = []

        # 检查标题
        if not content.title or len(content.title.strip()) == 0:
            errors.append(
                ValidationError(
                    field="title",
                    message="标题不能为空",
                    value=content.title,
                    error_code="empty_title"
                )
            )
        elif len(content.title) > 100:
            errors.append(
                ValidationError(
                    field="title",
                    message="标题长度不能超过100个字符",
                    value=len(content.title),
                    error_code="title_too_long"
                )
            )

        # 检查内容
        if not content.content or len(content.content.strip()) == 0:
            errors.append(
                ValidationError(
                    field="content",
                    message="内容不能为空",
                    value=content.content,
                    error_code="empty_content"
                )
            )
        elif len(content.content) < 20:
            errors.append(
                ValidationError(
                    field="content",
                    message="内容长度不能少于20个字符",
                    value=len(content.content),
                    error_code="content_too_short"
                )
            )

        # 检查标签
        if hasattr(content, 'tags') and content.tags:
            if len(content.tags) > 5:
                errors.append(
                    ValidationError(
                        field="tags",
                        message="标签数量不能超过5个",
                        value=len(content.tags),
                        error_code="too_many_tags"
                    )
                )

        return ValidationResult(is_valid=len(errors) == 0, errors=errors)

    async def _publish_impl(self, content: TransformedContent, options: Dict[str, Any]) -> PublishResult:
        """发布实现"""
        try:
            if not self.content_publisher:
                return PublishResult(
                    success=False,
                    platform=self.platform,
                    platform_post_id=None,
                    publish_url=None,
                    error_message="未初始化内容发布器，请先完成认证",
                    error_details=None,
                    published_at=None
                )

            # 准备发布数据
            publish_data = {
                "title": content.title,
                "content": content.content,
                "tags": getattr(content, 'tags', []),
                "format": getattr(content, 'content_format', ContentFormat.MARKDOWN)
            }

            # 执行反检测行为
            if self.anti_detection:
                await self.anti_detection.simulate_human_behavior()

            # 执行发布
            result = await self.content_publisher.publish_article(publish_data)

            if result.get("success"):
                return PublishResult(
                    success=True,
                    platform=self.platform,
                    platform_post_id=self._extract_post_id(result.get("url")),
                    publish_url=result.get("url"),
                    error_message=None,
                    error_details=None,
                    published_at=None
                )
            else:
                return PublishResult(
                    success=False,
                    platform=self.platform,
                    platform_post_id=None,
                    publish_url=None,
                    error_message=result.get("error", "发布失败"),
                    error_details=None,
                    published_at=None
                )

        except Exception as e:
            return PublishResult(
                success=False,
                platform=self.platform,
                platform_post_id=None,
                publish_url=None,
                error_message=f"发布过程发生错误: {str(e)}",
                error_details=None,
                published_at=None
            )

    async def _get_publish_status_impl(self, platform_post_id: str) -> Dict[str, Any]:
        """获取发布状态"""
        try:
            if not self._page:
                return {"status": "unknown", "message": "浏览器未初始化"}

            # 构造文章URL
            article_url = f"https://zhuanlan.zhihu.com/p/{platform_post_id}"

            # 访问文章页面
            await self._page.goto(article_url)
            await self._page.wait_for_load_state("networkidle")

            # 检查文章是否存在
            if await self._page.locator("h1.Post-Title").count() > 0:
                return {
                    "status": "published",
                    "url": article_url,
                    "message": "文章正常可访问"
                }
            else:
                return {
                    "status": "not_found",
                    "message": "文章不存在或已删除"
                }

        except Exception as e:
            return {
                "status": "error",
                "message": f"查询状态失败: {str(e)}"
            }

    def _extract_post_id(self, url: Optional[str]) -> Optional[str]:
        """从URL提取文章ID"""
        if not url:
            return None

        match = re.search(r'/p/(\d+)', url)
        return match.group(1) if match else None

    async def close(self):
        """关闭适配器"""
        if self.browser_manager:
            await self.browser_manager.close()

    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
