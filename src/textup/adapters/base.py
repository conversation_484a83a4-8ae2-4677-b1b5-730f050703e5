"""
TextUp 平台适配器基类

本模块定义了平台适配器的基础抽象类，提供通用的实现逻辑和标准化接口。
所有具体的平台适配器都应该继承自BaseAdapter类。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
import asyncio
import aiohttp
import time
from datetime import datetime, timedelta

from ..models import (
    Platform,
    TransformedContent,
    PublishResult,
    AuthResult,
    ValidationResult,
    ValidationError,
    PublishStatus,
)
from ..utils import (
    PlatformAdapterProtocol,
    TextUpError,
    PlatformAPIError,
    RateLimitError,
    InvalidCredentialsError,
    TimeoutError,
    handle_exception,
    create_error_from_http_status,
)


class BaseAdapter(PlatformAdapterProtocol, ABC):
    """平台适配器基类"""

    def __init__(
        self,
        timeout: int = 30,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        rate_limit_calls: int = 100,
        rate_limit_period: int = 3600,
    ):
        """
        初始化适配器

        Args:
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
            rate_limit_calls: 频率限制调用次数
            rate_limit_period: 频率限制时间周期（秒）
        """
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.rate_limit_calls = rate_limit_calls
        self.rate_limit_period = rate_limit_period

        # 请求计数器（用于频率控制）
        self._request_times: List[datetime] = []
        self._session: Optional[aiohttp.ClientSession] = None
        self._auth_data: Optional[Dict[str, Any]] = None

    @property
    @abstractmethod
    def platform(self) -> Platform:
        """返回平台标识"""
        pass

    @property
    @abstractmethod
    def base_url(self) -> str:
        """返回平台API基础URL"""
        pass

    @property
    @abstractmethod
    def required_credentials(self) -> List[str]:
        """返回必需的认证字段"""
        pass

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self._close_session()

    async def _ensure_session(self):
        """确保HTTP会话存在"""
        if self._session is None or self._session.closed:
            connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self._session = aiohttp.ClientSession(
                connector=connector, timeout=timeout, headers=self._get_default_headers()
            )

    async def _close_session(self):
        """关闭HTTP会话"""
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None

    def _get_default_headers(self) -> Dict[str, str]:
        """获取默认请求头"""
        return {
            "User-Agent": "TextUp/1.0.0",
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

    async def _check_rate_limit(self):
        """检查频率限制"""
        now = datetime.now()
        cutoff = now - timedelta(seconds=self.rate_limit_period)

        # 清理过期的请求记录
        self._request_times = [t for t in self._request_times if t > cutoff]

        # 检查是否超过频率限制
        if len(self._request_times) >= self.rate_limit_calls:
            oldest_request = min(self._request_times)
            retry_after = int(
                (oldest_request + timedelta(seconds=self.rate_limit_period) - now).total_seconds()
            )
            raise RateLimitError(
                platform=self.platform.value,
                retry_after=retry_after,
                context={"current_requests": len(self._request_times)},
            )

        # 记录当前请求时间
        self._request_times.append(now)

    async def _make_request(
        self,
        method: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """
        发起HTTP请求

        Args:
            method: HTTP方法
            url: 请求URL
            headers: 请求头
            json_data: JSON数据
            data: 表单数据
            params: URL参数

        Returns:
            响应数据字典

        Raises:
            TextUpError: 请求失败时抛出
        """
        await self._ensure_session()
        await self._check_rate_limit()

        # 合并请求头
        request_headers = self._get_default_headers()
        if self._auth_data:
            auth_headers = self._get_auth_headers()
            request_headers.update(auth_headers)
        if headers:
            request_headers.update(headers)

        last_exception = None

        # 重试机制
        for attempt in range(self.max_retries + 1):
            try:
                async with self._session.request(
                    method=method,
                    url=url,
                    headers=request_headers,
                    json=json_data,
                    data=data,
                    params=params,
                ) as response:

                    response_text = await response.text()

                    # 处理HTTP错误状态码
                    if response.status >= 400:
                        error = create_error_from_http_status(
                            response.status,
                            response_text,
                            self.platform.value,
                            context={"method": method, "url": url, "attempt": attempt + 1},
                        )

                        # 对于某些错误，不进行重试
                        if isinstance(error, (InvalidCredentialsError, RateLimitError)):
                            raise error

                        last_exception = error

                        # 最后一次尝试，直接抛出异常
                        if attempt == self.max_retries:
                            raise error

                        # 等待后重试
                        await asyncio.sleep(self.retry_delay * (2**attempt))
                        continue

                    # 解析响应
                    try:
                        if response.headers.get("content-type", "").startswith("application/json"):
                            return await response.json()
                        else:
                            return {"text": response_text}
                    except Exception as e:
                        raise PlatformAPIError(
                            platform=self.platform.value,
                            api_error=f"无法解析响应: {str(e)}",
                            context={"response_text": response_text[:500]},
                        )

            except aiohttp.ClientError as e:
                last_exception = TimeoutError(
                    operation=f"{method} {url}", timeout=self.timeout, cause=e
                )

                if attempt == self.max_retries:
                    raise last_exception

                await asyncio.sleep(self.retry_delay * (2**attempt))

        # 理论上不应该到达这里
        raise last_exception or TextUpError("请求失败，原因未知")

    @abstractmethod
    def _get_auth_headers(self) -> Dict[str, str]:
        """获取认证头"""
        pass

    @abstractmethod
    def _validate_credentials(self, credentials: Dict[str, Any]) -> ValidationResult:
        """验证认证凭证"""
        pass

    async def authenticate(self, credentials: Dict[str, Any]) -> AuthResult:
        """
        平台认证

        Args:
            credentials: 认证凭证

        Returns:
            认证结果
        """
        try:
            # 验证凭证格式
            validation = self._validate_credentials(credentials)
            if not validation.is_valid:
                return AuthResult(
                    success=False,
                    platform=self.platform,
                    error_message=f"凭证验证失败: {'; '.join([e.message for e in validation.errors])}",
                )

            # 执行具体的认证逻辑
            auth_result = await self._authenticate_impl(credentials)

            if auth_result.success:
                self._auth_data = auth_result.auth_data

            return auth_result

        except Exception as e:
            return AuthResult(
                success=False,
                platform=self.platform,
                error_message=str(e),
                error_details={"exception": type(e).__name__},
            )

    @abstractmethod
    async def _authenticate_impl(self, credentials: Dict[str, Any]) -> AuthResult:
        """具体的认证实现"""
        pass

    async def validate_format(self, content: TransformedContent) -> ValidationResult:
        """
        验证内容格式是否符合平台要求

        Args:
            content: 转换后的内容

        Returns:
            验证结果
        """
        try:
            return self._validate_format_impl(content)
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                errors=[
                    ValidationError(
                        field="format",
                        message=f"格式验证异常: {str(e)}",
                        value=(
                            content.content_format if hasattr(content, "content_format") else None
                        ),
                    )
                ],
            )

    @abstractmethod
    def _validate_format_impl(self, content: TransformedContent) -> ValidationResult:
        """具体的格式验证实现"""
        pass

    async def publish(self, content: TransformedContent, options: Dict[str, Any]) -> PublishResult:
        """
        发布内容到平台

        Args:
            content: 转换后的内容
            options: 发布选项

        Returns:
            发布结果
        """
        try:
            # 验证认证状态
            if not self._auth_data:
                return PublishResult(
                    success=False, platform=self.platform, error_message="未认证，请先完成平台认证"
                )

            # 验证内容格式
            validation = await self.validate_format(content)
            if not validation.is_valid:
                return PublishResult(
                    success=False,
                    platform=self.platform,
                    error_message=f"内容格式验证失败: {'; '.join([e.message for e in validation.errors])}",
                )

            # 执行具体的发布逻辑
            return await self._publish_impl(content, options)

        except Exception as e:
            return PublishResult(
                success=False,
                platform=self.platform,
                error_message=str(e),
                error_details={
                    "exception": type(e).__name__,
                    "content_title": content.title if hasattr(content, "title") else None,
                },
            )

    @abstractmethod
    async def _publish_impl(
        self, content: TransformedContent, options: Dict[str, Any]
    ) -> PublishResult:
        """具体的发布实现"""
        pass

    async def get_publish_status(self, platform_post_id: str) -> Dict[str, Any]:
        """
        获取发布状态

        Args:
            platform_post_id: 平台文章ID

        Returns:
            状态信息字典
        """
        try:
            return await self._get_publish_status_impl(platform_post_id)
        except Exception as e:
            handle_exception("get_publish_status", e, {"platform_post_id": platform_post_id})

    @abstractmethod
    async def _get_publish_status_impl(self, platform_post_id: str) -> Dict[str, Any]:
        """具体的状态查询实现"""
        pass

    def __del__(self):
        """析构函数，确保会话关闭"""
        if hasattr(self, "_session") and self._session and not self._session.closed:
            # 注意：这里不能使用await，只能提醒用户正确关闭
            import warnings

            warnings.warn(
                f"{self.__class__.__name__} 实例被销毁但HTTP会话未正确关闭，请使用 async with 语法"
            )
