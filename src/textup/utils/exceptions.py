"""
TextUp 异常处理模块

本模块定义了TextUp项目的异常层次结构，提供了细粒度的错误分类和处理机制。
异常设计遵循Python异常处理最佳实践，支持错误码、错误级别和上下文信息。
"""

from typing import Optional, Dict, Any, List
from enum import Enum
import traceback
from datetime import datetime


class ErrorSeverity(str, Enum):
    """错误严重级别"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(str, Enum):
    """错误分类"""

    NETWORK = "network"
    AUTHENTICATION = "authentication"
    VALIDATION = "validation"
    CONFIGURATION = "configuration"
    PLATFORM = "platform"
    CONTENT = "content"
    DATABASE = "database"
    PERMISSION = "permission"
    RATE_LIMIT = "rate_limit"
    SYSTEM = "system"
    UNKNOWN = "unknown"


class TextUpError(Exception):
    """TextUp基础异常类"""

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        category: ErrorCategory = ErrorCategory.UNKNOWN,
        context: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.severity = severity
        self.category = category
        self.context = context or {}
        self.cause = cause
        self.timestamp = datetime.now()
        self.traceback_info = traceback.format_exc() if cause else None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "severity": self.severity.value,
            "category": self.category.value,
            "context": self.context,
            "timestamp": self.timestamp.isoformat(),
            "traceback": self.traceback_info,
        }

    def __str__(self) -> str:
        return f"[{self.error_code}] {self.message}"

    def __repr__(self) -> str:
        return (
            f"{self.__class__.__name__}("
            f"message='{self.message}', "
            f"error_code='{self.error_code}', "
            f"severity={self.severity}, "
            f"category={self.category})"
        )


# =============================================================================
# 网络相关异常
# =============================================================================


class NetworkError(TextUpError):
    """网络异常基类"""

    def __init__(self, message: str, **kwargs):
        super().__init__(
            message, category=ErrorCategory.NETWORK, severity=ErrorSeverity.HIGH, **kwargs
        )


class ConnectionError(NetworkError):
    """连接异常"""

    def __init__(self, url: str, timeout: Optional[int] = None, **kwargs):
        context = {"url": url, "timeout": timeout}
        context.update(kwargs.get("context", {}))
        super().__init__(
            f"连接失败: {url}", error_code="NETWORK_CONNECTION_FAILED", context=context, **kwargs
        )


class TimeoutError(NetworkError):
    """超时异常"""

    def __init__(self, operation: str, timeout: int, **kwargs):
        context = {"operation": operation, "timeout": timeout}
        context.update(kwargs.get("context", {}))
        super().__init__(
            f"操作超时: {operation} (超时时间: {timeout}秒)",
            error_code="NETWORK_TIMEOUT",
            context=context,
            **kwargs,
        )


class RateLimitError(NetworkError):
    """频率限制异常"""

    def __init__(self, platform: str, retry_after: Optional[int] = None, **kwargs):
        context = {"platform": platform, "retry_after": retry_after}
        context.update(kwargs.get("context", {}))
        # Remove context and category from kwargs to avoid duplicate arguments
        filtered_kwargs = {k: v for k, v in kwargs.items() if k not in ("context", "category")}
        super().__init__(
            f"请求频率过高: {platform}" + (f", 请在{retry_after}秒后重试" if retry_after else ""),
            error_code="RATE_LIMIT_EXCEEDED",
            category=ErrorCategory.RATE_LIMIT,
            context=context,
            **filtered_kwargs,
        )


# =============================================================================
# 认证相关异常
# =============================================================================


class AuthenticationError(TextUpError):
    """认证异常基类"""

    def __init__(self, message: str, **kwargs):
        super().__init__(
            message, category=ErrorCategory.AUTHENTICATION, severity=ErrorSeverity.HIGH, **kwargs
        )


class InvalidCredentialsError(AuthenticationError):
    """无效凭证异常"""

    def __init__(self, platform: str, **kwargs):
        context = {"platform": platform}
        context.update(kwargs.get("context", {}))
        super().__init__(
            f"无效的认证凭证: {platform}",
            error_code="AUTH_INVALID_CREDENTIALS",
            context=context,
            **kwargs,
        )


class TokenExpiredError(AuthenticationError):
    """令牌过期异常"""

    def __init__(self, platform: str, expires_at: Optional[datetime] = None, **kwargs):
        context = {
            "platform": platform,
            "expires_at": expires_at.isoformat() if expires_at else None,
        }
        context.update(kwargs.get("context", {}))
        super().__init__(
            f"访问令牌已过期: {platform}",
            error_code="AUTH_TOKEN_EXPIRED",
            context=context,
            **kwargs,
        )


class PermissionDeniedError(AuthenticationError):
    """权限拒绝异常"""

    def __init__(self, platform: str, required_permission: str, **kwargs):
        context = {"platform": platform, "required_permission": required_permission}
        context.update(kwargs.get("context", {}))
        super().__init__(
            f"权限不足: {platform}, 需要权限: {required_permission}",
            error_code="AUTH_PERMISSION_DENIED",
            category=ErrorCategory.PERMISSION,
            context=context,
            **kwargs,
        )


# =============================================================================
# 验证相关异常
# =============================================================================


class ValidationError(TextUpError):
    """通用验证异常"""

    def __init__(self, field: str, message: str, value: Any = None, **kwargs):
        context = {"field": field, "value": value}
        context.update(kwargs.get("context", {}))
        super().__init__(
            f"验证失败: {field}, {message}",
            error_code="VALIDATION_FAILED",
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.MEDIUM,
            context=context,
            **kwargs,
        )


# =============================================================================
# 内容相关异常
# =============================================================================


class ContentError(TextUpError):
    """内容异常基类"""

    def __init__(self, message: str, **kwargs):
        super().__init__(
            message, category=ErrorCategory.CONTENT, severity=ErrorSeverity.MEDIUM, **kwargs
        )


class ContentValidationError(ContentError):
    """内容验证异常"""

    def __init__(self, content_type: str, errors: List[str], **kwargs):
        context = {"content_type": content_type, "validation_errors": errors}
        context.update(kwargs.get("context", {}))
        super().__init__(
            f"内容验证失败: {content_type}, 错误: {'; '.join(errors)}",
            error_code="CONTENT_VALIDATION_FAILED",
            category=ErrorCategory.VALIDATION,
            context=context,
            **kwargs,
        )


class ContentFormatError(ContentError):
    """内容格式异常"""

    def __init__(self, source_format: str, target_format: str, **kwargs):
        context = {"source_format": source_format, "target_format": target_format}
        context.update(kwargs.get("context", {}))
        super().__init__(
            f"内容格式转换失败: {source_format} -> {target_format}",
            error_code="CONTENT_FORMAT_ERROR",
            context=context,
            **kwargs,
        )


class ContentSizeError(ContentError):
    """内容大小异常"""

    def __init__(self, size: int, max_size: int, **kwargs):
        context = {"size": size, "max_size": max_size}
        context.update(kwargs.get("context", {}))
        super().__init__(
            f"内容大小超出限制: {size} bytes > {max_size} bytes",
            error_code="CONTENT_SIZE_EXCEEDED",
            context=context,
            **kwargs,
        )


# =============================================================================
# 平台相关异常
# =============================================================================


class PlatformError(TextUpError):
    """平台异常基类"""

    def __init__(self, message: str, platform: str, **kwargs):
        context = {"platform": platform}
        context.update(kwargs.get("context", {}))
        # Remove context from kwargs to avoid duplicate argument
        filtered_kwargs = {k: v for k, v in kwargs.items() if k != "context"}
        super().__init__(
            message, category=ErrorCategory.PLATFORM, context=context, **filtered_kwargs
        )


class PlatformUnavailableError(PlatformError):
    """平台不可用异常"""

    def __init__(self, platform: str, **kwargs):
        super().__init__(
            f"平台服务不可用: {platform}",
            platform=platform,
            error_code="PLATFORM_UNAVAILABLE",
            severity=ErrorSeverity.HIGH,
            **kwargs,
        )


class PlatformAPIError(PlatformError):
    """平台API异常"""

    def __init__(self, platform: str, api_error: str, status_code: Optional[int] = None, **kwargs):
        context = {"api_error": api_error, "status_code": status_code}
        context.update(kwargs.get("context", {}))
        # Remove context from kwargs to avoid duplicate argument
        filtered_kwargs = {k: v for k, v in kwargs.items() if k != "context"}
        super().__init__(
            f"平台API错误: {platform}, {api_error}",
            platform=platform,
            error_code="PLATFORM_API_ERROR",
            context=context,
            **filtered_kwargs,
        )


class UnsupportedPlatformError(PlatformError):
    """不支持的平台异常"""

    def __init__(self, platform: str, **kwargs):
        super().__init__(
            f"不支持的平台: {platform}",
            platform=platform,
            error_code="PLATFORM_UNSUPPORTED",
            severity=ErrorSeverity.MEDIUM,
            **kwargs,
        )


# =============================================================================
# 发布相关异常
# =============================================================================


class PublishError(TextUpError):
    """发布异常基类"""

    def __init__(self, message: str, platform: Optional[str] = None, **kwargs):
        context = {"platform": platform} if platform else {}
        context.update(kwargs.get("context", {}))
        super().__init__(
            message,
            category=ErrorCategory.PLATFORM,
            severity=ErrorSeverity.HIGH,
            context=context,
            **kwargs,
        )


class PublishFailedError(PublishError):
    """发布失败异常"""

    def __init__(self, platform: str, reason: str, **kwargs):
        super().__init__(
            f"发布失败: {platform}, 原因: {reason}",
            platform=platform,
            error_code="PUBLISH_FAILED",
            **kwargs,
        )


class PublishTimeoutError(PublishError):
    """发布超时异常"""

    def __init__(self, platform: str, timeout: int, **kwargs):
        super().__init__(
            f"发布超时: {platform}, 超时时间: {timeout}秒",
            platform=platform,
            error_code="PUBLISH_TIMEOUT",
            **kwargs,
        )


# =============================================================================
# 配置相关异常
# =============================================================================


class ConfigurationError(TextUpError):
    """配置异常基类"""

    def __init__(self, message: str, **kwargs):
        super().__init__(
            message, category=ErrorCategory.CONFIGURATION, severity=ErrorSeverity.HIGH, **kwargs
        )


class ConfigNotFoundError(ConfigurationError):
    """配置未找到异常"""

    def __init__(self, config_path: str, **kwargs):
        context = {"config_path": config_path}
        context.update(kwargs.get("context", {}))
        super().__init__(
            f"配置文件未找到: {config_path}",
            error_code="CONFIG_NOT_FOUND",
            context=context,
            **kwargs,
        )


class ConfigValidationError(ConfigurationError):
    """配置验证异常"""

    def __init__(self, config_key: str, validation_error: str, **kwargs):
        context = {"config_key": config_key, "validation_error": validation_error}
        context.update(kwargs.get("context", {}))
        super().__init__(
            f"配置验证失败: {config_key}, 错误: {validation_error}",
            error_code="CONFIG_VALIDATION_FAILED",
            category=ErrorCategory.VALIDATION,
            context=context,
            **kwargs,
        )


# =============================================================================
# 数据库相关异常
# =============================================================================


class DatabaseError(TextUpError):
    """数据库异常基类"""

    def __init__(self, message: str, **kwargs):
        super().__init__(
            message, category=ErrorCategory.DATABASE, severity=ErrorSeverity.HIGH, **kwargs
        )


class DatabaseConnectionError(DatabaseError):
    """数据库连接异常"""

    def __init__(self, database_url: str, **kwargs):
        context = {"database_url": database_url}
        context.update(kwargs.get("context", {}))
        super().__init__(
            f"数据库连接失败: {database_url}",
            error_code="DATABASE_CONNECTION_FAILED",
            context=context,
            **kwargs,
        )


class RecordNotFoundError(DatabaseError):
    """记录未找到异常"""

    def __init__(self, record_type: str, record_id: str, **kwargs):
        context = {"record_type": record_type, "record_id": record_id}
        context.update(kwargs.get("context", {}))
        super().__init__(
            f"记录未找到: {record_type}#{record_id}",
            error_code="RECORD_NOT_FOUND",
            severity=ErrorSeverity.MEDIUM,
            context=context,
            **kwargs,
        )


# =============================================================================
# 系统相关异常
# =============================================================================


class SystemError(TextUpError):
    """系统异常基类"""

    def __init__(self, message: str, **kwargs):
        super().__init__(
            message, category=ErrorCategory.SYSTEM, severity=ErrorSeverity.CRITICAL, **kwargs
        )


class FileNotFoundError(SystemError):
    """文件未找到异常"""

    def __init__(self, file_path: str, **kwargs):
        context = {"file_path": file_path}
        context.update(kwargs.get("context", {}))
        super().__init__(
            f"文件未找到: {file_path}",
            error_code="FILE_NOT_FOUND",
            severity=ErrorSeverity.MEDIUM,
            context=context,
            **kwargs,
        )


class InsufficientStorageError(SystemError):
    """存储空间不足异常"""

    def __init__(self, required_space: int, available_space: int, **kwargs):
        context = {"required_space": required_space, "available_space": available_space}
        context.update(kwargs.get("context", {}))
        super().__init__(
            f"存储空间不足: 需要 {required_space} bytes, 可用 {available_space} bytes",
            error_code="INSUFFICIENT_STORAGE",
            context=context,
            **kwargs,
        )


# =============================================================================
# 异常处理工具函数
# =============================================================================


def handle_exception(
    func_name: str,
    exception: Exception,
    context: Optional[Dict[str, Any]] = None,
    reraise: bool = True,
) -> Optional[TextUpError]:
    """统一异常处理函数"""

    # 如果已经是TextUpError，直接返回
    if isinstance(exception, TextUpError):
        if context:
            exception.context.update(context)
        if reraise:
            raise exception
        return exception

    # 转换为TextUpError
    textup_error = TextUpError(
        message=f"在 {func_name} 中发生未处理异常: {str(exception)}",
        error_code="UNHANDLED_EXCEPTION",
        severity=ErrorSeverity.HIGH,
        category=ErrorCategory.SYSTEM,
        context=context or {},
        cause=exception,
    )

    if reraise:
        raise textup_error
    return textup_error


def create_error_from_http_status(
    status_code: int, response_text: str, platform: str, context: Optional[Dict[str, Any]] = None
) -> TextUpError:
    """根据HTTP状态码创建对应异常"""

    context = context or {}
    context.update({"status_code": status_code, "response_text": response_text})

    if status_code == 401:
        return InvalidCredentialsError(platform, context=context)
    elif status_code == 403:
        return PermissionDeniedError(platform, "unknown", context=context)
    elif status_code == 429:
        return RateLimitError(platform, context=context)
    elif status_code >= 500:
        return PlatformUnavailableError(platform, context=context)
    else:
        return PlatformAPIError(platform, f"HTTP {status_code}", status_code, context=context)
