"""
TextUp 核心接口定义

本模块定义了TextUp项目的核心接口协议，使用Python的Protocol实现适配器模式。
这些接口确保了各个组件之间的松耦合，便于扩展和测试。
"""

from typing import Protocol, Dict, Any, List, Optional, Union
from abc import abstractmethod
import asyncio
from datetime import datetime

from ..models import (
    Content,
    TransformedContent,
    PublishTask,
    PublishRecord,
    PublishResult,
    AuthResult,
    ValidationResult,
    Platform,
    PlatformConfig,
)


class ContentManagerProtocol(Protocol):
    """内容管理器接口协议"""

    @abstractmethod
    async def parse_content(self, file_path: str) -> Content:
        """解析文件内容"""
        ...

    @abstractmethod
    async def validate_content(self, content: Content) -> ValidationResult:
        """验证内容格式"""
        ...

    @abstractmethod
    async def transform_content(self, content: Content, target_format: str) -> TransformedContent:
        """转换内容格式"""
        ...


class PlatformAdapterProtocol(Protocol):
    """平台适配器接口协议"""

    @property
    @abstractmethod
    def platform(self) -> Platform:
        """返回平台标识"""
        ...

    @abstractmethod
    async def authenticate(self, credentials: Dict[str, Any]) -> AuthResult:
        """平台认证"""
        ...

    @abstractmethod
    async def validate_format(self, content: TransformedContent) -> ValidationResult:
        """验证内容格式是否符合平台要求"""
        ...

    @abstractmethod
    async def publish(self, content: TransformedContent, options: Dict[str, Any]) -> PublishResult:
        """发布内容到平台"""
        ...

    @abstractmethod
    async def get_publish_status(self, platform_post_id: str) -> Dict[str, Any]:
        """获取发布状态"""
        ...


class PublishEngineProtocol(Protocol):
    """发布引擎接口协议"""

    @abstractmethod
    async def execute_task(self, task: PublishTask) -> List[PublishRecord]:
        """执行发布任务"""
        ...

    @abstractmethod
    async def execute_parallel(self, tasks: List[PublishTask]) -> List[List[PublishRecord]]:
        """并行执行多个发布任务"""
        ...

    @abstractmethod
    async def schedule_task(self, task: PublishTask, scheduled_time: datetime) -> str:
        """调度任务"""
        ...


class ConfigManagerProtocol(Protocol):
    """配置管理器接口协议"""

    @abstractmethod
    async def load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """加载配置"""
        ...

    @abstractmethod
    async def save_config(self, config: Dict[str, Any], config_path: Optional[str] = None) -> bool:
        """保存配置"""
        ...

    @abstractmethod
    async def get_platform_config(self, platform: Platform) -> Optional[PlatformConfig]:
        """获取平台配置"""
        ...

    @abstractmethod
    async def set_platform_config(self, platform_config: PlatformConfig) -> bool:
        """设置平台配置"""
        ...


class CredentialsManagerProtocol(Protocol):
    """凭证管理器接口协议"""

    @abstractmethod
    async def encrypt_credentials(self, credentials: Dict[str, Any]) -> str:
        """加密凭证"""
        ...

    @abstractmethod
    async def decrypt_credentials(self, encrypted_credentials: str) -> Dict[str, Any]:
        """解密凭证"""
        ...

    @abstractmethod
    async def store_credentials(self, platform: Platform, credentials: Dict[str, Any]) -> bool:
        """存储凭证"""
        ...

    @abstractmethod
    async def get_credentials(self, platform: Platform) -> Optional[Dict[str, Any]]:
        """获取凭证"""
        ...


class DatabaseManagerProtocol(Protocol):
    """数据库管理器接口协议"""

    @abstractmethod
    async def save_content(self, content: Content) -> str:
        """保存内容"""
        ...

    @abstractmethod
    async def get_content(self, content_id: str) -> Optional[Content]:
        """获取内容"""
        ...

    @abstractmethod
    async def save_task(self, task: PublishTask) -> str:
        """保存任务"""
        ...

    @abstractmethod
    async def get_task(self, task_id: str) -> Optional[PublishTask]: ...

    @abstractmethod
    async def save_record(self, record: PublishRecord) -> str:
        """保存发布记录"""
        ...

    @abstractmethod
    async def get_records(
        self, task_id: Optional[str] = None, platform: Optional[Platform] = None
    ) -> List[PublishRecord]:
        """获取发布记录"""
        ...


class AdapterFactoryProtocol(Protocol):
    """适配器工厂接口协议"""

    @abstractmethod
    def create_adapter(self, platform: Platform) -> PlatformAdapterProtocol:
        """创建平台适配器"""
        ...

    @abstractmethod
    def register_adapter(self, platform: Platform, adapter_class: type) -> None:
        """注册适配器"""
        ...

    @abstractmethod
    def get_supported_platforms(self) -> List[Platform]:
        """获取支持的平台列表"""
        ...


class LoggerProtocol(Protocol):
    """日志记录器接口协议"""

    @abstractmethod
    def debug(self, message: str, **kwargs) -> None:
        """调试日志"""
        ...

    @abstractmethod
    def info(self, message: str, **kwargs) -> None:
        """信息日志"""
        ...

    @abstractmethod
    def warning(self, message: str, **kwargs) -> None:
        """警告日志"""
        ...

    @abstractmethod
    def error(self, message: str, **kwargs) -> None:
        """错误日志"""
        ...

    @abstractmethod
    def critical(self, message: str, **kwargs) -> None:
        """严重错误日志"""
        ...


# 工厂方法协议
class ServiceFactoryProtocol(Protocol):
    """服务工厂接口协议"""

    @abstractmethod
    def create_content_manager(self) -> ContentManagerProtocol:
        """创建内容管理器"""
        ...

    @abstractmethod
    def create_publish_engine(self) -> PublishEngineProtocol:
        """创建发布引擎"""
        ...

    @abstractmethod
    def create_config_manager(self) -> ConfigManagerProtocol:
        """创建配置管理器"""
        ...

    @abstractmethod
    def create_credentials_manager(self) -> CredentialsManagerProtocol:
        """创建凭证管理器"""
        ...

    @abstractmethod
    def create_database_manager(self) -> DatabaseManagerProtocol:
        """创建数据库管理器"""
        ...


# 命令模式协议
class CommandProtocol(Protocol):
    """命令接口协议"""

    @abstractmethod
    async def execute(self, *args, **kwargs) -> Any:
        """执行命令"""
        ...

    @abstractmethod
    async def undo(self) -> Any:
        """撤销命令"""
        ...


class CommandInvokerProtocol(Protocol):
    """命令调用器接口协议"""

    @abstractmethod
    async def execute_command(self, command: CommandProtocol) -> Any:
        """执行命令"""
        ...

    @abstractmethod
    async def undo_last_command(self) -> Any:
        """撤销最后一个命令"""
        ...

    @abstractmethod
    def get_command_history(self) -> List[CommandProtocol]:
        """获取命令历史"""
        ...
