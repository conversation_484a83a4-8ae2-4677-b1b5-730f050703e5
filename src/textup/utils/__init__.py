"""
TextUp 工具模块

本模块包含了TextUp项目的核心工具类和实用函数，包括:
- 核心接口协议定义 (interfaces)
- 异常处理类 (exceptions)
- 工具函数和辅助类
"""

# 核心接口协议
from .interfaces import (
    ContentManagerProtocol,
    PlatformAdapterProtocol,
    PublishEngineProtocol,
    ConfigManagerProtocol,
    CredentialsManagerProtocol,
    DatabaseManagerProtocol,
    AdapterFactoryProtocol,
    LoggerProtocol,
    ServiceFactoryProtocol,
    CommandProtocol,
    CommandInvokerProtocol,
)

# 异常处理
from .exceptions import (
    # 基础异常
    TextUpError,
    ErrorSeverity,
    ErrorCategory,
    ValidationError,
    # 网络异常
    NetworkError,
    ConnectionError,
    TimeoutError,
    RateLimitError,
    # 认证异常
    AuthenticationError,
    InvalidCredentialsError,
    TokenExpiredError,
    PermissionDeniedError,
    # 内容异常
    ContentError,
    ContentValidationError,
    ContentFormatError,
    ContentSizeError,
    # 平台异常
    PlatformError,
    PlatformUnavailableError,
    PlatformAPIError,
    UnsupportedPlatformError,
    # 配置异常
    ConfigurationError,
    ConfigNotFoundError,
    ConfigValidationError,
    # 数据库异常
    DatabaseError,
    DatabaseConnectionError,
    RecordNotFoundError,
    # 系统异常
    SystemError,
    FileNotFoundError,
    InsufficientStorageError,
    # 工具函数
    handle_exception,
    create_error_from_http_status,
)

__all__ = [
    # 接口协议
    "ContentManagerProtocol",
    "PlatformAdapterProtocol",
    "PublishEngineProtocol",
    "ConfigManagerProtocol",
    "CredentialsManagerProtocol",
    "DatabaseManagerProtocol",
    "AdapterFactoryProtocol",
    "LoggerProtocol",
    "ServiceFactoryProtocol",
    "CommandProtocol",
    "CommandInvokerProtocol",
    # 异常类
    "TextUpError",
    "ErrorSeverity",
    "ErrorCategory",
    "ValidationError",
    "NetworkError",
    "ConnectionError",
    "TimeoutError",
    "RateLimitError",
    "AuthenticationError",
    "InvalidCredentialsError",
    "TokenExpiredError",
    "PermissionDeniedError",
    "ContentError",
    "ContentValidationError",
    "ContentFormatError",
    "ContentSizeError",
    "PlatformError",
    "PlatformUnavailableError",
    "PlatformAPIError",
    "UnsupportedPlatformError",
    "ConfigurationError",
    "ConfigNotFoundError",
    "ConfigValidationError",
    "DatabaseError",
    "DatabaseConnectionError",
    "RecordNotFoundError",
    "SystemError",
    "FileNotFoundError",
    "InsufficientStorageError",
    "handle_exception",
    "create_error_from_http_status",
]
