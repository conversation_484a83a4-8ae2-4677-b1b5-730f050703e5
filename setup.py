#!/usr/bin/env python3
"""
TextUp - 多平台文本内容发布工具
Multi-platform text content publishing tool
"""

from setuptools import setup, find_packages
import os

# Read long description from README
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# Read requirements from requirements.txt
def read_requirements():
    """Read requirements from requirements.txt file"""
    req_file = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(req_file):
        with open(req_file, 'r', encoding='utf-8') as f:
            requirements = []
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    # Remove version constraints for flexibility
                    pkg = line.split('>=')[0].split('==')[0].split('>')[0].split('<')[0]
                    requirements.append(pkg)
            return requirements
    return []

setup(
    name="textup",
    version="1.0.0",
    author="TextUp Team",
    author_email="<EMAIL>",
    description="多平台文本内容发布工具",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/textup/textup",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: System :: Archiving :: Backup",
        "Topic :: Text Processing :: Markup",
    ],
    python_requires=">=3.9",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.4.3",
            "pytest-cov>=4.1.0",
            "pytest-mock>=3.12.0",
            "pytest-asyncio>=0.21.1",
            "black>=23.12.0",
            "isort>=5.13.2",
            "flake8>=6.1.0",
            "mypy>=1.7.1",
            "pre-commit>=3.6.0",
            "bandit>=1.7.5",
            "safety>=2.3.5",
        ]
    },
    entry_points={
        "console_scripts": [
            "textup=textup.cli:main",
        ],
    },
    include_package_data=True,
    zip_safe=False,
    keywords="content-publishing multi-platform automation zhihu weibo xiaohongshu toutiao cli",
    project_urls={
        "Bug Reports": "https://github.com/textup/textup/issues",
        "Source": "https://github.com/textup/textup",
        "Documentation": "https://github.com/textup/textup#readme",
    },
)