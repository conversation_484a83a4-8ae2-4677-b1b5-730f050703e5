#!/bin/bash

echo "🚀 TextUp 快速测试工具"
echo "===================="

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试结果计数
PASS=0
FAIL=0

# 兼容macOS的timeout函数
run_with_timeout() {
    local timeout_duration="$1"
    local command="$2"

    if command -v timeout >/dev/null 2>&1; then
        # Linux系统使用timeout命令
        timeout "${timeout_duration}s" bash -c "$command"
    elif command -v gtimeout >/dev/null 2>&1; then
        # macOS with coreutils installed
        gtimeout "${timeout_duration}s" bash -c "$command"
    else
        # macOS fallback - 使用后台进程和kill
        bash -c "$command" &
        local pid=$!
        sleep "$timeout_duration" && kill $pid 2>/dev/null &
        local killer_pid=$!
        wait $pid 2>/dev/null
        local exit_code=$?
        kill $killer_pid 2>/dev/null
        return $exit_code
    fi
}

# 测试函数
test_step() {
    local step_name="$1"
    local command="$2"
    local description="$3"

    echo -e "\n${YELLOW}🔸 $step_name${NC}"
    if [ -n "$description" ]; then
        echo "   $description"
    fi

    if eval "$command" > /tmp/quick_test_output 2>&1; then
        echo -e "${GREEN}✅ 通过${NC}"
        ((PASS++))
    else
        echo -e "${RED}❌ 失败${NC}"
        echo "错误信息:"
        head -3 /tmp/quick_test_output | sed 's/^/   /'
        ((FAIL++))
    fi
}

# 开始测试
start_time=$(date +%s)

# 1. 环境检查
test_step "Python环境检查" "python --version" "检查Python是否可用"
test_step "UV工具检查" "uv --version" "检查uv包管理器"
test_step "虚拟环境检查" "[ -n \"\$VIRTUAL_ENV\" ]" "检查是否在虚拟环境中"

# 2. 项目结构检查
test_step "源码目录检查" "[ -d 'src/textup' ]" "检查源码目录结构"
test_step "主模块检查" "[ -f 'src/textup/cli/main.py' ]" "检查主模块文件"
test_step "配置文件检查" "[ -f 'pyproject.toml' ]" "检查项目配置文件"

# 3. 语法和导入检查
test_step "主模块语法检查" "python -m py_compile src/textup/cli/main.py" "检查主模块语法"
test_step "模块导入测试" "python -c 'import sys; sys.path.append(\"src\"); import textup'" "测试模块导入"

# 4. 基础功能测试
test_step "版本命令测试" "run_with_timeout 10 'uv run python -m textup.cli --version'" "测试版本显示"
test_step "帮助命令测试" "run_with_timeout 10 'uv run python -m textup.cli --help'" "测试帮助信息"

# 5. CLI基础功能
test_step "配置命令测试" "run_with_timeout 15 'uv run python -m textup.cli config --list'" "测试配置列表功能"

# 6. 创建必要目录
test_step "日志目录创建" "mkdir -p dev-logs" "确保日志目录存在"
test_step "配置目录创建" "mkdir -p dev-config" "确保配置目录存在"

# 结果统计
end_time=$(date +%s)
duration=$((end_time - start_time))

echo -e "\n📊 测试结果统计"
echo "================"
echo -e "总计: $((PASS + FAIL)) 项测试"
echo -e "${GREEN}通过: $PASS${NC}"
echo -e "${RED}失败: $FAIL${NC}"
echo -e "用时: ${duration}秒"

if [ $FAIL -eq 0 ]; then
    echo -e "\n${GREEN}🎉 所有测试通过！开发环境就绪！${NC}"
    exit 0
else
    echo -e "\n${RED}⚠️  有 $FAIL 项测试失败，请检查环境配置${NC}"
    echo -e "\n💡 建议操作："
    echo "   1. 确保在项目根目录执行"
    echo "   2. 激活虚拟环境: source .venv/bin/activate"
    echo "   3. 安装依赖: uv pip install -e ."
    echo "   4. 运行故障排查: ./dev-scripts/troubleshoot.sh"
    exit 1
fi
