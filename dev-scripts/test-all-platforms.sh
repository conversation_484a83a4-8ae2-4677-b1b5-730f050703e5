#!/bin/bash

echo "🧪 TextUp 全平台功能测试套件"
echo "============================"

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 测试统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
SKIPPED_TESTS=0

# 测试结果数组
declare -a TEST_RESULTS
declare -a FAILED_TESTS_DETAILS

# 创建测试目录和文件
setup_test_environment() {
    echo -e "${BLUE}📋 设置测试环境...${NC}"

    # 创建必要目录
    mkdir -p dev-logs dev-config test-content test-results

    # 创建测试文章
    cat > test-content/basic-test.md << 'EOF'
# TextUp 功能测试文章

## 简介
这是一篇用于测试TextUp多平台发布功能的基础测试文章。

## 主要内容
- **功能**: 多平台发布
- **格式**: Markdown支持
- **平台**: 微博、知乎、今日头条

## 测试要点
1. 文本格式转换
2. 链接处理
3. 标签解析

### 代码示例
```python
def test_textup():
    return "Hello TextUp!"
```

### 列表测试
- 项目一
- 项目二
- 项目三

**测试标签**: #TextUp #测试 #多平台发布

测试时间: $(date)
EOF

    # 创建微博专用测试文章
    cat > test-content/weibo-short.md << 'EOF'
# 微博测试 🚀

TextUp微博发布测试中...

✅ 支持短内容
✅ 支持Emoji
✅ 支持话题标签

#微博测试 #TextUp #自动发布
EOF

    # 创建知乎长文章测试
    cat > test-content/zhihu-long.md << 'EOF'
# 知乎专栏：TextUp深度解析

## 引言
TextUp是一个优秀的多平台内容发布工具，本文将深入分析其技术架构和应用场景。

## 技术特性
### 1. 架构设计
TextUp采用现代Python技术栈，包括：
- **CLI框架**: Typer
- **HTTP客户端**: httpx
- **数据验证**: Pydantic
- **配置管理**: YAML

### 2. 核心功能
```python
class PlatformAdapter:
    async def publish(self, content: str) -> PublishResult:
        # 发布逻辑
        return await self._post_content(content)
```

## 使用场景
1. 个人博主内容同步
2. 企业营销内容分发
3. 技术团队文档发布

## 总结
TextUp通过自动化处理，大大提升了内容发布效率。

**关键词**: #知乎 #TextUp #技术分析 #多平台发布
EOF

    # 创建今日头条测试文章
    cat > test-content/toutiao-article.md << 'EOF'
# 今日头条：多平台发布工具推荐

## 痛点分析
自媒体创作者面临的挑战：
- 📝 多平台手动发布费时费力
- 🔄 格式要求不同需要调整
- 🔐 账号管理复杂

## 解决方案
TextUp一键解决上述问题！

### 核心优势
**🚀 效率提升**: 一键多发，节省80%时间
**🎯 智能适配**: 自动格式转换
**🔒 安全可靠**: OAuth2.0认证

### 技术特色
- 异步并发处理
- 类型安全设计
- 完善错误处理

## 使用效果
用户反馈显示：
> "TextUp让发布变得像发朋友圈一样简单！"

立即体验TextUp，提升内容创作效率！

#今日头条 #TextUp #效率工具 #自媒体
EOF

    echo -e "${GREEN}✅ 测试环境设置完成${NC}"
}

# 执行测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    local platform="$3"
    local timeout_duration="${4:-30}"

    echo -e "\n${CYAN}🔸 [$platform] $test_name${NC}"
    ((TOTAL_TESTS++))

    # 使用timeout防止命令挂起
    if timeout ${timeout_duration}s bash -c "$test_command" > "test-results/${platform}_${test_name// /_}_$(date +%s).log" 2>&1; then
        echo -e "${GREEN}✅ 通过${NC}"
        ((PASSED_TESTS++))
        TEST_RESULTS+=("✅ [$platform] $test_name")
    else
        echo -e "${RED}❌ 失败${NC}"
        ((FAILED_TESTS++))
        TEST_RESULTS+=("❌ [$platform] $test_name")
        FAILED_TESTS_DETAILS+=("[$platform] $test_name: $test_command")
        # 显示错误信息的前几行
        echo "   错误详情:"
        tail -3 "test-results/${platform}_${test_name// /_}_"*.log 2>/dev/null | head -3 | sed 's/^/   /'
    fi
}

# 跳过测试函数
skip_test() {
    local test_name="$1"
    local platform="$2"
    local reason="$3"

    echo -e "\n${YELLOW}⏭️  [$platform] $test_name - 跳过${NC}"
    echo -e "   原因: $reason"
    ((TOTAL_TESTS++))
    ((SKIPPED_TESTS++))
    TEST_RESULTS+=("⏭️ [$platform] $test_name - $reason")
}

# 平台配置测试
test_platform_config() {
    local platform="$1"
    local client_key="$2"
    local client_secret="$3"
    local redirect_uri="$4"

    echo -e "\n${PURPLE}📋 测试 $platform 平台配置${NC}"

    # 配置基本凭证
    run_test "配置客户端ID" "uv run python -m textup.cli --debug config --set platforms.${platform}.${client_key}=test_${platform}_id" "$platform" 15
    run_test "配置客户端密钥" "uv run python -m textup.cli --debug config --set platforms.${platform}.${client_secret}=test_${platform}_secret" "$platform" 15
    run_test "配置回调地址" "uv run python -m textup.cli --debug config --set platforms.${platform}.${redirect_uri}=http://localhost:8080/auth/${platform}/callback" "$platform" 15

    # 验证配置
    run_test "验证平台配置" "uv run python -m textup.cli --debug config --get platforms.${platform}" "$platform" 10

    # 测试认证状态
    run_test "检查认证状态" "uv run python -m textup.cli --debug auth --status --platform ${platform}" "$platform" 15
}

# 内容处理测试
test_content_processing() {
    local platform="$1"
    local test_file="$2"

    echo -e "\n${PURPLE}📄 测试 $platform 内容处理${NC}"

    if [ ! -f "$test_file" ]; then
        skip_test "内容验证" "$platform" "测试文件不存在: $test_file"
        skip_test "内容预览" "$platform" "测试文件不存在: $test_file"
        skip_test "模拟发布" "$platform" "测试文件不存在: $test_file"
        return
    fi

    # 内容验证
    run_test "内容验证" "uv run python -m textup.cli --debug validate --file $test_file --platform $platform" "$platform" 20

    # 内容预览
    run_test "内容预览" "uv run python -m textup.cli --debug preview --file $test_file --platform $platform" "$platform" 25

    # 模拟发布
    run_test "模拟发布" "uv run python -m textup.cli --debug publish --file $test_file --platform $platform --dry-run" "$platform" 30
}

# 错误处理测试
test_error_handling() {
    local platform="$1"

    echo -e "\n${PURPLE}🚨 测试 $platform 错误处理${NC}"

    # 测试无效文件
    run_test "无效文件处理" "! uv run python -m textup.cli --debug publish --file nonexistent.md --platform $platform --dry-run" "$platform" 15

    # 测试空文件
    touch test-content/empty.md
    run_test "空文件处理" "uv run python -m textup.cli --debug validate --file test-content/empty.md --platform $platform" "$platform" 15
    rm -f test-content/empty.md
}

# 开始测试执行
main() {
    echo -e "${BLUE}🚀 开始全平台功能测试${NC}"
    echo "测试时间: $(date)"
    echo "测试日志将保存在: test-results/"

    # 设置测试环境
    setup_test_environment

    # 基础环境测试
    echo -e "\n${PURPLE}🔧 基础环境测试${NC}"
    run_test "Python环境检查" "python --version" "系统" 5
    run_test "UV工具检查" "uv --version" "系统" 5
    run_test "模块导入检查" "python -c 'import sys; sys.path.append(\"src\"); import textup'" "系统" 10
    run_test "版本命令检查" "uv run python -m textup.cli --version" "系统" 15
    run_test "帮助命令检查" "uv run python -m textup.cli --help" "系统" 15

    # 配置系统测试
    echo -e "\n${PURPLE}⚙️ 配置系统测试${NC}"
    run_test "配置列表功能" "uv run python -m textup.cli --debug config --list" "配置" 20
    run_test "配置设置功能" "uv run python -m textup.cli --debug config --set test.key=value" "配置" 15
    run_test "配置获取功能" "uv run python -m textup.cli --debug config --get test.key" "配置" 10

    # 微博平台测试
    test_platform_config "weibo" "client_id" "client_secret" "redirect_uri"
    test_content_processing "weibo" "test-content/weibo-short.md"
    test_error_handling "weibo"

    # 知乎平台测试
    test_platform_config "zhihu" "client_id" "client_secret" "redirect_uri"
    test_content_processing "zhihu" "test-content/zhihu-long.md"
    test_error_handling "zhihu"

    # 今日头条平台测试
    test_platform_config "toutiao" "app_id" "secret" "redirect_uri"
    test_content_processing "toutiao" "test-content/toutiao-article.md"
    test_error_handling "toutiao"

    # 多平台综合测试
    echo -e "\n${PURPLE}🌐 多平台综合测试${NC}"
    run_test "多平台配置检查" "uv run python -m textup.cli --debug config --get platforms" "多平台" 20
    run_test "多平台认证状态" "uv run python -m textup.cli --debug auth --status" "多平台" 25
    run_test "多平台模拟发布" "uv run python -m textup.cli --debug publish --file test-content/basic-test.md --platforms weibo,zhihu,toutiao --dry-run" "多平台" 45

    # 批量处理测试
    echo -e "\n${PURPLE}📦 批量处理测试${NC}"
    run_test "目录批量处理" "uv run python -m textup.cli --debug publish --directory test-content --platform weibo --dry-run" "批量" 60

    # 性能测试
    echo -e "\n${PURPLE}⚡ 性能测试${NC}"

    # 创建性能测试文件
    for i in {1..5}; do
        cp test-content/basic-test.md "test-content/perf-test-$i.md"
    done

    # 测试批量处理性能
    start_time=$(date +%s)
    run_test "批量性能测试" "uv run python -m textup.cli --debug publish --pattern 'test-content/perf-test-*.md' --platform weibo --dry-run" "性能" 90
    end_time=$(date +%s)
    perf_duration=$((end_time - start_time))

    # 清理性能测试文件
    rm -f test-content/perf-test-*.md

    # 生成测试报告
    generate_report "$perf_duration"
}

# 生成测试报告
generate_report() {
    local perf_duration="$1"

    echo -e "\n${BLUE}📊 测试报告${NC}"
    echo "============================================"
    echo "测试完成时间: $(date)"
    echo "总测试数量: $TOTAL_TESTS"
    echo -e "${GREEN}通过测试: $PASSED_TESTS${NC}"
    echo -e "${RED}失败测试: $FAILED_TESTS${NC}"
    echo -e "${YELLOW}跳过测试: $SKIPPED_TESTS${NC}"
    echo "性能测试用时: ${perf_duration}秒"
    echo "成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"

    # 保存详细报告
    cat > test-results/test-report-$(date +%Y%m%d-%H%M%S).md << EOF
# TextUp 全平台功能测试报告

## 测试概要
- 测试时间: $(date)
- 总测试数: $TOTAL_TESTS
- 通过数: $PASSED_TESTS
- 失败数: $FAILED_TESTS
- 跳过数: $SKIPPED_TESTS
- 成功率: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%
- 性能测试用时: ${perf_duration}秒

## 测试结果详情
$(printf '%s\n' "${TEST_RESULTS[@]}")

## 失败测试详情
$(if [ ${#FAILED_TESTS_DETAILS[@]} -gt 0 ]; then printf '%s\n' "${FAILED_TESTS_DETAILS[@]}"; else echo "无失败测试"; fi)

## 建议
$(if [ $FAILED_TESTS -gt 0 ]; then
echo "- 检查失败的测试项目"
echo "- 查看详细日志文件"
echo "- 运行 ./dev-scripts/troubleshoot.sh 排查问题"
else
echo "- 所有测试通过，系统运行正常"
echo "- 可以进行下一步开发或部署"
fi)

## 日志文件
测试过程的详细日志保存在 test-results/ 目录下。
EOF

    echo -e "\n📄 详细报告已保存到: test-results/test-report-$(date +%Y%m%d-%H%M%S).md"

    # 显示详细结果
    if [ $FAILED_TESTS -gt 0 ]; then
        echo -e "\n${RED}⚠️ 失败的测试:${NC}"
        for detail in "${FAILED_TESTS_DETAILS[@]}"; do
            echo -e "   ${RED}•${NC} $detail"
        done

        echo -e "\n${YELLOW}💡 建议操作:${NC}"
        echo "   1. 查看详细日志: ls -la test-results/"
        echo "   2. 运行故障排查: ./dev-scripts/troubleshoot.sh"
        echo "   3. 检查环境配置: ./dev-scripts/quick-test.sh"

        exit 1
    else
        echo -e "\n${GREEN}🎉 所有测试通过！系统功能正常！${NC}"
        echo -e "\n${CYAN}🚀 下一步建议:${NC}"
        echo "   1. 配置真实的API凭证"
        echo "   2. 进行真实环境发布测试"
        echo "   3. 开始正式使用或部署"

        exit 0
    fi
}

# 清理函数
cleanup() {
    echo -e "\n${YELLOW}🧹 清理测试环境...${NC}"
    # 保留日志但清理临时文件
    find test-results/ -name "*.tmp" -delete 2>/dev/null || true
    echo -e "${GREEN}✅ 清理完成${NC}"
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 检查是否在正确目录
if [ ! -f "pyproject.toml" ] || [ ! -d "src/textup" ]; then
    echo -e "${RED}❌ 错误: 请在TextUp项目根目录下运行此脚本${NC}"
    echo "当前目录: $(pwd)"
    echo "期望文件: pyproject.toml, src/textup/"
    exit 1
fi

# 检查虚拟环境
if [ -z "$VIRTUAL_ENV" ]; then
    echo -e "${YELLOW}⚠️ 警告: 未检测到虚拟环境${NC}"
    echo "建议先激活虚拟环境: source .venv/bin/activate"
    read -p "继续执行吗？(y/N): " confirm
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        echo "测试已取消"
        exit 1
    fi
fi

# 执行主函数
main "$@"
