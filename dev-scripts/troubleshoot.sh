#!/bin/bash

echo "🔍 TextUp 故障排查工具"
echo "======================"
echo "该工具将帮助您诊断TextUp开发环境中的常见问题"
echo "检查时间: $(date)"
echo ""

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 问题计数器
ISSUES_FOUND=0
WARNINGS_FOUND=0
CHECKS_PASSED=0

# 问题详情数组
declare -a ISSUES_DETAILS
declare -a WARNINGS_DETAILS
declare -a SUGGESTIONS

# 检查函数
check_item() {
    local item_name="$1"
    local check_command="$2"
    local success_message="$3"
    local failure_message="$4"
    local is_critical="${5:-true}"

    echo -n "🔸 检查 $item_name ... "

    if eval "$check_command" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ $success_message${NC}"
        ((CHECKS_PASSED++))
        return 0
    else
        if [ "$is_critical" = "true" ]; then
            echo -e "${RED}❌ $failure_message${NC}"
            ((ISSUES_FOUND++))
            ISSUES_DETAILS+=("$item_name: $failure_message")
        else
            echo -e "${YELLOW}⚠️ $failure_message${NC}"
            ((WARNINGS_FOUND++))
            WARNINGS_DETAILS+=("$item_name: $failure_message")
        fi
        return 1
    fi
}

# 信息显示函数
show_info() {
    local label="$1"
    local command="$2"
    local color="$3"

    echo -n "📋 $label: "
    if result=$(eval "$command" 2>/dev/null); then
        echo -e "${color}$result${NC}"
    else
        echo -e "${RED}获取失败${NC}"
    fi
}

echo "🌟 第一部分：系统环境检查"
echo "========================"

# 1. 基础环境检查
show_info "操作系统" "uname -s" "$CYAN"
show_info "架构" "uname -m" "$CYAN"
show_info "当前用户" "whoami" "$CYAN"
show_info "当前目录" "pwd" "$CYAN"
show_info "Shell" "echo \$SHELL" "$CYAN"

echo ""

# 2. Python环境检查
echo "🐍 Python环境检查"
echo "------------------"

check_item "Python可执行性" "command -v python" "Python可用" "Python未找到，请安装Python" true

if command -v python >/dev/null 2>&1; then
    show_info "Python版本" "python --version" "$GREEN"
    show_info "Python路径" "which python" "$BLUE"

    # 检查Python版本是否满足要求
    python_version=$(python -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')" 2>/dev/null)
    if [ -n "$python_version" ]; then
        if python -c "import sys; exit(0 if sys.version_info >= (3, 9) else 1)" 2>/dev/null; then
            echo -e "🔸 Python版本检查 ... ${GREEN}✅ 版本 $python_version 满足要求 (>= 3.9)${NC}"
            ((CHECKS_PASSED++))
        else
            echo -e "🔸 Python版本检查 ... ${RED}❌ 版本 $python_version 过低，需要 >= 3.9${NC}"
            ((ISSUES_FOUND++))
            ISSUES_DETAILS+=("Python版本: 需要升级到3.9或更高版本")
            SUGGESTIONS+=("升级Python: 建议使用pyenv或从官网下载新版本")
        fi
    fi
fi

# 检查Python3
if command -v python3 >/dev/null 2>&1; then
    show_info "Python3版本" "python3 --version" "$GREEN"
    show_info "Python3路径" "which python3" "$BLUE"
fi

echo ""

# 3. 包管理器检查
echo "📦 包管理器检查"
echo "--------------"

check_item "uv包管理器" "command -v uv" "uv已安装" "uv未安装，这是推荐的包管理器" false

if command -v uv >/dev/null 2>&1; then
    show_info "UV版本" "uv --version" "$GREEN"
    show_info "UV路径" "which uv" "$BLUE"
else
    SUGGESTIONS+=("安装uv: curl -LsSf https://astral.sh/uv/install.sh | sh")
fi

check_item "pip工具" "command -v pip" "pip已安装" "pip未找到" false

if command -v pip >/dev/null 2>&1; then
    show_info "Pip版本" "pip --version" "$GREEN"
fi

echo ""

# 4. 虚拟环境检查
echo "🔒 虚拟环境检查"
echo "--------------"

if [ -n "$VIRTUAL_ENV" ]; then
    echo -e "🔸 虚拟环境状态 ... ${GREEN}✅ 已激活${NC}"
    show_info "虚拟环境路径" "echo \$VIRTUAL_ENV" "$GREEN"
    ((CHECKS_PASSED++))
else
    echo -e "🔸 虚拟环境状态 ... ${YELLOW}⚠️ 未激活虚拟环境${NC}"
    ((WARNINGS_FOUND++))
    WARNINGS_DETAILS+=("虚拟环境: 建议在虚拟环境中运行")
    SUGGESTIONS+=("激活虚拟环境: source .venv/bin/activate")
fi

check_item "虚拟环境目录" "[ -d '.venv' ]" ".venv目录存在" ".venv目录不存在" false

if [ -d ".venv" ]; then
    show_info "虚拟环境Python" "ls -la .venv/bin/python*" "$BLUE"
fi

echo ""

# 5. 项目结构检查
echo "🏗️ 项目结构检查"
echo "==============="

echo "🔸 检查项目根目录文件:"
critical_files=(
    "pyproject.toml:项目配置文件"
    "README.md:项目说明文件"
    "src/:源码目录"
    "src/textup/:主模块目录"
    "src/textup/__init__.py:模块初始化文件"
    "src/textup/cli/:CLI模块目录"
    "src/textup/cli/main.py:主程序文件"
)

for file_info in "${critical_files[@]}"; do
    IFS=':' read -r file desc <<< "$file_info"
    check_item "$desc" "[ -e '$file' ]" "存在" "缺失: $file" true
done

echo ""
echo "🔸 检查可选目录和文件:"
optional_items=(
    "tests/:测试目录"
    "docs/:文档目录"
    "dev-scripts/:开发脚本目录"
    "requirements.txt:依赖文件"
    ".gitignore:Git忽略文件"
)

for item_info in "${optional_items[@]}"; do
    IFS=':' read -r item desc <<< "$item_info"
    check_item "$desc" "[ -e '$item' ]" "存在" "不存在 (可选)" false
done

echo ""

# 6. 依赖检查
echo "🔗 依赖包检查"
echo "============"

echo "🔸 检查核心Python包:"
core_packages=(
    "typer:CLI框架"
    "rich:终端美化"
    "pydantic:数据验证"
    "httpx:HTTP客户端"
    "pyyaml:YAML处理"
    "asyncio:异步处理"
)

python_available=false
if command -v python >/dev/null 2>&1; then
    python_available=true
fi

for pkg_info in "${core_packages[@]}"; do
    IFS=':' read -r pkg desc <<< "$pkg_info"
    if [ "$python_available" = true ]; then
        check_item "$desc ($pkg)" "python -c 'import $pkg'" "已安装" "未安装" true
    else
        echo -e "🔸 $desc ($pkg) ... ${RED}❌ 无法检查 (Python不可用)${NC}"
    fi
done

echo ""

# 7. 模块导入测试
echo "🔄 模块导入测试"
echo "=============="

if [ "$python_available" = true ]; then
    check_item "TextUp模块导入" "python -c 'import sys; sys.path.append(\"src\"); import textup'" "导入成功" "导入失败" true

    if python -c 'import sys; sys.path.append("src"); import textup' >/dev/null 2>&1; then
        check_item "CLI模块导入" "python -c 'import sys; sys.path.append(\"src\"); from textup.cli import main'" "导入成功" "导入失败" true
        check_item "服务模块导入" "python -c 'import sys; sys.path.append(\"src\"); from textup.services import config_manager'" "导入成功" "导入失败" false
    fi
else
    echo -e "🔸 模块导入测试 ... ${RED}❌ 跳过 (Python不可用)${NC}"
fi

echo ""

# 8. 配置和权限检查
echo "⚙️ 配置和权限检查"
echo "=================="

# 检查写权限
check_item "当前目录写权限" "[ -w '.' ]" "有写权限" "无写权限" true

# 检查配置目录
config_dirs=(
    "$HOME/.textup:用户配置目录"
    "./dev-config:开发配置目录"
    "./dev-logs:开发日志目录"
)

for dir_info in "${config_dirs[@]}"; do
    IFS=':' read -r dir desc <<< "$dir_info"
    check_item "$desc" "[ -d '$dir' ]" "目录存在" "目录不存在" false

    if [ -d "$dir" ]; then
        check_item "$desc 写权限" "[ -w '$dir' ]" "有写权限" "无写权限" false
    fi
done

echo ""

# 9. 基本功能测试
echo "🧪 基本功能测试"
echo "=============="

if [ "$python_available" = true ] && python -c 'import sys; sys.path.append("src"); import textup' >/dev/null 2>&1; then

    # 测试版本命令
    if command -v uv >/dev/null 2>&1; then
        check_item "版本命令测试" "timeout 10s uv run python -m textup.cli --version" "版本显示正常" "版本命令失败" true
        check_item "帮助命令测试" "timeout 10s uv run python -m textup.cli --help" "帮助显示正常" "帮助命令失败" true

        # 测试配置命令（如果基础命令工作）
        if timeout 10s uv run python -m textup.cli --version >/dev/null 2>&1; then
            check_item "配置命令测试" "timeout 15s uv run python -m textup.cli config --list" "配置命令正常" "配置命令失败" false
        fi
    else
        echo -e "🔸 命令行测试 ... ${YELLOW}⚠️ 跳过 (uv不可用)${NC}"
    fi
else
    echo -e "🔸 功能测试 ... ${RED}❌ 跳过 (模块导入失败)${NC}"
fi

echo ""

# 10. 磁盘空间和系统资源检查
echo "💾 系统资源检查"
echo "==============="

show_info "磁盘使用率" "df -h . | tail -1 | awk '{print \$5}'" "$BLUE"
show_info "可用空间" "df -h . | tail -1 | awk '{print \$4}'" "$BLUE"

if command -v free >/dev/null 2>&1; then
    show_info "内存使用" "free -h | grep '^Mem:' | awk '{print \$3\"/\"\$2}'" "$BLUE"
fi

# 检查磁盘空间
available_space=$(df . | tail -1 | awk '{print $4}')
if [ "$available_space" -lt 500000 ]; then  # 500MB in KB
    echo -e "🔸 磁盘空间检查 ... ${YELLOW}⚠️ 可用空间较少 (< 500MB)${NC}"
    ((WARNINGS_FOUND++))
    WARNINGS_DETAILS+=("磁盘空间: 可用空间不足500MB")
    SUGGESTIONS+=("清理磁盘空间: 删除不需要的文件或移动项目到空间更大的分区")
else
    echo -e "🔸 磁盘空间检查 ... ${GREEN}✅ 空间充足${NC}"
    ((CHECKS_PASSED++))
fi

echo ""

# 11. 网络连接检查
echo "🌐 网络连接检查"
echo "==============="

check_item "互联网连接" "ping -c 1 8.8.8.8" "网络连接正常" "网络连接失败" false
check_item "DNS解析" "nslookup google.com" "DNS解析正常" "DNS解析失败" false

if command -v curl >/dev/null 2>&1; then
    check_item "HTTPS连接" "curl -Is https://www.google.com" "HTTPS连接正常" "HTTPS连接失败" false
fi

echo ""

# 12. 生成诊断报告
echo "📊 诊断结果总结"
echo "================"

total_checks=$((CHECKS_PASSED + ISSUES_FOUND + WARNINGS_FOUND))
echo "检查项目总数: $total_checks"
echo -e "${GREEN}通过检查: $CHECKS_PASSED${NC}"
echo -e "${RED}发现问题: $ISSUES_FOUND${NC}"
echo -e "${YELLOW}警告信息: $WARNINGS_FOUND${NC}"

if [ $ISSUES_FOUND -gt 0 ]; then
    echo ""
    echo -e "${RED}🚨 发现的问题:${NC}"
    for issue in "${ISSUES_DETAILS[@]}"; do
        echo -e "   ${RED}•${NC} $issue"
    done
fi

if [ $WARNINGS_FOUND -gt 0 ]; then
    echo ""
    echo -e "${YELLOW}⚠️ 警告信息:${NC}"
    for warning in "${WARNINGS_DETAILS[@]}"; do
        echo -e "   ${YELLOW}•${NC} $warning"
    done
fi

if [ ${#SUGGESTIONS[@]} -gt 0 ]; then
    echo ""
    echo -e "${CYAN}💡 建议的解决方案:${NC}"
    for suggestion in "${SUGGESTIONS[@]}"; do
        echo -e "   ${CYAN}•${NC} $suggestion"
    done
fi

# 13. 快速修复建议
echo ""
echo "🔧 快速修复指南"
echo "==============="

if [ $ISSUES_FOUND -gt 0 ] || [ $WARNINGS_FOUND -gt 0 ]; then
    echo "根据检查结果，建议按以下顺序进行修复："
    echo ""

    if ! command -v python >/dev/null 2>&1; then
        echo "1. 安装Python 3.9+:"
        echo "   - macOS: brew install python@3.9 或从 python.org 下载"
        echo "   - Ubuntu: sudo apt update && sudo apt install python3.9"
        echo "   - 其他系统: 访问 https://python.org"
        echo ""
    fi

    if ! command -v uv >/dev/null 2>&1; then
        echo "2. 安装uv包管理器:"
        echo "   curl -LsSf https://astral.sh/uv/install.sh | sh"
        echo "   然后重新加载shell: source ~/.bashrc 或 source ~/.zshrc"
        echo ""
    fi

    if [ -z "$VIRTUAL_ENV" ]; then
        echo "3. 创建并激活虚拟环境:"
        echo "   uv venv"
        echo "   source .venv/bin/activate"
        echo ""
    fi

    if ! python -c 'import sys; sys.path.append("src"); import textup' >/dev/null 2>&1; then
        echo "4. 安装项目依赖:"
        echo "   uv pip install -e ."
        echo ""
    fi

    echo "5. 创建必要目录:"
    echo "   mkdir -p dev-config dev-logs test-content test-results"
    echo ""

    echo "6. 运行快速测试验证修复:"
    echo "   ./dev-scripts/quick-test.sh"
    echo ""
else
    echo -e "${GREEN}🎉 恭喜！您的开发环境配置完美！${NC}"
    echo ""
    echo "建议的下一步操作："
    echo "1. 运行快速测试: ./dev-scripts/quick-test.sh"
    echo "2. 运行完整测试: ./dev-scripts/test-all-platforms.sh"
    echo "3. 开始开发或测试功能"
fi

# 14. 生成诊断日志
echo ""
echo "📄 保存诊断报告"
echo "==============="

mkdir -p dev-logs

report_file="dev-logs/troubleshoot-$(date +%Y%m%d-%H%M%S).log"
{
    echo "TextUp 故障排查报告"
    echo "=================="
    echo "时间: $(date)"
    echo "用户: $(whoami)"
    echo "系统: $(uname -a)"
    echo "目录: $(pwd)"
    echo ""
    echo "检查结果统计:"
    echo "- 总检查数: $total_checks"
    echo "- 通过数: $CHECKS_PASSED"
    echo "- 问题数: $ISSUES_FOUND"
    echo "- 警告数: $WARNINGS_FOUND"
    echo ""
    if [ $ISSUES_FOUND -gt 0 ]; then
        echo "发现的问题:"
        printf '%s\n' "${ISSUES_DETAILS[@]}"
        echo ""
    fi
    if [ $WARNINGS_FOUND -gt 0 ]; then
        echo "警告信息:"
        printf '%s\n' "${WARNINGS_DETAILS[@]}"
        echo ""
    fi
    if [ ${#SUGGESTIONS[@]} -gt 0 ]; then
        echo "建议方案:"
        printf '%s\n' "${SUGGESTIONS[@]}"
        echo ""
    fi
} > "$report_file"

echo "诊断报告已保存到: $report_file"

# 15. 退出状态
echo ""
if [ $ISSUES_FOUND -gt 0 ]; then
    echo -e "${RED}❌ 发现 $ISSUES_FOUND 个问题需要解决${NC}"
    exit 1
elif [ $WARNINGS_FOUND -gt 0 ]; then
    echo -e "${YELLOW}⚠️ 有 $WARNINGS_FOUND 个警告，但不影响基本使用${NC}"
    exit 0
else
    echo -e "${GREEN}✅ 环境检查完全通过！${NC}"
    exit 0
fi
