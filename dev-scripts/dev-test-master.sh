#!/bin/bash

# TextUp 开发测试主控台
# ======================
# 统一管理所有开发测试功能的主入口脚本

set -e  # 遇到错误立即退出

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 项目信息
PROJECT_NAME="TextUp"
VERSION="1.0.0"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 清屏函数
clear_screen() {
    clear
    echo -e "${BLUE}╔════════════════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║${WHITE}                             TextUp 开发测试主控台 v$VERSION                              ${BLUE}║${NC}"
    echo -e "${BLUE}║${CYAN}                           统一管理所有开发测试功能的主入口                                 ${BLUE}║${NC}"
    echo -e "${BLUE}╠════════════════════════════════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${BLUE}║${NC} 当前时间: $(date '+%Y-%m-%d %H:%M:%S')${BLUE}                                                      ║${NC}"
    echo -e "${BLUE}║${NC} 项目路径: $PROJECT_ROOT${BLUE}"
    printf "%-$((88 - ${#PROJECT_ROOT}))s║\n" ""
    echo -e "${BLUE}║${NC} 脚本路径: $SCRIPT_DIR${BLUE}"
    printf "%-$((88 - ${#SCRIPT_DIR}))s║\n" ""
    echo -e "${BLUE}╚════════════════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 显示状态信息
show_status() {
    echo -e "${CYAN}📊 当前环境状态:${NC}"

    # 检查Python
    if command -v python >/dev/null 2>&1; then
        python_version=$(python --version 2>&1)
        echo -e "   ${GREEN}✅${NC} Python: $python_version"
    else
        echo -e "   ${RED}❌${NC} Python: 未安装"
    fi

    # 检查UV
    if command -v uv >/dev/null 2>&1; then
        uv_version=$(uv --version 2>&1)
        echo -e "   ${GREEN}✅${NC} UV: $uv_version"
    else
        echo -e "   ${YELLOW}⚠️${NC} UV: 未安装 (推荐安装)"
    fi

    # 检查虚拟环境
    if [ -n "$VIRTUAL_ENV" ]; then
        echo -e "   ${GREEN}✅${NC} 虚拟环境: 已激活 ($VIRTUAL_ENV)"
    else
        echo -e "   ${YELLOW}⚠️${NC} 虚拟环境: 未激活"
    fi

    # 检查项目结构
    if [ -f "$PROJECT_ROOT/pyproject.toml" ]; then
        echo -e "   ${GREEN}✅${NC} 项目配置: pyproject.toml 存在"
    else
        echo -e "   ${RED}❌${NC} 项目配置: pyproject.toml 缺失"
    fi

    # 检查源码
    if [ -d "$PROJECT_ROOT/src/textup" ]; then
        echo -e "   ${GREEN}✅${NC} 源码目录: src/textup/ 存在"
    else
        echo -e "   ${RED}❌${NC} 源码目录: src/textup/ 缺失"
    fi

    # 检查日志目录
    if [ -d "$PROJECT_ROOT/dev-logs" ]; then
        log_count=$(ls -1 "$PROJECT_ROOT/dev-logs"/*.log 2>/dev/null | wc -l || echo 0)
        echo -e "   ${GREEN}✅${NC} 日志目录: 存在 ($log_count 个日志文件)"
    else
        echo -e "   ${YELLOW}⚠️${NC} 日志目录: 不存在 (将自动创建)"
    fi

    echo ""
}

# 显示主菜单
show_main_menu() {
    echo -e "${WHITE}🎯 主功能菜单:${NC}"
    echo -e "${GREEN}   1.${NC} 🚀 快速测试          - 基础环境和语法检查 (30秒)"
    echo -e "${GREEN}   2.${NC} 🧪 完整功能测试      - 所有平台完整功能验证 (5-10分钟)"
    echo -e "${GREEN}   3.${NC} 🔍 故障排查          - 深度环境诊断和问题修复"
    echo -e "${GREEN}   4.${NC} 📊 日志分析          - 分析运行日志和性能数据"
    echo -e "${GREEN}   5.${NC} 🛠️ 开发工具菜单      - 代码开发和调试工具"
    echo ""
    echo -e "${WHITE}🎛️ 管理功能:${NC}"
    echo -e "${BLUE}   6.${NC} ⚙️ 环境配置管理      - 配置开发环境和依赖"
    echo -e "${BLUE}   7.${NC} 📋 查看系统状态      - 详细的系统和环境信息"
    echo -e "${BLUE}   8.${NC} 🧹 清理和维护        - 清理日志和临时文件"
    echo ""
    echo -e "${WHITE}📚 帮助和信息:${NC}"
    echo -e "${PURPLE}   9.${NC} 📖 使用指南          - 详细的使用说明和示例"
    echo -e "${PURPLE}  10.${NC} 🆘 帮助信息          - 命令帮助和故障解决"
    echo ""
    echo -e "${RED}   0.${NC} 🚪 退出程序"
    echo ""
}

# 显示开发工具菜单
show_dev_menu() {
    clear_screen
    echo -e "${WHITE}🛠️ 开发工具菜单:${NC}"
    echo -e "${GREEN}   1.${NC} 🔄 热重载开发        - 监控代码变化自动测试"
    echo -e "${GREEN}   2.${NC} 🐛 调试模式运行      - 带断点的调试执行"
    echo -e "${GREEN}   3.${NC} 📝 创建测试内容      - 生成各平台测试文章"
    echo -e "${GREEN}   4.${NC} 🧪 单平台测试        - 测试特定平台功能"
    echo -e "${GREEN}   5.${NC} ⚡ 性能基准测试      - 批量处理性能评估"
    echo -e "${GREEN}   6.${NC} 🔧 代码质量检查      - 语法、格式、类型检查"
    echo ""
    echo -e "${YELLOW}   0.${NC} 🔙 返回主菜单"
    echo ""
}

# 执行快速测试
run_quick_test() {
    echo -e "${CYAN}🚀 执行快速测试...${NC}"
    echo ""

    if [ -f "$SCRIPT_DIR/quick-test.sh" ]; then
        chmod +x "$SCRIPT_DIR/quick-test.sh"
        "$SCRIPT_DIR/quick-test.sh"
    else
        echo -e "${RED}❌ 快速测试脚本不存在: $SCRIPT_DIR/quick-test.sh${NC}"
        return 1
    fi

    echo ""
    read -p "按回车键继续..."
}

# 执行完整功能测试
run_full_test() {
    echo -e "${CYAN}🧪 执行完整功能测试...${NC}"
    echo -e "${YELLOW}⚠️ 此操作可能需要5-10分钟时间${NC}"
    read -p "确认继续吗? (y/N): " confirm

    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        echo "已取消测试"
        return 0
    fi

    echo ""

    if [ -f "$SCRIPT_DIR/test-all-platforms.sh" ]; then
        chmod +x "$SCRIPT_DIR/test-all-platforms.sh"
        "$SCRIPT_DIR/test-all-platforms.sh"
    else
        echo -e "${RED}❌ 完整测试脚本不存在: $SCRIPT_DIR/test-all-platforms.sh${NC}"
        return 1
    fi

    echo ""
    read -p "按回车键继续..."
}

# 执行故障排查
run_troubleshoot() {
    echo -e "${CYAN}🔍 执行故障排查...${NC}"
    echo ""

    if [ -f "$SCRIPT_DIR/troubleshoot.sh" ]; then
        chmod +x "$SCRIPT_DIR/troubleshoot.sh"
        "$SCRIPT_DIR/troubleshoot.sh"
    else
        echo -e "${RED}❌ 故障排查脚本不存在: $SCRIPT_DIR/troubleshoot.sh${NC}"
        return 1
    fi

    echo ""
    read -p "按回车键继续..."
}

# 执行日志分析
run_log_analysis() {
    echo -e "${CYAN}📊 执行日志分析...${NC}"
    echo ""

    if [ -f "$SCRIPT_DIR/analyze-logs.sh" ]; then
        chmod +x "$SCRIPT_DIR/analyze-logs.sh"
        "$SCRIPT_DIR/analyze-logs.sh"
    else
        echo -e "${RED}❌ 日志分析脚本不存在: $SCRIPT_DIR/analyze-logs.sh${NC}"
        return 1
    fi

    echo ""
    read -p "按回车键继续..."
}

# 环境配置管理
manage_environment() {
    clear_screen
    echo -e "${WHITE}⚙️ 环境配置管理${NC}"
    echo ""

    echo -e "${CYAN}选择操作:${NC}"
    echo -e "   ${GREEN}1.${NC} 🔧 初始化开发环境"
    echo -e "   ${GREEN}2.${NC} 📦 安装/更新依赖"
    echo -e "   ${GREEN}3.${NC} 🐍 创建虚拟环境"
    echo -e "   ${GREEN}4.${NC} 📁 创建必要目录"
    echo -e "   ${GREEN}5.${NC} 🔄 重新安装项目"
    echo -e "   ${YELLOW}0.${NC} 🔙 返回主菜单"
    echo ""

    read -p "请选择操作 (0-5): " env_choice

    case $env_choice in
        1)
            echo -e "${CYAN}🔧 初始化开发环境...${NC}"
            init_dev_environment
            ;;
        2)
            echo -e "${CYAN}📦 安装/更新依赖...${NC}"
            install_dependencies
            ;;
        3)
            echo -e "${CYAN}🐍 创建虚拟环境...${NC}"
            create_virtual_env
            ;;
        4)
            echo -e "${CYAN}📁 创建必要目录...${NC}"
            create_directories
            ;;
        5)
            echo -e "${CYAN}🔄 重新安装项目...${NC}"
            reinstall_project
            ;;
        0)
            return 0
            ;;
        *)
            echo -e "${RED}❌ 无效选择${NC}"
            ;;
    esac

    echo ""
    read -p "按回车键继续..."
}

# 初始化开发环境
init_dev_environment() {
    echo "正在初始化开发环境..."

    # 检查并创建虚拟环境
    if [ ! -d "$PROJECT_ROOT/.venv" ]; then
        echo "创建虚拟环境..."
        cd "$PROJECT_ROOT"
        if command -v uv >/dev/null 2>&1; then
            uv venv
        else
            python -m venv .venv
        fi
    fi

    # 激活虚拟环境提示
    if [ -z "$VIRTUAL_ENV" ]; then
        echo -e "${YELLOW}⚠️ 请手动激活虚拟环境:${NC}"
        echo "   source $PROJECT_ROOT/.venv/bin/activate"
    fi

    # 创建必要目录
    create_directories

    # 安装依赖
    install_dependencies

    echo -e "${GREEN}✅ 开发环境初始化完成${NC}"
}

# 安装依赖
install_dependencies() {
    cd "$PROJECT_ROOT"

    if command -v uv >/dev/null 2>&1; then
        echo "使用 uv 安装依赖..."
        uv pip install -e .
    else
        echo "使用 pip 安装依赖..."
        pip install -e .
    fi

    echo -e "${GREEN}✅ 依赖安装完成${NC}"
}

# 创建虚拟环境
create_virtual_env() {
    cd "$PROJECT_ROOT"

    if [ -d ".venv" ]; then
        echo -e "${YELLOW}⚠️ 虚拟环境已存在${NC}"
        read -p "是否重新创建? (y/N): " recreate
        if [ "$recreate" = "y" ] || [ "$recreate" = "Y" ]; then
            rm -rf .venv
        else
            return 0
        fi
    fi

    if command -v uv >/dev/null 2>&1; then
        echo "使用 uv 创建虚拟环境..."
        uv venv
    else
        echo "使用 python 创建虚拟环境..."
        python -m venv .venv
    fi

    echo -e "${GREEN}✅ 虚拟环境创建完成${NC}"
    echo -e "${YELLOW}💡 请手动激活: source .venv/bin/activate${NC}"
}

# 创建必要目录
create_directories() {
    cd "$PROJECT_ROOT"

    directories=(
        "dev-logs"
        "dev-config"
        "test-content"
        "test-results"
    )

    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            echo "✅ 创建目录: $dir"
        fi
    done

    echo -e "${GREEN}✅ 目录结构创建完成${NC}"
}

# 重新安装项目
reinstall_project() {
    cd "$PROJECT_ROOT"

    echo "正在重新安装项目..."

    if command -v uv >/dev/null 2>&1; then
        uv pip uninstall textup -y 2>/dev/null || true
        uv pip install -e .
    else
        pip uninstall textup -y 2>/dev/null || true
        pip install -e .
    fi

    echo -e "${GREEN}✅ 项目重新安装完成${NC}"
}

# 查看系统状态
show_system_status() {
    clear_screen
    echo -e "${WHITE}📋 详细系统状态${NC}"
    echo ""

    echo -e "${CYAN}🖥️ 系统信息:${NC}"
    echo "   操作系统: $(uname -s)"
    echo "   架构: $(uname -m)"
    echo "   内核: $(uname -r)"
    echo "   主机名: $(hostname)"
    echo ""

    echo -e "${CYAN}🐍 Python 环境:${NC}"
    if command -v python >/dev/null 2>&1; then
        echo "   Python 版本: $(python --version)"
        echo "   Python 路径: $(which python)"
        echo "   Pip 版本: $(pip --version 2>/dev/null || echo '未安装')"
    else
        echo "   ❌ Python 未安装"
    fi
    echo ""

    echo -e "${CYAN}📦 包管理器:${NC}"
    if command -v uv >/dev/null 2>&1; then
        echo "   UV 版本: $(uv --version)"
        echo "   UV 路径: $(which uv)"
    else
        echo "   ⚠️ UV 未安装"
    fi
    echo ""

    echo -e "${CYAN}🔒 虚拟环境:${NC}"
    if [ -n "$VIRTUAL_ENV" ]; then
        echo "   状态: ✅ 已激活"
        echo "   路径: $VIRTUAL_ENV"
        echo "   Python: $(which python)"
    else
        echo "   状态: ⚠️ 未激活"
        if [ -d "$PROJECT_ROOT/.venv" ]; then
            echo "   .venv 目录: ✅ 存在"
        else
            echo "   .venv 目录: ❌ 不存在"
        fi
    fi
    echo ""

    echo -e "${CYAN}📁 项目结构:${NC}"
    echo "   项目根目录: $PROJECT_ROOT"
    echo "   源码目录: $([ -d "$PROJECT_ROOT/src" ] && echo "✅ 存在" || echo "❌ 缺失")"
    echo "   测试目录: $([ -d "$PROJECT_ROOT/tests" ] && echo "✅ 存在" || echo "⚠️ 不存在")"
    echo "   文档目录: $([ -d "$PROJECT_ROOT/docs" ] && echo "✅ 存在" || echo "⚠️ 不存在")"
    echo "   开发脚本: $([ -d "$SCRIPT_DIR" ] && echo "✅ 存在" || echo "❌ 缺失")"
    echo ""

    echo -e "${CYAN}📊 资源使用:${NC}"
    echo "   磁盘使用: $(df -h "$PROJECT_ROOT" | tail -1 | awk '{print $5 " (" $4 " 可用)"}')"
    if command -v free >/dev/null 2>&1; then
        echo "   内存使用: $(free -h | grep '^Mem:' | awk '{print $3 "/" $2}')"
    fi
    echo ""

    read -p "按回车键返回主菜单..."
}

# 清理和维护
cleanup_maintenance() {
    clear_screen
    echo -e "${WHITE}🧹 清理和维护${NC}"
    echo ""

    echo -e "${CYAN}选择清理选项:${NC}"
    echo -e "   ${GREEN}1.${NC} 🗑️ 清理日志文件"
    echo -e "   ${GREEN}2.${NC} 🔄 清理Python缓存"
    echo -e "   ${GREEN}3.${NC} 📦 清理pip缓存"
    echo -e "   ${GREEN}4.${NC} 🧪 清理测试文件"
    echo -e "   ${GREEN}5.${NC} 💥 完全清理 (谨慎使用)"
    echo -e "   ${YELLOW}0.${NC} 🔙 返回主菜单"
    echo ""

    read -p "请选择操作 (0-5): " cleanup_choice

    case $cleanup_choice in
        1)
            cleanup_logs
            ;;
        2)
            cleanup_python_cache
            ;;
        3)
            cleanup_pip_cache
            ;;
        4)
            cleanup_test_files
            ;;
        5)
            full_cleanup
            ;;
        0)
            return 0
            ;;
        *)
            echo -e "${RED}❌ 无效选择${NC}"
            ;;
    esac

    echo ""
    read -p "按回车键继续..."
}

# 清理日志文件
cleanup_logs() {
    cd "$PROJECT_ROOT"

    echo "🗑️ 清理日志文件..."

    if [ -d "dev-logs" ]; then
        log_count=$(ls -1 dev-logs/*.log 2>/dev/null | wc -l || echo 0)
        if [ $log_count -gt 0 ]; then
            echo "找到 $log_count 个日志文件"
            read -p "确认删除所有日志文件? (y/N): " confirm
            if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
                rm -f dev-logs/*.log dev-logs/*.tmp
                echo -e "${GREEN}✅ 日志文件清理完成${NC}"
            fi
        else
            echo "没有找到日志文件"
        fi
    fi
}

# 清理Python缓存
cleanup_python_cache() {
    cd "$PROJECT_ROOT"

    echo "🔄 清理Python缓存..."

    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    find . -name "*.pyc" -delete 2>/dev/null || true
    find . -name "*.pyo" -delete 2>/dev/null || true

    echo -e "${GREEN}✅ Python缓存清理完成${NC}"
}

# 清理pip缓存
cleanup_pip_cache() {
    echo "📦 清理pip缓存..."

    if command -v pip >/dev/null 2>&1; then
        pip cache purge 2>/dev/null || true
        echo -e "${GREEN}✅ Pip缓存清理完成${NC}"
    fi

    if command -v uv >/dev/null 2>&1; then
        uv cache clean 2>/dev/null || true
        echo -e "${GREEN}✅ UV缓存清理完成${NC}"
    fi
}

# 清理测试文件
cleanup_test_files() {
    cd "$PROJECT_ROOT"

    echo "🧪 清理测试文件..."

    test_dirs=("test-content" "test-results" "benchmark-content")

    for dir in "${test_dirs[@]}"; do
        if [ -d "$dir" ]; then
            file_count=$(find "$dir" -type f | wc -l)
            if [ $file_count -gt 0 ]; then
                echo "在 $dir 中找到 $file_count 个文件"
                read -p "清理 $dir 目录? (y/N): " confirm
                if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
                    rm -rf "$dir"/*
                    echo "✅ $dir 清理完成"
                fi
            fi
        fi
    done

    echo -e "${GREEN}✅ 测试文件清理完成${NC}"
}

# 完全清理
full_cleanup() {
    echo -e "${RED}💥 完全清理模式${NC}"
    echo -e "${YELLOW}⚠️ 这将删除所有日志、缓存、测试文件和虚拟环境${NC}"
    read -p "确认执行完全清理? (输入 'YES' 确认): " confirm

    if [ "$confirm" = "YES" ]; then
        cleanup_logs
        cleanup_python_cache
        cleanup_pip_cache
        cleanup_test_files

        # 删除虚拟环境
        if [ -d "$PROJECT_ROOT/.venv" ]; then
            rm -rf "$PROJECT_ROOT/.venv"
            echo "✅ 虚拟环境已删除"
        fi

        echo -e "${GREEN}✅ 完全清理完成${NC}"
        echo -e "${YELLOW}💡 建议重新运行环境初始化${NC}"
    else
        echo "已取消完全清理"
    fi
}

# 显示使用指南
show_usage_guide() {
    clear_screen
    echo -e "${WHITE}📖 TextUp 开发测试使用指南${NC}"
    echo ""

    echo -e "${CYAN}🎯 快速开始流程:${NC}"
    echo "   1. 🔧 首次使用: 选择菜单 '6 - 环境配置管理' → '1 - 初始化开发环境'"
    echo "   2. 🚀 快速验证: 选择菜单 '1 - 快速测试' 确保基础功能正常"
    echo "   3. 🧪 功能测试: 选择菜单 '2 - 完整功能测试' 验证所有功能"
    echo "   4. 📊 查看结果: 选择菜单 '4 - 日志分析' 查看测试报告"
    echo ""

    echo -e "${CYAN}🛠️ 开发工作流:${NC}"
    echo "   📝 修改代码 → 🚀 快速测试 → 🔍 查看日志 → 🔄 重复流程"
    echo ""

    echo -e "${CYAN}📁 重要目录说明:${NC}"
    echo "   dev-logs/     - 开发日志和测试报告"
    echo "   dev-config/   - 开发配置文件"
    echo "   test-content/ - 测试文章和内容"
    echo "   test-results/ - 测试结果和报告"
    echo ""

    echo -e "${CYAN}🔧 直接运行方式:${NC}"
    echo "   uv run python -m textup.cli --version"
    echo "   uv run python -m textup.cli --debug config --list"
    echo "   uv run python -m textup.cli publish --file test.md --platform weibo --dry-run"
    echo ""

    echo -e "${CYAN}💡 最佳实践建议:${NC}"
    echo "   • 在虚拟环境中开发: source .venv/bin/activate"
    echo "   • 每次修改后运行快速测试验证基础功能"
    echo "   • 使用 --debug 参数获取详细日志信息"
    echo "   • 定期运行完整测试确保功能完整性"
    echo "   • 使用日志分析工具跟踪性能和错误"
    echo ""

    read -p "按回车键返回主菜单..."
}

# 显示帮助信息
show_help() {
    clear_screen
    echo -e "${WHITE}🆘 帮助信息${NC}"
    echo ""

    echo -e "${CYAN}📞 常见问题解决:${NC}"
    echo ""

    echo -e "${YELLOW}Q: 提示 'textup: command not found'${NC}"
    echo -e "A: 1. 检查虚拟环境是否激活: source .venv/bin/activate"
    echo -e "   2. 重新安装项目: uv pip install -e ."
    echo -e "   3. 使用直接运行方式: uv run python -m textup.cli"
    echo ""

    echo -e "${YELLOW}Q: 模块导入失败${NC}"
    echo -e "A: 1. 检查项目结构是否完整"
    echo -e "   2. 确认在项目根目录运行"
    echo -e "   3. 重新安装依赖: uv pip install -e ."
    echo ""

    echo -e "${YELLOW}Q: 测试失败或超时${NC}"
    echo -e "A: 1. 检查网络连接"
    echo -e "   2. 运行故障排查: 菜单选择 '3'"
    echo -e "   3. 查看详细日志: dev-logs/textup-dev.log"
    echo ""

    echo -e "${YELLOW}Q: 性能测试很慢${NC}"
    echo -e "A: 1. 这是正常现象，完整测试需要5-10分钟"
    echo -e "   2. 可以使用快速测试进行基础验证"
    echo -e "   3. 单独测试特定功能模块"
    echo ""

    echo -e "${CYAN}📱 联系方式:${NC}"
    echo -e "   🔗 项目仓库: https://github.com/textup-team/textup"
    echo -e "   📧 问题反馈: 通过GitHub Issues提交"
    echo -e "   📖 详细文档: docs/ 目录下的文档"
    echo ""

    echo -e "${CYAN}🛠️ 高级用法:${NC}"
    echo -e "   # 直接运行脚本"
    echo -e "   ./dev-scripts/quick-test.sh"
    echo -e "   ./dev-scripts/test-all-platforms.sh"
    echo -e "   ./dev-scripts/troubleshoot.sh"
    echo -e "   ./dev-scripts/analyze-logs.sh"
    echo ""
    echo -e "   # 带参数运行"
    echo -e "   ./dev-scripts/analyze-logs.sh --interactive"
    echo -e "   ./dev-scripts/dev-test-master.sh --help"
    echo ""

    read -p "按回车键返回主菜单..."
}

# 处理开发工具菜单
handle_dev_menu() {
    while true; do
        show_dev_menu
        read -p "请选择操作 (0-6): " dev_choice

        case $dev_choice in
            1)
                echo -e "${CYAN}🔄 启动热重载开发模式...${NC}"
                echo -e "${YELLOW}功能开发中，敬请期待${NC}"
                read -p "按回车键继续..."
                ;;
            2)
                echo -e "${CYAN}🐛 启动调试模式...${NC}"
                echo -e "${YELLOW}功能开发中，敬请期待${NC}"
                read -p "按回车键继续..."
                ;;
            3)
                echo -e "${CYAN}📝 创建测试内容...${NC}"
                create_test_content
                ;;
            4)
                echo -e "${CYAN}🧪 单平台测试...${NC}"
                single_platform_test
                ;;
            5)
                echo -e "${CYAN}⚡ 性能基准测试...${NC}"
                performance_benchmark
                ;;
            6)
                echo -e "${CYAN}🔧 代码质量检查...${NC}"
                code_quality_check
                ;;
            0)
                break
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请重试${NC}"
                sleep 1
                ;;
        esac
    done
}

# 创建测试内容
create_test_content() {
    cd "$PROJECT_ROOT"
    mkdir -p test-content

    echo "正在创建测试内容..."

    # 创建基础测试文章
    cat > test-content/basic-test.md << 'EOF'
# TextUp 基础功能测试

## 测试目的
验证TextUp的基本发布功能。

## 内容特性测试

### 文本格式
- **粗体文本**
- *斜体文本*
- `行内代码`

### 列表功能
1. 有序列表项1
2. 有序列表项2
3. 有序列表项3

- 无序列表项A
- 无序列表项B
- 无序列表项C

### 代码块
```python
def hello_textup():
    print("Hello from TextUp!")
    return "success"
```

### 链接测试
访问 [TextUp项目](https://github.com/textup-team/textup) 了解更多。

**测试标签**: #TextUp #测试 #开发 #多平台发布

生成时间: $(date)
EOF

    echo "✅ 测试内容创建完成"
    echo "   位置: test-content/"
    read -p "按回车键继续..."
}

# 单平台测试
single_platform_test() {
    echo "选择要测试的平台:"
    echo "1. 微博 (Weibo)"
    echo "2. 知乎 (Zhihu)"
    echo "3. 今日头条 (Toutiao)"
    echo "0. 取消"

    read -p "请选择 (0-3): " platform_choice

    case $platform_choice in
        1)
            test_platform "weibo"
            ;;
        2)
            test_platform "zhihu"
            ;;
        3)
            test_platform "toutiao"
            ;;
        0)
            return 0
            ;;
        *)
            echo -e "${RED}❌ 无效选择${NC}"
            return 1
            ;;
    esac
}

# 测试指定平台
test_platform() {
    local platform="$1"
    echo -e "${CYAN}🧪 测试 $platform 平台...${NC}"

    # 确保测试内容存在
    if [ ! -f "test-content/basic-test.md" ]; then
        echo "创建测试内容..."
        create_test_content
    fi

    # 运行平台特定测试
    echo "1. 配置验证..."
    timeout 15s uv run python -m textup.cli --debug config --get "platforms.$platform" || true

    echo "2. 内容验证..."
    timeout 20s uv run python -m textup.cli --debug validate --file test-content/basic-test.md --platform "$platform" || true

    echo "3. 内容预览..."
    timeout 25s uv run python -m textup.cli --debug preview --file test-content/basic-test.md --platform "$platform" || true

    echo "4. 模拟发布..."
    timeout 30s uv run python -m textup.cli --debug publish --file test-content/basic-test.md --platform "$platform" --dry-run || true

    echo -e "${GREEN}✅ $platform 平台测试完成${NC}"
    read -p "按回车键继续..."
}

# 性能基准测试
performance_benchmark() {
    echo -e "${CYAN}⚡ 运行性能基准测试...${NC}"
    echo "这可能需要几分钟时间..."

    # 创建基准测试内容
    mkdir -p benchmark-content
    for i in {1..5}; do
        cp test-content/basic-test.md "benchmark-content/test-$i.md" 2>/dev/null || echo "# Test $i" > "benchmark-content/test-$i.md"
    done

    echo "1. 单文件处理速度测试..."
    time timeout 30s uv run python -m textup.cli publish --file benchmark-content/test-1.md --platform weibo --dry-run 2>&1 || true

    echo -e "\n2. 批量处理速度测试..."
    time timeout 60s uv run python -m textup.cli publish --directory benchmark-content --platform weibo --dry-run 2>&1 || true

    # 清理
    rm -rf benchmark-content

    echo -e "${GREEN}✅ 性能基准测试完成${NC}"
    read -p "按回车键继续..."
}

# 代码质量检查
code_quality_check() {
    cd "$PROJECT_ROOT"

    echo -e "${CYAN}🔧 代码质量检查...${NC}"

    echo "1. Python语法检查..."
    python -m py_compile src/textup/cli/main.py 2>&1 || true

    echo "2. 模块导入检查..."
    python -c "import sys; sys.path.append('src'); import textup" 2>&1 || true

    echo "3. 基础功能检查..."
    timeout 10s uv run python -m textup.cli --version 2>&1 || true

    if command -v flake8 >/dev/null 2>&1; then
        echo "4. 代码风格检查..."
        flake8 src/textup --max-line-length=88 --ignore=E203,W503 2>&1 || true
    else
        echo "4. 代码风格检查... (跳过 - flake8未安装)"
    fi

    echo -e "${GREEN}✅ 代码质量检查完成${NC}"
    read -p "按回车键继续..."
}

# 检查运行环境
check_prerequisites() {
    local errors=0

    # 检查是否在正确目录
    if [ ! -f "pyproject.toml" ]; then
        echo -e "${RED}❌ 错误: 请在TextUp项目根目录运行此脚本${NC}"
        echo "当前目录: $(pwd)"
        echo "期望文件: pyproject.toml"
        exit 1
    fi

    # 检查Python
    if ! command -v python >/dev/null 2>&1; then
        echo -e "${RED}❌ Python未安装或未在PATH中${NC}"
        ((errors++))
    fi

    # 创建必要目录
    mkdir -p dev-logs dev-config test-content test-results

    if [ $errors -gt 0 ]; then
        echo -e "${RED}发现 $errors 个环境问题，请先解决${NC}"
        echo "建议运行: 菜单选择 '6 - 环境配置管理' → '1 - 初始化开发环境'"
        read -p "是否继续? (y/N): " continue_anyway
        if [ "$continue_anyway" != "y" ] && [ "$continue_anyway" != "Y" ]; then
            exit 1
        fi
    fi
}

# 主程序循环
main() {
    # 检查命令行参数
    if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
        echo "TextUp 开发测试主控台"
        echo ""
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  --help, -h     显示帮助信息"
        echo "  --quick        直接运行快速测试"
        echo "  --full         直接运行完整测试"
        echo "  --troubleshoot 直接运行故障排查"
        echo ""
        exit 0
    fi

    # 直接运行模式
    case "$1" in
        --quick)
            check_prerequisites
            run_quick_test
            exit $?
            ;;
        --full)
            check_prerequisites
            run_full_test
            exit $?
            ;;
        --troubleshoot)
            check_prerequisites
            run_troubleshoot
            exit $?
            ;;
    esac

    # 检查环境
    check_prerequisites

    # 主菜单循环
    while true; do
        clear_screen
        show_status
        show_main_menu

        read -p "请选择操作 (0-10): " choice

        case $choice in
            1)
                run_quick_test
                ;;
            2)
                run_full_test
                ;;
            3)
                run_troubleshoot
                ;;
            4)
                run_log_analysis
                ;;
            5)
                handle_dev_menu
                ;;
            6)
                manage_environment
                ;;
            7)
                show_system_status
                ;;
            8)
                cleanup_maintenance
                ;;
            9)
                show_usage_guide
                ;;
            10)
                show_help
                ;;
            0)
                clear_screen
                echo -e "${CYAN}👋 感谢使用 TextUp 开发测试工具！${NC}"
                echo -e "${GREEN}祝您开发愉快！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请输入 0-10 之间的数字${NC}"
                sleep 2
                ;;
        esac
    done
}

# 信号处理
cleanup_on_exit() {
    echo -e "\n${YELLOW}🧹 正在清理...${NC}"
    # 清理临时文件
    find "$PROJECT_ROOT" -name "*.tmp" -delete 2>/dev/null || true
    echo -e "${GREEN}✅ 清理完成${NC}"
}

trap cleanup_on_exit EXIT INT TERM

# 脚本入口点
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
