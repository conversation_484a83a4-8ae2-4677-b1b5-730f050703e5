#!/bin/bash

echo "📊 TextUp 日志分析工具"
echo "===================="
echo "分析时间: $(date)"
echo ""

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志文件路径
MAIN_LOG="dev-logs/textup-dev.log"
ERROR_LOG="dev-logs/textup-errors.log"
LOG_DIR="dev-logs"
REPORT_FILE="$LOG_DIR/log-analysis-$(date +%Y%m%d-%H%M%S).md"

# 统计变量
TOTAL_LINES=0
DEBUG_COUNT=0
INFO_COUNT=0
WARNING_COUNT=0
ERROR_COUNT=0
CRITICAL_COUNT=0

# 检查日志文件是否存在
check_log_files() {
    echo "🔍 检查日志文件..."

    if [ ! -d "$LOG_DIR" ]; then
        echo -e "${RED}❌ 日志目录不存在: $LOG_DIR${NC}"
        echo "请先运行一些TextUp命令生成日志，或执行: mkdir -p $LOG_DIR"
        exit 1
    fi

    if [ ! -f "$MAIN_LOG" ]; then
        echo -e "${YELLOW}⚠️ 主日志文件不存在: $MAIN_LOG${NC}"
        echo "请先运行一些TextUp命令生成日志"
        # 创建空的日志文件用于分析
        touch "$MAIN_LOG"
    else
        echo -e "${GREEN}✅ 主日志文件: $MAIN_LOG${NC}"
    fi

    if [ ! -f "$ERROR_LOG" ]; then
        echo -e "${YELLOW}⚠️ 错误日志文件不存在: $ERROR_LOG${NC}"
        touch "$ERROR_LOG"
    else
        echo -e "${GREEN}✅ 错误日志文件: $ERROR_LOG${NC}"
    fi

    echo ""
}

# 基础统计分析
analyze_basic_stats() {
    echo "📈 基础统计分析"
    echo "==============="

    if [ -s "$MAIN_LOG" ]; then
        TOTAL_LINES=$(wc -l < "$MAIN_LOG")
        DEBUG_COUNT=$(grep -c "DEBUG" "$MAIN_LOG" 2>/dev/null || echo 0)
        INFO_COUNT=$(grep -c "INFO" "$MAIN_LOG" 2>/dev/null || echo 0)
        WARNING_COUNT=$(grep -c "WARNING" "$MAIN_LOG" 2>/dev/null || echo 0)
        ERROR_COUNT=$(grep -c "ERROR" "$MAIN_LOG" 2>/dev/null || echo 0)
        CRITICAL_COUNT=$(grep -c "CRITICAL" "$MAIN_LOG" 2>/dev/null || echo 0)

        echo "📋 日志文件基础信息:"
        echo "   文件大小: $(du -h "$MAIN_LOG" | cut -f1)"
        echo "   总行数: $TOTAL_LINES"
        echo "   创建时间: $(stat -c %y "$MAIN_LOG" 2>/dev/null || stat -f %Sm "$MAIN_LOG" 2>/dev/null || echo "未知")"
        echo "   最后修改: $(stat -c %y "$MAIN_LOG" 2>/dev/null || stat -f %Sm "$MAIN_LOG" 2>/dev/null || echo "未知")"
        echo ""

        echo "📊 日志级别分布:"
        printf "   ${BLUE}DEBUG${NC}:    %6d 条 (%.1f%%)\n" $DEBUG_COUNT $(echo "scale=1; $DEBUG_COUNT * 100.0 / $TOTAL_LINES" | bc -l 2>/dev/null || echo "0.0")
        printf "   ${GREEN}INFO${NC}:     %6d 条 (%.1f%%)\n" $INFO_COUNT $(echo "scale=1; $INFO_COUNT * 100.0 / $TOTAL_LINES" | bc -l 2>/dev/null || echo "0.0")
        printf "   ${YELLOW}WARNING${NC}:  %6d 条 (%.1f%%)\n" $WARNING_COUNT $(echo "scale=1; $WARNING_COUNT * 100.0 / $TOTAL_LINES" | bc -l 2>/dev/null || echo "0.0")
        printf "   ${RED}ERROR${NC}:    %6d 条 (%.1f%%)\n" $ERROR_COUNT $(echo "scale=1; $ERROR_COUNT * 100.0 / $TOTAL_LINES" | bc -l 2>/dev/null || echo "0.0")
        printf "   ${PURPLE}CRITICAL${NC}: %6d 条 (%.1f%%)\n" $CRITICAL_COUNT $(echo "scale=1; $CRITICAL_COUNT * 100.0 / $TOTAL_LINES" | bc -l 2>/dev/null || echo "0.0")
        echo ""
    else
        echo "📋 主日志文件为空"
        echo ""
    fi

    # 错误日志统计
    if [ -s "$ERROR_LOG" ]; then
        error_lines=$(wc -l < "$ERROR_LOG")
        error_size=$(du -h "$ERROR_LOG" | cut -f1)
        echo "🚨 错误日志统计:"
        echo "   错误日志大小: $error_size"
        echo "   错误条目数: $error_lines"
        echo ""
    fi
}

# 时间分布分析
analyze_time_distribution() {
    echo "⏰ 时间分布分析"
    echo "==============="

    if [ ! -s "$MAIN_LOG" ]; then
        echo "主日志文件为空，跳过时间分析"
        echo ""
        return
    fi

    echo "📅 按日期分布 (最近7天):"
    for i in {0..6}; do
        date_pattern=$(date -d "$i days ago" +"%Y-%m-%d" 2>/dev/null || date -v-${i}d +"%Y-%m-%d" 2>/dev/null || echo "$(date +%Y-%m-%d)")
        count=$(grep "$date_pattern" "$MAIN_LOG" 2>/dev/null | wc -l)
        if [ $count -gt 0 ]; then
            printf "   %s: %4d 条\n" "$date_pattern" $count
        fi
    done
    echo ""

    echo "🕐 按小时分布 (今天):"
    today=$(date +"%Y-%m-%d")
    for hour in {00..23}; do
        count=$(grep "$today $hour:" "$MAIN_LOG" 2>/dev/null | wc -l)
        if [ $count -gt 0 ]; then
            printf "   %s时: %3d 条\n" "$hour" $count
        fi
    done
    echo ""
}

# 错误分析
analyze_errors() {
    echo "🚨 错误和异常分析"
    echo "================="

    if [ ! -s "$MAIN_LOG" ]; then
        echo "主日志文件为空，跳过错误分析"
        echo ""
        return
    fi

    # 最近的错误
    echo "📋 最近的错误 (最新10条):"
    if [ $ERROR_COUNT -gt 0 ] || [ $CRITICAL_COUNT -gt 0 ]; then
        grep -E "(ERROR|CRITICAL)" "$MAIN_LOG" | tail -10 | while read -r line; do
            echo "   🔸 $line"
        done
    else
        echo "   ✅ 没有发现错误日志"
    fi
    echo ""

    # 最频繁的错误
    echo "📊 最频繁的错误类型 (Top 5):"
    if [ $ERROR_COUNT -gt 0 ]; then
        grep "ERROR" "$MAIN_LOG" | sed 's/.*ERROR[[:space:]]*-[[:space:]]*//' | sed 's/:.*$//' | sort | uniq -c | sort -rn | head -5 | while read -r count error; do
            printf "   %3d 次: %s\n" "$count" "$error"
        done
    else
        echo "   ✅ 没有发现错误"
    fi
    echo ""

    # 警告分析
    echo "⚠️ 警告信息分析 (最新5条):"
    if [ $WARNING_COUNT -gt 0 ]; then
        grep "WARNING" "$MAIN_LOG" | tail -5 | while read -r line; do
            echo "   🔸 $line"
        done
    else
        echo "   ✅ 没有发现警告"
    fi
    echo ""
}

# 功能使用分析
analyze_functionality() {
    echo "🔧 功能使用分析"
    echo "==============="

    if [ ! -s "$MAIN_LOG" ]; then
        echo "主日志文件为空，跳过功能分析"
        echo ""
        return
    fi

    # 命令使用统计
    echo "📊 命令使用统计:"
    commands=("config" "auth" "publish" "validate" "preview" "status" "history")

    for cmd in "${commands[@]}"; do
        count=$(grep -i "$cmd" "$MAIN_LOG" 2>/dev/null | wc -l)
        if [ $count -gt 0 ]; then
            printf "   %s: %4d 次\n" "$cmd" $count
        fi
    done
    echo ""

    # 平台使用统计
    echo "🌐 平台使用统计:"
    platforms=("weibo" "zhihu" "toutiao" "xiaohongshu")

    for platform in "${platforms[@]}"; do
        count=$(grep -i "$platform" "$MAIN_LOG" 2>/dev/null | wc -l)
        if [ $count -gt 0 ]; then
            printf "   %s: %4d 次\n" "$platform" $count
        fi
    done
    echo ""

    # API调用统计
    echo "📡 API调用统计:"
    api_count=$(grep -E "(HTTP|API|Request|Response)" "$MAIN_LOG" 2>/dev/null | wc -l)
    success_count=$(grep -E "(200|201|202|204)" "$MAIN_LOG" 2>/dev/null | wc -l)
    error_count=$(grep -E "(400|401|403|404|500|502|503|504)" "$MAIN_LOG" 2>/dev/null | wc -l)

    echo "   总API调用: $api_count 次"
    echo "   成功响应: $success_count 次"
    echo "   错误响应: $error_count 次"

    if [ $api_count -gt 0 ]; then
        success_rate=$(echo "scale=1; $success_count * 100.0 / $api_count" | bc -l 2>/dev/null || echo "0.0")
        echo "   成功率: $success_rate%"
    fi
    echo ""
}

# 性能分析
analyze_performance() {
    echo "⚡ 性能分析"
    echo "=========="

    if [ ! -s "$MAIN_LOG" ]; then
        echo "主日志文件为空，跳过性能分析"
        echo ""
        return
    fi

    # 响应时间分析
    echo "⏱️ 响应时间分析:"

    # 查找包含时间信息的日志
    response_times=$(grep -oE "[0-9]+\.?[0-9]*ms" "$MAIN_LOG" 2>/dev/null | sed 's/ms//' | head -100)

    if [ -n "$response_times" ]; then
        # 计算平均响应时间
        avg_time=$(echo "$response_times" | awk '{sum+=$1; count++} END {print (count>0 ? sum/count : 0)}')
        max_time=$(echo "$response_times" | sort -n | tail -1)
        min_time=$(echo "$response_times" | sort -n | head -1)

        printf "   平均响应时间: %.2f ms\n" "$avg_time"
        printf "   最快响应时间: %.2f ms\n" "$min_time"
        printf "   最慢响应时间: %.2f ms\n" "$max_time"

        # 响应时间分布
        echo ""
        echo "📊 响应时间分布:"
        echo "$response_times" | awk '
        {
            if ($1 < 100) fast++
            else if ($1 < 500) medium++
            else if ($1 < 1000) slow++
            else very_slow++
            total++
        }
        END {
            printf "   < 100ms (快速):   %3d 次 (%.1f%%)\n", fast, (fast*100.0/total)
            printf "   100-500ms (正常): %3d 次 (%.1f%%)\n", medium, (medium*100.0/total)
            printf "   500-1000ms (慢):  %3d 次 (%.1f%%)\n", slow, (slow*100.0/total)
            printf "   > 1000ms (很慢):  %3d 次 (%.1f%%)\n", very_slow, (very_slow*100.0/total)
        }'
    else
        echo "   未找到响应时间数据"
    fi
    echo ""

    # 内存使用分析
    echo "💾 资源使用分析:"
    memory_warnings=$(grep -i "memory\|内存" "$MAIN_LOG" 2>/dev/null | wc -l)
    cpu_warnings=$(grep -i "cpu\|处理器" "$MAIN_LOG" 2>/dev/null | wc -l)

    echo "   内存相关日志: $memory_warnings 条"
    echo "   CPU相关日志: $cpu_warnings 条"
    echo ""
}

# 趋势分析
analyze_trends() {
    echo "📈 趋势分析"
    echo "=========="

    if [ ! -s "$MAIN_LOG" ]; then
        echo "主日志文件为空，跳过趋势分析"
        echo ""
        return
    fi

    # 活跃度趋势
    echo "📊 活跃度趋势 (最近7天):"
    for i in {6..0}; do
        date_pattern=$(date -d "$i days ago" +"%Y-%m-%d" 2>/dev/null || date -v-${i}d +"%Y-%m-%d" 2>/dev/null)
        if [ -n "$date_pattern" ]; then
            count=$(grep "$date_pattern" "$MAIN_LOG" 2>/dev/null | wc -l)
            errors=$(grep "$date_pattern" "$MAIN_LOG" 2>/dev/null | grep -c "ERROR" || echo 0)

            if [ $count -gt 0 ]; then
                bar=""
                bar_length=$((count / 10))  # 简单的条形图
                for ((j=1; j<=bar_length; j++)); do
                    bar="$bar█"
                done

                printf "   %s: %4d 条 %s" "$date_pattern" $count "$bar"
                if [ $errors -gt 0 ]; then
                    printf " (${RED}%d 错误${NC})" $errors
                fi
                echo ""
            fi
        fi
    done
    echo ""

    # 错误趋势
    echo "🚨 错误趋势分析:"
    current_errors=$ERROR_COUNT

    # 尝试找到历史数据进行比较
    if [ -f "$LOG_DIR/last_error_count.tmp" ]; then
        last_errors=$(cat "$LOG_DIR/last_error_count.tmp")
        if [ $current_errors -gt $last_errors ]; then
            echo -e "   ${RED}📈 错误数量增加: $last_errors → $current_errors (+$((current_errors - last_errors)))${NC}"
        elif [ $current_errors -lt $last_errors ]; then
            echo -e "   ${GREEN}📉 错误数量减少: $last_errors → $current_errors (-$((last_errors - current_errors)))${NC}"
        else
            echo -e "   ${BLUE}➡️ 错误数量保持: $current_errors${NC}"
        fi
    else
        echo "   📋 首次分析，建立基线数据"
    fi

    # 保存当前错误计数供下次比较
    echo $current_errors > "$LOG_DIR/last_error_count.tmp"
    echo ""
}

# 建议和优化
generate_recommendations() {
    echo "💡 建议和优化"
    echo "============"

    recommendations=()

    # 基于统计数据生成建议
    if [ $ERROR_COUNT -gt 10 ]; then
        recommendations+=("🔴 错误数量较多($ERROR_COUNT)，建议重点关注错误处理和代码质量")
    fi

    if [ $WARNING_COUNT -gt 50 ]; then
        recommendations+=("🟡 警告数量较多($WARNING_COUNT)，建议优化代码逻辑减少警告")
    fi

    if [ $DEBUG_COUNT -gt 1000 ]; then
        recommendations+=("🔵 DEBUG日志过多($DEBUG_COUNT)，生产环境建议调整日志级别")
    fi

    # 性能相关建议
    response_times=$(grep -oE "[0-9]+\.?[0-9]*ms" "$MAIN_LOG" 2>/dev/null | sed 's/ms//')
    if [ -n "$response_times" ]; then
        max_time=$(echo "$response_times" | sort -n | tail -1)
        if [ "${max_time%.*}" -gt 1000 ]; then  # 大于1秒
            recommendations+=("⚡ 发现较慢的操作(${max_time}ms)，建议优化性能")
        fi
    fi

    # 文件大小建议
    if [ -s "$MAIN_LOG" ]; then
        log_size_kb=$(du -k "$MAIN_LOG" | cut -f1)
        if [ $log_size_kb -gt 10240 ]; then  # 大于10MB
            recommendations+=("📁 日志文件较大(${log_size_kb}KB)，建议定期清理或配置日志轮转")
        fi
    fi

    # 显示建议
    if [ ${#recommendations[@]} -gt 0 ]; then
        for rec in "${recommendations[@]}"; do
            echo "   $rec"
        done
    else
        echo -e "   ${GREEN}✅ 系统运行状况良好，暂无特别建议${NC}"
    fi
    echo ""

    # 维护建议
    echo "🛠️ 日常维护建议:"
    echo "   • 定期清理过大的日志文件"
    echo "   • 监控错误和警告的变化趋势"
    echo "   • 在生产环境中调整合适的日志级别"
    echo "   • 对频繁出现的警告进行专项优化"
    echo "   • 建立日志监控告警机制"
    echo ""
}

# 生成报告
generate_report() {
    echo "📄 生成详细报告"
    echo "==============="

    {
        echo "# TextUp 日志分析报告"
        echo ""
        echo "**生成时间**: $(date)"
        echo "**分析工具**: TextUp Log Analyzer"
        echo ""
        echo "## 📊 统计概览"
        echo ""
        echo "| 指标 | 数值 |"
        echo "|------|------|"
        echo "| 总日志行数 | $TOTAL_LINES |"
        echo "| DEBUG 日志 | $DEBUG_COUNT |"
        echo "| INFO 日志 | $INFO_COUNT |"
        echo "| WARNING 日志 | $WARNING_COUNT |"
        echo "| ERROR 日志 | $ERROR_COUNT |"
        echo "| CRITICAL 日志 | $CRITICAL_COUNT |"
        echo ""
        echo "## 🚨 错误分析"
        echo ""
        if [ $ERROR_COUNT -gt 0 ]; then
            echo "### 最近错误日志"
            echo '```'
            grep -E "(ERROR|CRITICAL)" "$MAIN_LOG" 2>/dev/null | tail -5
            echo '```'
        else
            echo "✅ 未发现错误日志"
        fi
        echo ""
        echo "## 📈 使用情况"
        echo ""
        echo "### 功能使用统计"
        commands=("config" "auth" "publish" "validate" "preview")
        for cmd in "${commands[@]}"; do
            count=$(grep -i "$cmd" "$MAIN_LOG" 2>/dev/null | wc -l)
            if [ $count -gt 0 ]; then
                echo "- $cmd: $count 次"
            fi
        done
        echo ""
        echo "### 平台使用统计"
        platforms=("weibo" "zhihu" "toutiao")
        for platform in "${platforms[@]}"; do
            count=$(grep -i "$platform" "$MAIN_LOG" 2>/dev/null | wc -l)
            if [ $count -gt 0 ]; then
                echo "- $platform: $count 次"
            fi
        done
        echo ""
        echo "## 💡 建议"
        echo ""
        if [ $ERROR_COUNT -gt 5 ]; then
            echo "- 🔴 错误数量较多，建议重点关注错误处理"
        fi
        if [ $WARNING_COUNT -gt 20 ]; then
            echo "- 🟡 警告较多，建议优化代码逻辑"
        fi
        if [ $ERROR_COUNT -eq 0 ] && [ $CRITICAL_COUNT -eq 0 ]; then
            echo "- ✅ 系统运行状况良好"
        fi
        echo ""
        echo "## 📋 维护建议"
        echo ""
        echo "1. 定期清理日志文件"
        echo "2. 监控错误趋势变化"
        echo "3. 优化性能瓶颈"
        echo "4. 建立监控告警"
        echo ""
        echo "---"
        echo "报告生成于: $(date)"
    } > "$REPORT_FILE"

    echo "详细报告已保存到: $REPORT_FILE"
    echo ""
}

# 交互式查询
interactive_query() {
    if [ "$1" = "--interactive" ]; then
        echo "🔍 交互式日志查询"
        echo "=================="
        echo "可用命令:"
        echo "1. 搜索关键词"
        echo "2. 查看错误详情"
        echo "3. 查看性能数据"
        echo "4. 退出"
        echo ""

        while true; do
            read -p "请选择操作 (1-4): " choice
            case $choice in
                1)
                    read -p "请输入搜索关键词: " keyword
                    echo "搜索结果:"
                    grep -i "$keyword" "$MAIN_LOG" 2>/dev/null | head -10
                    echo ""
                    ;;
                2)
                    echo "最新错误详情:"
                    grep "ERROR" "$MAIN_LOG" 2>/dev/null | tail -5
                    echo ""
                    ;;
                3)
                    echo "性能相关数据:"
                    grep -E "ms|time|duration" "$MAIN_LOG" 2>/dev/null | tail -10
                    echo ""
                    ;;
                4)
                    echo "退出交互模式"
                    break
                    ;;
                *)
                    echo "无效选择，请重试"
                    ;;
            esac
        done
    fi
}

# 主函数
main() {
    # 检查命令行参数
    if [ "$1" = "--help" ]; then
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  --interactive    启用交互式查询模式"
        echo "  --help          显示帮助信息"
        echo ""
        echo "示例:"
        echo "  $0                # 运行完整分析"
        echo "  $0 --interactive  # 启用交互模式"
        exit 0
    fi

    # 执行分析
    check_log_files
    analyze_basic_stats
    analyze_time_distribution
    analyze_errors
    analyze_functionality
    analyze_performance
    analyze_trends
    generate_recommendations
    generate_report

    # 交互式查询
    interactive_query "$1"

    echo "🎉 日志分析完成！"
    echo ""
    echo "📄 报告文件: $REPORT_FILE"
    echo "🔍 主日志: $MAIN_LOG"
    echo "🚨 错误日志: $ERROR_LOG"
    echo ""
    echo "💡 提示: 使用 --interactive 参数启用交互式查询模式"
}

# 运行主函数
main "$@"
