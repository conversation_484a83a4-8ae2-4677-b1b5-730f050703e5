#!/usr/bin/env python3
"""
Playwright安装脚本

本脚本用于安装和配置Playwright，包括Python包和浏览器二进制文件。
适用于TextUp项目的知乎Playwright适配器。

使用方法:
    python install_playwright.py
    python install_playwright.py --browsers chromium firefox
    python install_playwright.py --check-only

依赖要求:
    - Python 3.8+
    - pip
"""

import sys
import subprocess
import os
import argparse
from pathlib import Path
from typing import List, Optional


class PlaywrightInstaller:
    """Playwright安装器"""

    def __init__(self):
        self.python_executable = sys.executable
        self.supported_browsers = ['chromium', 'firefox', 'webkit']

    def check_python_version(self) -> bool:
        """检查Python版本"""
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print(f"❌ Python版本过低: {version.major}.{version.minor}")
            print("   需要Python 3.8或更高版本")
            return False

        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True

    def check_pip_available(self) -> bool:
        """检查pip是否可用"""
        try:
            result = subprocess.run(
                [self.python_executable, '-m', 'pip', '--version'],
                capture_output=True,
                text=True,
                check=True
            )
            print(f"✅ pip可用: {result.stdout.strip()}")
            return True
        except subprocess.CalledProcessError:
            print("❌ pip不可用")
            return False

    def install_playwright_package(self) -> bool:
        """安装Playwright Python包"""
        try:
            print("📦 安装Playwright Python包...")

            # 安装核心包
            packages = [
                'playwright>=1.40.0',
                'beautifulsoup4>=4.12.0',
                'markdown2>=2.4.0'
            ]

            for package in packages:
                print(f"   安装 {package}")
                result = subprocess.run(
                    [self.python_executable, '-m', 'pip', 'install', package],
                    capture_output=True,
                    text=True
                )

                if result.returncode != 0:
                    print(f"❌ 安装失败 {package}")
                    print(f"   错误: {result.stderr}")
                    return False

            print("✅ Playwright Python包安装成功")
            return True

        except Exception as e:
            print(f"❌ 安装过程出错: {e}")
            return False

    def install_browsers(self, browsers: Optional[List[str]] = None) -> bool:
        """安装浏览器"""
        if browsers is None:
            browsers = ['chromium']  # 默认只安装chromium，因为知乎适配器主要用它

        # 验证浏览器名称
        invalid_browsers = [b for b in browsers if b not in self.supported_browsers]
        if invalid_browsers:
            print(f"❌ 不支持的浏览器: {', '.join(invalid_browsers)}")
            print(f"   支持的浏览器: {', '.join(self.supported_browsers)}")
            return False

        try:
            print(f"🌐 安装浏览器: {', '.join(browsers)}")

            # 安装指定浏览器
            cmd = [self.python_executable, '-m', 'playwright', 'install'] + browsers

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True
            )

            if result.returncode != 0:
                print("❌ 浏览器安装失败")
                print(f"   错误: {result.stderr}")
                return False

            print("✅ 浏览器安装成功")

            # 安装系统依赖（Linux）
            if sys.platform.startswith('linux'):
                print("🔧 安装系统依赖...")
                deps_result = subprocess.run(
                    [self.python_executable, '-m', 'playwright', 'install-deps'],
                    capture_output=True,
                    text=True
                )

                if deps_result.returncode == 0:
                    print("✅ 系统依赖安装成功")
                else:
                    print("⚠️  系统依赖安装可能有问题，但不影响基本使用")

            return True

        except Exception as e:
            print(f"❌ 浏览器安装出错: {e}")
            return False

    def verify_installation(self) -> bool:
        """验证安装"""
        try:
            print("🔍 验证Playwright安装...")

            # 检查Python包
            result = subprocess.run(
                [self.python_executable, '-c', 'import playwright; print("✅ Playwright包导入成功")'],
                capture_output=True,
                text=True
            )

            if result.returncode != 0:
                print("❌ Playwright包导入失败")
                print(f"   错误: {result.stderr}")
                return False

            print(result.stdout.strip())

            # 检查浏览器
            browser_check = subprocess.run(
                [self.python_executable, '-m', 'playwright', '--help'],
                capture_output=True,
                text=True
            )

            if browser_check.returncode == 0:
                print("✅ Playwright命令行工具可用")
            else:
                print("⚠️  Playwright命令行工具可能有问题")

            return True

        except Exception as e:
            print(f"❌ 验证过程出错: {e}")
            return False

    def create_test_script(self) -> bool:
        """创建测试脚本"""
        try:
            test_script_content = '''#!/usr/bin/env python3
"""
Playwright安装测试脚本
"""

import asyncio
from playwright.async_api import async_playwright

async def test_playwright():
    """测试Playwright基本功能"""
    try:
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context()
            page = await context.new_page()

            await page.goto('https://www.baidu.com')
            title = await page.title()

            await browser.close()

            print(f"✅ 测试成功: 页面标题 = {title}")
            return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 运行Playwright测试...")
    success = asyncio.run(test_playwright())
    sys.exit(0 if success else 1)
'''

            test_file = Path("test_playwright.py")
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(test_script_content)

            print(f"✅ 创建测试脚本: {test_file}")
            print(f"   运行测试: python {test_file}")
            return True

        except Exception as e:
            print(f"❌ 创建测试脚本失败: {e}")
            return False

    def check_installation_only(self) -> bool:
        """仅检查安装状态"""
        print("🔍 检查Playwright安装状态...")

        # 检查Python包
        try:
            import playwright
            print(f"✅ Playwright包已安装: {playwright.__version__}")
        except ImportError:
            print("❌ Playwright包未安装")
            return False

        # 检查浏览器
        try:
            result = subprocess.run(
                [self.python_executable, '-m', 'playwright', '--version'],
                capture_output=True,
                text=True,
                check=True
            )
            print(f"✅ Playwright CLI: {result.stdout.strip()}")
        except subprocess.CalledProcessError:
            print("❌ Playwright CLI不可用")
            return False

        return True

    def install_full(self, browsers: Optional[List[str]] = None) -> bool:
        """完整安装流程"""
        print("🚀 开始Playwright完整安装流程")
        print("=" * 50)

        # 检查环境
        if not self.check_python_version():
            return False

        if not self.check_pip_available():
            return False

        # 安装Python包
        if not self.install_playwright_package():
            return False

        # 安装浏览器
        if not self.install_browsers(browsers):
            return False

        # 验证安装
        if not self.verify_installation():
            return False

        # 创建测试脚本
        self.create_test_script()

        print("\n" + "=" * 50)
        print("🎉 Playwright安装完成！")
        print("\n下一步:")
        print("1. 运行测试脚本: python test_playwright.py")
        print("2. 查看知乎适配器文档: docs/examples/zhihu_playwright_example.py")
        print("3. 开始使用TextUp进行知乎文章发布")

        return True


def main():
    parser = argparse.ArgumentParser(
        description="Playwright安装脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
    python install_playwright.py                    # 完整安装（默认chromium）
    python install_playwright.py --browsers chromium firefox  # 安装多个浏览器
    python install_playwright.py --check-only       # 仅检查安装状态
        """
    )

    parser.add_argument(
        '--browsers',
        nargs='+',
        choices=['chromium', 'firefox', 'webkit'],
        default=['chromium'],
        help='要安装的浏览器 (默认: chromium)'
    )

    parser.add_argument(
        '--check-only',
        action='store_true',
        help='仅检查安装状态，不执行安装'
    )

    args = parser.parse_args()

    installer = PlaywrightInstaller()

    try:
        if args.check_only:
            success = installer.check_installation_only()
        else:
            success = installer.install_full(args.browsers)

        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        print("\n❌ 安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装过程发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
